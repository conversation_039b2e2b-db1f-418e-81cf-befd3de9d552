export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      assets: {
        Row: {
          account_details: string | null
          acquisition_date: string | null
          category: string
          created_at: string
          currency: string | null
          description: string | null
          id: string
          image_url: string | null
          location: string | null
          name: string
          notes: string | null
          type: string
          updated_at: string
          user_id: string
          value: number | null
        }
        Insert: {
          account_details?: string | null
          acquisition_date?: string | null
          category: string
          created_at?: string
          currency?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          location?: string | null
          name: string
          notes?: string | null
          type: string
          updated_at?: string
          user_id: string
          value?: number | null
        }
        Update: {
          account_details?: string | null
          acquisition_date?: string | null
          category?: string
          created_at?: string
          currency?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          location?: string | null
          name?: string
          notes?: string | null
          type?: string
          updated_at?: string
          user_id?: string
          value?: number | null
        }
        Relationships: []
      }
      auth_users_mapping: {
        Row: {
          auth_user_id: string
          created_at: string
          custom_user_id: string
          id: string
        }
        Insert: {
          auth_user_id: string
          created_at?: string
          custom_user_id: string
          id?: string
        }
        Update: {
          auth_user_id?: string
          created_at?: string
          custom_user_id?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "auth_users_mapping_custom_user_id_fkey"
            columns: ["custom_user_id"]
            isOneToOne: true
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      contacts: {
        Row: {
          address: string | null
          country_code: string | null
          created_at: string
          email: string | null
          first_name: string
          has_personal_message: boolean | null
          id: string
          last_name: string
          notes: string | null
          personal_message: string | null
          phone: string | null
          relationship: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          address?: string | null
          country_code?: string | null
          created_at?: string
          email?: string | null
          first_name: string
          has_personal_message?: boolean | null
          id?: string
          last_name: string
          notes?: string | null
          personal_message?: string | null
          phone?: string | null
          relationship?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string | null
          country_code?: string | null
          created_at?: string
          email?: string | null
          first_name?: string
          has_personal_message?: boolean | null
          id?: string
          last_name?: string
          notes?: string | null
          personal_message?: string | null
          phone?: string | null
          relationship?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "contacts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      custom_users: {
        Row: {
          created_at: string | null
          email: string
          email_verified: boolean | null
          first_name: string
          id: string
          last_name: string
          password_hash: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          email_verified?: boolean | null
          first_name: string
          id?: string
          last_name: string
          password_hash: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          email_verified?: boolean | null
          first_name?: string
          id?: string
          last_name?: string
          password_hash?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      death_notifications: {
        Row: {
          created_at: string
          death_certificate: string | null
          id: string
          reported_at: string
          reported_by: string
          status: string
          updated_at: string
          user_id: string
          verification_details: string | null
          verification_method: string | null
          verified_at: string | null
        }
        Insert: {
          created_at?: string
          death_certificate?: string | null
          id?: string
          reported_at: string
          reported_by: string
          status: string
          updated_at?: string
          user_id: string
          verification_details?: string | null
          verification_method?: string | null
          verified_at?: string | null
        }
        Update: {
          created_at?: string
          death_certificate?: string | null
          id?: string
          reported_at?: string
          reported_by?: string
          status?: string
          updated_at?: string
          user_id?: string
          verification_details?: string | null
          verification_method?: string | null
          verified_at?: string | null
        }
        Relationships: []
      }
      last_wishes: {
        Row: {
          burial_option: string | null
          burial_wishes: string | null
          created_at: string
          funeral_wishes: string | null
          has_pets: boolean
          id: string
          is_organ_donor: boolean
          organ_donor_country: string | null
          organ_donor_state: string | null
          other_wishes: string | null
          personal_messages: string | null
          pet_care_instructions: string | null
          show_personal_messages: boolean | null
          updated_at: string
          user_id: string
        }
        Insert: {
          burial_option?: string | null
          burial_wishes?: string | null
          created_at?: string
          funeral_wishes?: string | null
          has_pets?: boolean
          id?: string
          is_organ_donor?: boolean
          organ_donor_country?: string | null
          organ_donor_state?: string | null
          other_wishes?: string | null
          personal_messages?: string | null
          pet_care_instructions?: string | null
          show_personal_messages?: boolean | null
          updated_at?: string
          user_id: string
        }
        Update: {
          burial_option?: string | null
          burial_wishes?: string | null
          created_at?: string
          funeral_wishes?: string | null
          has_pets?: boolean
          id?: string
          is_organ_donor?: boolean
          organ_donor_country?: string | null
          organ_donor_state?: string | null
          other_wishes?: string | null
          personal_messages?: string | null
          pet_care_instructions?: string | null
          show_personal_messages?: boolean | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "last_wishes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string | null
          first_name: string | null
          id: string
          last_name: string | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          subscription_end_date: string | null
          subscription_start_date: string | null
          subscription_status: string
          subscription_tier: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          id: string
          last_name?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          subscription_tier?: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          id?: string
          last_name?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          subscription_tier?: string
          updated_at?: string
        }
        Relationships: []
      }
      service_sunset: {
        Row: {
          account_number: string | null
          auto_renewal: boolean
          cancellation_instructions: string | null
          category: string
          contact_info: string | null
          created_at: string
          description: string | null
          id: string
          name: string
          renewal_date: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          account_number?: string | null
          auto_renewal?: boolean
          cancellation_instructions?: string | null
          category: string
          contact_info?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name: string
          renewal_date?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          account_number?: string | null
          auto_renewal?: boolean
          cancellation_instructions?: string | null
          category?: string
          contact_info?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          renewal_date?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      time_capsule_media: {
        Row: {
          capsule_id: string
          created_at: string
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          id: string
        }
        Insert: {
          capsule_id: string
          created_at?: string
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          id?: string
        }
        Update: {
          capsule_id?: string
          created_at?: string
          file_name?: string
          file_path?: string
          file_size?: number
          file_type?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_capsule_media_capsule_id_fkey"
            columns: ["capsule_id"]
            isOneToOne: false
            referencedRelation: "time_capsules"
            referencedColumns: ["id"]
          },
        ]
      }
      time_capsules: {
        Row: {
          created_at: string
          delivery_date: string
          id: string
          message: string | null
          recipient_email: string
          recipient_first_name: string | null
          recipient_last_name: string | null
          recipient_name: string | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          delivery_date: string
          id?: string
          message?: string | null
          recipient_email: string
          recipient_first_name?: string | null
          recipient_last_name?: string | null
          recipient_name?: string | null
          status: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          delivery_date?: string
          id?: string
          message?: string | null
          recipient_email?: string
          recipient_first_name?: string | null
          recipient_last_name?: string | null
          recipient_name?: string | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_capsules_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      trustees: {
        Row: {
          created_at: string
          first_name: string
          id: string
          invitation_accepted_at: string | null
          invitation_sent_at: string | null
          last_name: string
          permissions: string[]
          relationship: string | null
          status: string
          trustee_email: string
          trustee_user_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          first_name: string
          id?: string
          invitation_accepted_at?: string | null
          invitation_sent_at?: string | null
          last_name: string
          permissions: string[]
          relationship?: string | null
          status: string
          trustee_email: string
          trustee_user_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          first_name?: string
          id?: string
          invitation_accepted_at?: string | null
          invitation_sent_at?: string | null
          last_name?: string
          permissions?: string[]
          relationship?: string | null
          status?: string
          trustee_email?: string
          trustee_user_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_sessions: {
        Row: {
          created_at: string | null
          expires_at: string
          id: string
          session_token: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at: string
          id?: string
          session_token: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string
          id?: string
          session_token?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "custom_users"
            referencedColumns: ["id"]
          },
        ]
      }
      vault_documents: {
        Row: {
          category: string
          created_at: string
          description: string | null
          encryption_key: string | null
          file_name: string | null
          file_path: string
          file_size: number | null
          file_type: string | null
          id: string
          is_encrypted: boolean
          name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          created_at?: string
          description?: string | null
          encryption_key?: string | null
          file_name?: string | null
          file_path: string
          file_size?: number | null
          file_type?: string | null
          id?: string
          is_encrypted?: boolean
          name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          encryption_key?: string | null
          file_name?: string | null
          file_path?: string
          file_size?: number | null
          file_type?: string | null
          id?: string
          is_encrypted?: boolean
          name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      verification_codes: {
        Row: {
          code: string
          created_at: string | null
          email: string
          expires_at: string
          id: string
          used: boolean | null
        }
        Insert: {
          code: string
          created_at?: string | null
          email: string
          expires_at: string
          id?: string
          used?: boolean | null
        }
        Update: {
          code?: string
          created_at?: string | null
          email?: string
          expires_at?: string
          id?: string
          used?: boolean | null
        }
        Relationships: []
      }
      will_progress: {
        Row: {
          assets_completed: boolean
          basic_info_completed: boolean
          created_at: string
          executor_completed: boolean
          final_review_completed: boolean
          id: string
          progress_percentage: number
          updated_at: string
          user_id: string
        }
        Insert: {
          assets_completed?: boolean
          basic_info_completed?: boolean
          created_at?: string
          executor_completed?: boolean
          final_review_completed?: boolean
          id?: string
          progress_percentage?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          assets_completed?: boolean
          basic_info_completed?: boolean
          created_at?: string
          executor_completed?: boolean
          final_review_completed?: boolean
          id?: string
          progress_percentage?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
