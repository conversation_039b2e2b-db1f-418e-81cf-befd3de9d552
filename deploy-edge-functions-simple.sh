#!/bin/bash

# Make sure the script exits on any error
set -e

echo "Deploying Supabase Edge Functions..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI not found. Installing..."
    npm install -g supabase
fi

# Login to Supabase (this will open a browser window)
echo "Logging in to Supabase..."
supabase login

# Link to the project
echo "Linking to Supabase project..."
supabase link --project-ref ccwvtcudztphwwzzgwvg

# Deploy each function individually
echo "Deploying create-checkout function..."
supabase functions deploy create-checkout

echo "Deploying check-subscription function..."
supabase functions deploy check-subscription

echo "Deploying estate-recommendations function..."
supabase functions deploy estate-recommendations

echo "Deploying deliver-time-capsule function..."
supabase functions deploy deliver-time-capsule

echo "Deploying send-trustee-invitation function..."
supabase functions deploy send-trustee-invitation

echo "Deploying send-email-hook function..."
supabase functions deploy send-email-hook

# Set the necessary secrets (using environment variables instead of hardcoded values)
echo "Setting up secrets..."
if [ -n "$STRIPE_SECRET_KEY" ]; then
    supabase secrets set STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY"
else
    echo "Warning: STRIPE_SECRET_KEY environment variable not set. Skipping secret setup."
fi

echo "Edge Functions deployed successfully!"
