# Legalock - Legacy Guardian

Legalock is a digital legacy management platform that helps users organize, protect, and pass on their digital assets and final wishes to their loved ones.

## Features

- Digital Asset Inventory: Create a comprehensive catalog of your digital accounts, subscriptions, and assets with secure access instructions.
- Secure Document Vault: Store important documents like wills, insurance policies, and passwords in an encrypted vault accessible only to designated trustees.
- Time Capsule Messages: Create messages to be delivered to loved ones at future dates or upon specific events.
- Trustee Management: Designate and manage trustees who will have access to your digital legacy.

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/samythel/legalock-legacy-guardian.git
   cd legalock-legacy-guardian
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   Copy the `.env.example` file to `.env.local` and update the values as needed:
   ```bash
   cp .env.example .env.local
   ```

   The following environment variables are required for local development:
   - `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

   Additional variables for full functionality:
   - `SUPABASE_SERVICE_ROLE_KEY`: For Supabase Edge Functions
   - `RESEND_API_KEY`: For email delivery
   - `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`, `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`: For payment processing
   - `DEEPSEEK_API_KEY`: For AI-powered estate recommendations

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Project Structure

- `src/app`: Next.js App Router pages and layouts
- `src/components`: Reusable UI components
- `src/context`: React Context providers
- `src/lib`: Utility functions and libraries
- `src/hooks`: Custom React hooks
- `src/types`: TypeScript type definitions

## Tech Stack

This project is built with:

- **Framework**: Next.js 14
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication**: Supabase Auth
- **Database**: Supabase PostgreSQL
- **State Management**: React Context API, React Query
- **Form Handling**: React Hook Form, Zod


## Deployment

### Deploying to Vercel

1. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy the project:
   ```bash
   vercel
   ```

4. For production deployment:
   ```bash
   vercel --prod
   ```

### Environment Variables

When deploying to Vercel, you'll need to set up the environment variables in the Vercel dashboard:

1. Go to your project in the Vercel dashboard
2. Navigate to Settings > Environment Variables
3. Add all the variables from your `.env.example` file with their appropriate values

### Custom Domain

To connect a custom domain to your Vercel deployment:

1. Go to your project in the Vercel dashboard
2. Navigate to Settings > Domains
3. Add your domain and follow the verification steps

## License

This project is licensed under the MIT License - see the LICENSE file for details.
