"use strict";exports.id=4272,exports.ids=[4272],exports.modules={32933:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(62881).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},30361:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},90434:(e,t,n)=>{n.d(t,{default:()=>o.a});var r=n(79404),o=n.n(r)},46074:(e,t,n)=>{n.d(t,{z$:()=>k,fC:()=>E});var r=n(17577),o=n(48051),i=n(10326);function u(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}var s=globalThis?.document?r.useLayoutEffect:()=>{},l=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef({}),a=r.useRef(e),l=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=c(u.current);l.current="mounted"===d?e:"none"},[d]),s(()=>{let t=u.current,n=a.current;if(n!==e){let r=l.current,o=c(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),s(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=c(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!a.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(l.current=c(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),i(e)},[])}}(t),u="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),a=(0,o.e)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||i.isPresent?r.cloneElement(u,{ref:a}):null};function c(e){return e?.animationName||"none"}l.displayName="Presence";var d=n(45226),f="Checkbox",[p,m]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let u=r.createContext(o),a=n.length;n=[...n,o];let s=t=>{let{scope:n,children:o,...s}=t,l=n?.[e]?.[a]||u,c=r.useMemo(()=>s,Object.values(s));return(0,i.jsx)(l.Provider,{value:c,children:o})};return s.displayName=t+"Provider",[s,function(n,i){let s=i?.[e]?.[a]||u,l=r.useContext(s);if(l)return l;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(f),[v,y]=p(f),h=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:s,checked:l,defaultChecked:c,required:f,disabled:p,value:m="on",onCheckedChange:y,form:h,...N}=e,[b,E]=r.useState(null),k=(0,o.e)(t,e=>E(e)),M=r.useRef(!1),O=!b||h||!!b.closest("form"),[R=!1,C]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,i]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,i=r.useRef(o),u=a(t);return r.useEffect(()=>{i.current!==o&&(u(o),i.current=o)},[o,i,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o,l=a(n);return[s,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else i(t)},[u,e,i,l])]}({prop:l,defaultProp:c,onChange:y}),T=r.useRef(R);return r.useEffect(()=>{let e=b?.form;if(e){let t=()=>C(T.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,C]),(0,i.jsxs)(v,{scope:n,state:R,disabled:p,children:[(0,i.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":w(R)?"mixed":R,"aria-required":f,"data-state":x(R),"data-disabled":p?"":void 0,disabled:p,value:m,...N,ref:k,onKeyDown:u(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:u(e.onClick,e=>{C(e=>!!w(e)||!e),O&&(M.current=e.isPropagationStopped(),M.current||e.stopPropagation())})}),O&&(0,i.jsx)(g,{control:b,bubbles:!M.current,name:s,value:m,checked:R,required:f,disabled:p,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!w(c)&&c})]})});h.displayName=f;var N="CheckboxIndicator",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,u=y(N,n);return(0,i.jsx)(l,{present:r||w(u.state)||!0===u.state,children:(0,i.jsx)(d.WV.span,{"data-state":x(u.state),"data-disabled":u.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=N;var g=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:u,...a}=e,l=r.useRef(null),c=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n),d=function(e){let[t,n]=r.useState(void 0);return s(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(t);r.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=w(n),t.call(e,!w(n)&&n),e.dispatchEvent(r)}},[c,n,o]);let f=r.useRef(!w(n)&&n);return(0,i.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:u??f.current,...a,tabIndex:-1,ref:l,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return"indeterminate"===e}function x(e){return w(e)?"indeterminate":e?"checked":"unchecked"}var E=h,k=b},45226:(e,t,n)=>{n.d(t,{WV:()=>u});var r=n(17577);n(60962);var o=n(34214),i=n(10326),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,o.Z8)(`Primitive.${t}`),u=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a,{...u,ref:r})});return u.displayName=`Primitive.${t}`,{...e,[t]:u}},{})}};