"use strict";exports.id=9322,exports.ids=[9322],exports.modules={71615:(e,t,r)=>{var n=r(88757);r.o(n,"cookies")&&r.d(t,{cookies:function(){return n.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return i}});let n=r(45869),s=r(6278);class i{get isEnabled(){return this._provider.isEnabled}enable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=n.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return h},draftMode:function(){return f},headers:function(){return u}});let n=r(68996),s=r(53047),i=r(92044),o=r(72934),a=r(33085),l=r(6278),d=r(45869),c=r(54580);function u(){let e="headers",t=d.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,c.getExpectedRequestStore)(e).headers}function h(){let e="cookies",t=d.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.RequestCookiesAdapter.seal(new i.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,c.getExpectedRequestStore)(e),s=o.actionAsyncStorage.getStore();return(null==s?void 0:s.isAction)||(null==s?void 0:s.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,c.getExpectedRequestStore)("draftMode");return new a.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return s}});let n=r(38238);class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,s);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,s)},set(t,r,s,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,s,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,a??r,s,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return u},ReadonlyRequestCookiesError:function(){return o},RequestCookiesAdapter:function(){return a},appendMutableCookies:function(){return c},getModifiedCookieValues:function(){return d}});let n=r(92044),s=r(38238),i=r(45869);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new o}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function d(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function c(e,t){let r=d(t);if(0===r.length)return!1;let s=new n.ResponseCookies(e),i=s.getAll();for(let e of r)s.set(e);for(let e of i)s.set(e);return!0}class u{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],a=new Set,d=()=>{let e=i.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return o;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{d()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{d()}};default:return s.ReflectAdapter.get(e,t,r)}}})}}},2723:(e,t,r)=>{r.d(t,{R:()=>_});var n=Object.defineProperty,s=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,d=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&d(e,r,t[r]);if(o)for(var r of o(t))l.call(t,r)&&d(e,r,t[r]);return e},u=(e,t)=>s(e,i(t)),h=(e,t,r)=>new Promise((n,s)=>{var i=e=>{try{a(r.next(e))}catch(e){s(e)}},o=e=>{try{a(r.throw(e))}catch(e){s(e)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(i,o);a((r=r.apply(e,t)).next())}),f=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},y=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function p(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var m=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){let n=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}n.push(p(t))}return yield this.resend.post("/emails/batch",n,t)})}},b=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return h(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return h(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return h(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},g=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts/${e.id}`)})}update(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},v=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return h(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},w=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",p(e),t)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return h(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},A="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",k="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.2.0",_=class{constructor(e){if(this.key=e,this.apiKeys=new f(this),this.audiences=new y(this),this.batch=new m(this),this.broadcasts=new b(this),this.contacts=new g(this),this.domains=new v(this),this.emails=new w(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":k,"Content-Type":"application/json"})}fetchRequest(e){return h(this,arguments,function*(e,t={}){try{let r=yield fetch(`${A}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:u(c({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return h(this,arguments,function*(e,t,r={}){let n=c({method:"POST",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}get(e){return h(this,arguments,function*(e,t={}){let r=c({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return h(this,arguments,function*(e,t,r={}){let n=c({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}patch(e,t){return h(this,arguments,function*(e,t,r={}){let n=c({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}delete(e,t){return h(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}}};