"use strict";exports.id=958,exports.ids=[958],exports.modules={20344:(e,t,r)=>{var o,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>x,createClientComponentClient:()=>u,createMiddlewareClient:()=>v,createMiddlewareSupabaseClient:()=>_,createPagesBrowserClient:()=>d,createPagesServerClient:()=>g,createRouteHandlerClient:()=>O,createServerActionClient:()=>A,createServerComponentClient:()=>w,createServerSupabaseClient:()=>j}),e.exports=((e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))a.call(e,r)||void 0===r||i(e,r,{get:()=>t[r],enumerable:!(o=s(t,r))||o.enumerable});return e})(i({},"__esModule",{value:!0}),l);var c=r(26228);function u({supabaseUrl:e="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:r,cookieOptions:i,isSingleton:s=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let n=()=>{var o;return(0,c.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(o=null==r?void 0:r.global)?void 0:o.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new c.BrowserCookieAuthStorageAdapter(i)}})};if(s){let e=o??n();return"undefined"==typeof window?e:(o||(o=e),o)}return n()}var d=u,p=r(26228),f=r(82338),h=class extends p.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,o;return(0,f.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,p.parseCookies)(t)[e]).find(e=>!!e)??(null==(o=this.context.req)?void 0:o.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var o;let i=(0,f.splitCookiesString)((null==(o=this.context.res.getHeader("set-cookie"))?void 0:o.toString())??"").filter(t=>!(e in(0,p.parseCookies)(t))),s=(0,p.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...i,s])}};function g(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,p.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new h(e,i)}})}var C=r(26228),y=r(82338),k=class extends C.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return(0,y.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,C.parseCookies)(t)[e]).find(e=>!!e)||(0,C.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let o=(0,C.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",o)}};function v(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,C.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new k(e,i)}})}var m=r(26228),b=class extends m.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function w(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,m.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new b(e,i)}})}var I=r(26228),S=class extends I.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function O(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,I.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new S(e,i)}})}var A=O;function x({supabaseUrl:e="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:r,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),d({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:o})}function j(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),g(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}function _(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),v(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}},71615:(e,t,r)=>{var o=r(88757);r.o(o,"cookies")&&r.d(t,{cookies:function(){return o.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return s}});let o=r(45869),i=r(6278);class s{get isEnabled(){return this._provider.isEnabled}enable(){let e=o.staticGenerationAsyncStorage.getStore();return e&&(0,i.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=o.staticGenerationAsyncStorage.getStore();return e&&(0,i.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return f},headers:function(){return d}});let o=r(68996),i=r(53047),s=r(92044),n=r(72934),a=r(33085),l=r(6278),c=r(45869),u=r(54580);function d(){let e="headers",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return i.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,u.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.RequestCookiesAdapter.seal(new s.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,u.getExpectedRequestStore)(e),i=n.actionAsyncStorage.getStore();return(null==i?void 0:i.isAction)||(null==i?void 0:i.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,u.getExpectedRequestStore)("draftMode");return new a.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return s},ReadonlyHeadersError:function(){return i}});let o=r(38238);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return o.ReflectAdapter.get(t,r,i);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==n)return o.ReflectAdapter.get(t,n,i)},set(t,r,i,s){if("symbol"==typeof r)return o.ReflectAdapter.set(t,r,i,s);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return o.ReflectAdapter.set(t,a??r,i,s)},has(t,r){if("symbol"==typeof r)return o.ReflectAdapter.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&o.ReflectAdapter.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return o.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||o.ReflectAdapter.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,o]of this.entries())e.call(t,o,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return n},RequestCookiesAdapter:function(){return a},appendMutableCookies:function(){return u},getModifiedCookieValues:function(){return c}});let o=r(92044),i=r(38238),s=r(45869);class n extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new n}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return n.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function c(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=c(t);if(0===r.length)return!1;let i=new o.ResponseCookies(e),s=i.getAll();for(let e of r)i.set(e);for(let e of s)i.set(e);return!0}class d{static wrap(e,t){let r=new o.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],a=new Set,c=()=>{let e=s.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of n){let r=new o.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return n;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{c()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{c()}};default:return i.ReflectAdapter.get(e,t,r)}}})}}},82338:e=>{var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function o(e,o){var i,s,n,a,l=e.split(";").filter(r),c=(i=l.shift(),s="",n="",(a=i.split("=")).length>1?(s=a.shift(),n=a.join("=")):n=i,{name:s,value:n}),u=c.name,d=c.value;o=o?Object.assign({},t,o):t;try{d=o.decodeValues?decodeURIComponent(d):d}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+d+"'. Set options.decodeValues to false to disable this feature.",e)}var p={name:u,value:d};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),o=t.join("=");"expires"===r?p.expires=new Date(o):"max-age"===r?p.maxAge=parseInt(o,10):"secure"===r?p.secure=!0:"httponly"===r?p.httpOnly=!0:"samesite"===r?p.sameSite=o:"partitioned"===r?p.partitioned=!0:p[r]=o}),p}function i(e,i){if(i=i?Object.assign({},t,i):t,!e)return i.map?{}:[];if(e.headers){if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var s=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];s||!e.headers.cookie||i.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=s}}return(Array.isArray(e)||(e=[e]),i.map)?e.filter(r).reduce(function(e,t){var r=o(t,i);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return o(e,i)})}e.exports=i,e.exports.parse=i,e.exports.parseString=o,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,o,i,s,n=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;l();)if(","===(r=e.charAt(a))){for(o=a,a+=1,l(),i=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=i,n.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!s||a>=e.length)&&n.push(e.substring(t,e.length))}return n}},26228:(e,t,r)=>{let o,i;r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>A,CookieAuthStorageAdapter:()=>O,DEFAULT_COOKIE_OPTIONS:()=>I,createSupabaseClient:()=>x,isBrowser:()=>w,parseCookies:()=>j,parseSupabaseCookie:()=>m,serializeCookie:()=>_,stringifySupabaseSession:()=>b});var s=r(78893);new TextEncoder;let n=new TextDecoder;s.Buffer.isEncoding("base64url");let a=e=>s.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=n.decode(t)),t}(e),"base64");var l=r(31518),c=Object.create,u=Object.defineProperty,d=Object.getOwnPropertyDescriptor,p=Object.getOwnPropertyNames,f=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,g=(e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of p(t))h.call(e,i)||i===r||u(e,i,{get:()=>t[i],enumerable:!(o=d(t,i))||o.enumerable});return e},C=(e,t,r)=>(r=null!=e?c(f(e)):{},g(!t&&e&&e.__esModule?r:u(r,"default",{value:e,enumerable:!0}),e)),y=(o={"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},i=(t||{}).decode||o,s=0;s<e.length;){var n=e.indexOf("=",s);if(-1===n)break;var a=e.indexOf(";",s);if(-1===a)a=e.length;else if(a<n){s=e.lastIndexOf(";",n-1)+1;continue}var l=e.slice(s,n).trim();if(void 0===r[l]){var c=e.slice(n+1,a).trim();34===c.charCodeAt(0)&&(c=c.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(c,i)}s=a+1}return r},e.serialize=function(e,o,s){var n=s||{},a=n.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(o);if(l&&!r.test(l))throw TypeError("argument val is invalid");var c=e+"="+l;if(null!=n.maxAge){var u=n.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(u)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");c+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");c+="; Path="+n.path}if(n.expires){var d=n.expires;if("[object Date]"!==t.call(d)&&!(d instanceof Date)||isNaN(d.valueOf()))throw TypeError("option expires is invalid");c+="; Expires="+d.toUTCString()}if(n.httpOnly&&(c+="; HttpOnly"),n.secure&&(c+="; Secure"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function i(e){return encodeURIComponent(e)}}},function(){return i||(0,o[p(o)[0]])((i={exports:{}}).exports,i),i.exports}),k=C(y()),v=C(y());function m(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,o,i]=t[0].split("."),s=a(o),n=new TextDecoder,{exp:l,sub:c,...u}=JSON.parse(n.decode(s));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:c,factors:t[4],...u}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function b(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function w(){return"undefined"!=typeof window&&void 0!==window.document}var I={path:"/",sameSite:"lax",maxAge:31536e6},S=RegExp(".{1,3180}","g"),O=class{constructor(e){this.cookieOptions={...I,...e,maxAge:I.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(m(t));let r=function(e,t=()=>null){let r=[];for(let o=0;;o++){let i=t(`${e}.${o}`);if(!i)break;r.push(i)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(m(r)):null}setItem(e,t){if(e.endsWith("-code-verifier")){this.setCookie(e,t);return}(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let o=[],i=t.match(S);return null==i||i.forEach((t,r)=>{let i=`${e}.${r}`;o.push({name:i,value:t})}),o})(e,b(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},A=class extends O{constructor(e){super(e)}getCookie(e){return w()?(0,k.parse)(document.cookie)[e]:null}setCookie(e,t){if(!w())return null;document.cookie=(0,k.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!w())return null;document.cookie=(0,k.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function x(e,t,r){var o;let i=w();return(0,l.eI)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:i,detectSessionInUrl:i,persistSession:!0,storage:r.auth.storage,...(null==(o=r.auth)?void 0:o.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var j=v.parse,_=v.serialize}};