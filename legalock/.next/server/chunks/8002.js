exports.id=8002,exports.ids=[8002],exports.modules={48773:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},3110:(e,r,t)=>{Promise.resolve().then(t.bind(t,11012)),Promise.resolve().then(t.bind(t,31062))},11012:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>u});var o=t(10326),n=t(2659),i=t(44976),a=t(68136),s=t(87423),c=t(17577);function u({children:e}){let[r]=(0,c.useState)(()=>new n.S);return o.jsx(i.aH,{client:r,children:o.jsx(a.H,{children:o.jsx(s.q,{children:e})})})}},31062:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>i});var o=t(10326);t(17577);var n=t(14831);function i({children:e,...r}){return o.jsx(n.f,{...r,children:e})}},68136:(e,r,t)=>{"use strict";t.d(r,{H:()=>c,a:()=>u});var o=t(10326),n=t(17577),i=t(35047),a=t(85999);let s=(0,n.createContext)(void 0),c=({children:e})=>{let[r,t]=(0,n.useState)(null),[c,u]=(0,n.useState)(!0),l=(0,i.useRouter)();(0,n.useEffect)(()=>{(async()=>{try{u(!0);let e=await fetch("/api/auth/session"),r=await e.json();r.authenticated&&r.user?t(r.user):t(null)}catch(e){console.error("Error getting session:",e),t(null)}finally{u(!1)}})()},[]);let d=async(e,r)=>{try{u(!0);let o=await fetch("/api/auth/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),n=await o.json();if(!o.ok){401===o.status?a.Am.error("Invalid email or password. Please try again."):403===o.status&&n.requiresVerification?(a.Am.info("Please verify your email to continue."),l.push(`/verify?email=${encodeURIComponent(e)}`)):429===o.status?a.Am.error("Too many login attempts. Please try again in a few minutes."):a.Am.error(n.error||"An error occurred during sign in.");return}t(n.user),a.Am.success("Signed in successfully!"),l.push("/dashboard")}catch(e){console.error("Sign in error:",e),a.Am.error("An error occurred during sign in. Please try again.")}finally{u(!1)}},h=async(e,r,o,n,i=!1,s)=>{try{u(!0);let c=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r,firstName:o,lastName:n,autoVerify:i,trusteeInvitationId:s})}),d=await c.json();if(!c.ok){409===c.status?a.Am.error("This email is already registered. Please sign in instead."):429===c.status?a.Am.error("Too many sign-up attempts. Please wait a few minutes before trying again."):a.Am.error(d.error||"An error occurred during sign up.");return}if(i&&d.user){t(d.user),a.Am.success("Account created successfully!"),l.push("/dashboard");return}a.Am.success("Account created! Please enter the verification code sent to your email."),l.push(`/verify?email=${encodeURIComponent(e)}`)}catch(e){console.error("Sign up error:",e),a.Am.error("An error occurred during sign up. Please try again.")}finally{u(!1)}},m=async()=>{try{u(!0);let e=await fetch("/api/auth/signout",{method:"POST"});if(!e.ok){let r=await e.json();a.Am.error(r.error||"An error occurred during sign out.");return}t(null),a.Am.info("Signed out successfully."),l.push("/")}catch(e){console.error("Sign out error:",e),a.Am.error("An error occurred during sign out. Please try again.")}finally{u(!1)}},y=async(e,r)=>{try{u(!0);let o=await fetch("/api/auth/verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,code:r})}),n=await o.json();if(!o.ok){400===o.status?a.Am.error("Invalid or expired verification code. Please try again."):a.Am.error(n.error||"An error occurred during verification.");return}t(n.user),a.Am.success("Email verified successfully!"),l.push("/dashboard")}catch(e){console.error("Verification error:",e),a.Am.error("An error occurred during verification. Please try again.")}finally{u(!1)}},f=async e=>{try{u(!0);let r=await fetch("/api/auth/resend-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),t=await r.json();if(!r.ok){429===r.status?a.Am.error("Too many attempts. Please wait a few minutes before trying again."):a.Am.error(t.error||"Failed to resend verification code.");return}a.Am.success("Verification code sent! Please check your email.")}catch(e){console.error("Resend verification error:",e),a.Am.error("An error occurred while resending the verification code. Please try again.")}finally{u(!1)}},p=async(e,r)=>{try{u(!0);let o=await fetch("/api/auth/update-profile",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:e,lastName:r})}),n=await o.json();if(!o.ok){a.Am.error(n.error||"An error occurred while updating your profile.");return}t(t=>t?{...t,firstName:e,lastName:r}:null),a.Am.success("Profile updated successfully!")}catch(e){console.error("Update profile error:",e),a.Am.error("An error occurred while updating your profile. Please try again.")}finally{u(!1)}},g=async e=>{try{u(!0);let r=await fetch("/api/auth/update-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),t=await r.json();if(!r.ok){409===r.status?a.Am.error("This email is already in use by another account."):a.Am.error(t.error||"An error occurred while updating your email.");return}a.Am.success("Verification code sent! Please verify your new email."),l.push(`/verify?email=${encodeURIComponent(e)}`)}catch(e){console.error("Update email error:",e),a.Am.error("An error occurred while updating your email. Please try again.")}finally{u(!1)}},v=async e=>{try{u(!0);let r=await fetch("/api/auth/update-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})}),t=await r.json();if(!r.ok){a.Am.error(t.error||"An error occurred while updating your password.");return}a.Am.success("Password updated successfully!")}catch(e){console.error("Update password error:",e),a.Am.error("An error occurred while updating your password. Please try again.")}finally{u(!1)}};return o.jsx(s.Provider,{value:{user:r,loading:c,signIn:d,signUp:h,signOut:m,verifyOtp:y,updateProfile:p,updateEmail:g,updatePassword:v,resendVerificationCode:f},children:e})},u=()=>{let e=(0,n.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},87423:(e,r,t)=>{"use strict";t.d(r,{m:()=>c,q:()=>u});var o=t(10326),n=t(17577),i=t(68136),a=t(85999);let s=(0,n.createContext)(void 0),c=()=>{let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSubscription must be used within a SubscriptionProvider");return e},u=({children:e})=>{let{user:r}=(0,i.a)(),[t,c]=(0,n.useState)("free"),[u,l]=(0,n.useState)(!0),[d,h]=(0,n.useState)(!1),[m,y]=(0,n.useState)(null),f=async()=>{if(!r){l(!1),c("free"),h(!1),y(null);return}try{l(!0),c("free"),h(!1),y(null)}catch(e){console.error("Error checking subscription:",e),c("free"),h(!1),y(null)}finally{l(!1)}},p=async e=>{if(!r)return a.Am.error("You must be logged in to subscribe"),null;try{let r=await fetch("/api/stripe/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({plan:e})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to create checkout session")}return(await r.json()).url}catch(e){return console.error("Error creating checkout session:",e),a.Am.error(e.message||"Failed to create checkout session"),null}};return(0,n.useEffect)(()=>{r?f():(c("free"),h(!1),y(null))},[r]),o.jsx(s.Provider,{value:{plan:t,isLoading:u,isSubscribed:d,subscriptionDetails:m,checkSubscription:f,createCheckout:p},children:e})}},56752:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>u});var o=t(19510),n=t(45317),i=t.n(n);t(5023);var a=t(68570);let s=(0,a.createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/components/theme-provider.tsx#ThemeProvider`),c=(0,a.createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/components/providers.tsx#Providers`);(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/sonner'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}();let u={title:"Legalock - Legacy Guardian",description:"Secure your digital legacy"};function l({children:e}){return o.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:o.jsx("body",{className:i().className,suppressHydrationWarning:!0,children:o.jsx(s,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:o.jsx(c,{children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/sonner'");throw e.code="MODULE_NOT_FOUND",e}()),{}),e]})})})})})}},5023:()=>{}};