"use strict";exports.id=5797,exports.ids=[5797,2777],exports.modules={28916:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(62881).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},77506:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(62881).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},88378:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(62881).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},24061:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35797:(e,t,r)=>{r.d(t,{Z:()=>c});var n=r(10326);r(17577);var o=r(35047),s=r(34793),i=r(68136),a=r(77506);!function(){var e=Error("Cannot find module '@/components/ui/sidebar'");throw e.code="MODULE_NOT_FOUND",e}();let c=({children:e})=>{let{loading:t}=(0,i.a)(),r=["/login","/register","/verify","/reset-password","/"].includes((0,o.usePathname)());return t&&!r?n.jsx("div",{className:"flex items-center justify-center min-h-screen",children:n.jsx(a.Z,{className:"h-8 w-8 animate-spin text-primary"})}):n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/sidebar'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("div",{className:"min-h-screen flex w-full",children:(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[n.jsx(s.Z,{}),n.jsx("main",{className:"flex-1 bg-gray-50",children:n.jsx("div",{className:"page-container",children:e})}),n.jsx("footer",{className:"py-4 px-6 border-t border-gray-200 text-center text-sm text-gray-500",children:(0,n.jsxs)("p",{children:["Legalock \xa9 ",new Date().getFullYear()," - Your digital legacy secured"]})})]})})})}},34793:(e,t,r)=>{r.d(t,{Z:()=>p});var n=r(10326),o=r(17577),s=r(90434),i=r(46226),a=r(24061),c=r(28916),l=r(88378),d=r(71810),u=r(2777),h=r(91664);(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}();var m=r(68136),f=r(85999);let p=()=>{let{user:e,signOut:t}=(0,m.a)(),[r,p]=(0,o.useState)(null);(0,o.useEffect)(()=>{e&&x()},[e]);let x=async()=>{try{if(!e)return;let{data:t,error:r}=await u.supabase.from("trustees").select("id").eq("trustee_user_id",e.id).eq("status","active").limit(1);if(r)throw r;p(t&&t.length>0)}catch(e){console.error("Error checking trustee status:",e)}},O=async()=>{try{await t(),f.Am.success("Successfully signed out")}catch(e){console.error("Sign out error:",e),f.Am.error("Failed to sign out")}};return(0,n.jsxs)("header",{className:"h-20 border-b border-gray-200 bg-white flex items-center px-4 sm:px-6 relative",children:[n.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2",children:n.jsx(s.default,{href:"/dashboard",className:"flex items-center",children:n.jsx(i.default,{src:"/images/Legalock-logo.svg",alt:"Legalock Logo",width:160,height:50,priority:!0})})}),(0,n.jsxs)("div",{className:"ml-auto flex items-center space-x-4",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:n.jsx(h.z,{variant:"outline",size:"sm",asChild:!0,children:(0,n.jsxs)(s.default,{href:"/trustee/dashboard",className:"flex items-center gap-2",children:[n.jsx(a.Z,{className:"h-4 w-4"}),"Trustee Dashboard"]})})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("p",{children:"Access your trustee responsibilities"})})]})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:n.jsx(h.z,{variant:"outline",size:"sm",asChild:!0,children:(0,n.jsxs)(s.default,{href:"/subscription",className:"flex items-center gap-2",children:[n.jsx(c.Z,{className:"h-4 w-4"}),"Subscription"]})})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("p",{children:"Manage your subscription plan"})})]})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:n.jsx(h.z,{variant:"outline",size:"sm",asChild:!0,children:(0,n.jsxs)(s.default,{href:"/settings",className:"flex items-center gap-2",children:[n.jsx(l.Z,{className:"h-4 w-4"}),"Settings"]})})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tooltip'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("p",{children:"Manage your account settings"})})]})}),(0,n.jsxs)(h.z,{variant:"outline",size:"sm",onClick:O,className:"flex items-center gap-2",children:[n.jsx(d.Z,{className:"h-4 w-4"}),"Sign Out"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{src:""}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-primary text-primary-foreground",children:(()=>{if(!e)return"U";if(e.firstName&&e.lastName)return`${e.firstName.charAt(0)}${e.lastName.charAt(0)}`.toUpperCase();if(e.email){let t=e.email.split("@");return 2===t.length?`${t[0].charAt(0)}${t[1].charAt(0)}`.toUpperCase():e.email.charAt(0).toUpperCase()}return"U"})()})]})]})]})}},91664:(e,t,r)=>{r.d(t,{z:()=>l});var n=r(10326),o=r(17577),s=r(34214),i=r(79360),a=r(51223);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...i},l)=>{let d=o?s.g7:"button";return n.jsx(d,{className:(0,a.cn)(c({variant:t,size:r,className:e})),ref:l,...i})});l.displayName="Button"},2777:(e,t,r)=>{r.d(t,{supabase:()=>a});var n=r(56292);let o="https://ccwvtcudztphwwzzgwvg.supabase.co",s=null,i=null,a=(0,n.eI)(o,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(i)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";i=(0,n.eI)(o,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,t,r)=>{r.d(t,{cn:()=>s});var n=r(41135),o=r(31009);function s(...e){return(0,o.m6)((0,n.W)(e))}}};