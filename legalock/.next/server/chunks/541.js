exports.id=541,exports.ids=[541],exports.modules={81421:(e,t,r)=>{"use strict";var o=r(31660),n=r(34006),i=r(43135),a=r(53659);e.exports=a||o.call(i,n)},34006:e=>{"use strict";e.exports=Function.prototype.apply},43135:e=>{"use strict";e.exports=Function.prototype.call},602:(e,t,r)=>{"use strict";var o=r(31660),n=r(17445),i=r(43135),a=r(81421);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},53659:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},98363:(e,t,r)=>{"use strict";var o=r(2749),n=r(602),i=n([o("%String.prototype.indexOf%")]);e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n([r]):r}},62344:(e,t,r)=>{"use strict";var o,n=r(602),i=r(86737);try{o=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!o&&i&&i(Object.prototype,"__proto__"),s=Object,l=s.getPrototypeOf;e.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof l&&function(e){return l(null==e?e:s(e))}},70091:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},86827:e=>{"use strict";e.exports=EvalError},56718:e=>{"use strict";e.exports=Error},37388:e=>{"use strict";e.exports=RangeError},63684:e=>{"use strict";e.exports=ReferenceError},31209:e=>{"use strict";e.exports=SyntaxError},17445:e=>{"use strict";e.exports=TypeError},76928:e=>{"use strict";e.exports=URIError},15678:e=>{"use strict";e.exports=Object},49214:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],d=0;d<u;d++)c[d]="$"+d;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var h=function(){};h.prototype=s.prototype,a.prototype=new h,h.prototype=null}return a}},31660:(e,t,r)=>{"use strict";var o=r(49214);e.exports=Function.prototype.bind||o},2749:(e,t,r)=>{"use strict";var o,n=r(15678),i=r(56718),a=r(86827),s=r(37388),l=r(63684),u=r(31209),c=r(17445),d=r(76928),h=r(95175),p=r(22334),m=r(46082),f=r(40430),y=r(24210),v=r(70792),P=r(92615),g=Function,T=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},_=r(86737),E=r(70091),b=function(){throw new c},S=_?function(){try{return arguments.callee,b}catch(e){try{return _(arguments,"callee").get}catch(e){return b}}}():b,O=r(91976)(),x=r(93941),w=r(67209),A=r(32395),R=r(34006),G=r(43135),C={},j="undefined"!=typeof Uint8Array&&x?x(Uint8Array):o,I={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":O&&x?x([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":C,"%AsyncGenerator%":C,"%AsyncGeneratorFunction%":C,"%AsyncIteratorPrototype%":C,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":C,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":O&&x?x(x([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&O&&x?x(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":_,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":s,"%ReferenceError%":l,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&O&&x?x(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":O&&x?x(""[Symbol.iterator]()):o,"%Symbol%":O?Symbol:o,"%SyntaxError%":u,"%ThrowTypeError%":S,"%TypedArray%":j,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":d,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":G,"%Function.prototype.apply%":R,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":w,"%Math.abs%":h,"%Math.floor%":p,"%Math.max%":m,"%Math.min%":f,"%Math.pow%":y,"%Math.round%":v,"%Math.sign%":P,"%Reflect.getPrototypeOf%":A};if(x)try{null.error}catch(e){var k=x(x(e));I["%Error.prototype%"]=k}var D=function e(t){var r;if("%AsyncFunction%"===t)r=T("async function () {}");else if("%GeneratorFunction%"===t)r=T("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=T("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&x&&(r=x(n.prototype))}return I[t]=r,r},N={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=r(31660),q=r(64995),F=M.call(G,Array.prototype.concat),U=M.call(R,Array.prototype.splice),L=M.call(G,String.prototype.replace),H=M.call(G,String.prototype.slice),$=M.call(G,RegExp.prototype.exec),z=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,B=/\\(\\)?/g,W=function(e){var t=H(e,0,1),r=H(e,-1);if("%"===t&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var o=[];return L(e,z,function(e,t,r,n){o[o.length]=r?L(n,B,"$1"):t||e}),o},K=function(e,t){var r,o=e;if(q(N,o)&&(o="%"+(r=N[o])[0]+"%"),q(I,o)){var n=I[o];if(n===C&&(n=D(o)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===$(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=W(e),o=r.length>0?r[0]:"",n=K("%"+o+"%",t),i=n.name,a=n.value,s=!1,l=n.alias;l&&(o=l[0],U(r,F([0,1],l)));for(var d=1,h=!0;d<r.length;d+=1){var p=r[d],m=H(p,0,1),f=H(p,-1);if(('"'===m||"'"===m||"`"===m||'"'===f||"'"===f||"`"===f)&&m!==f)throw new u("property names with quotes must have matching quotes");if("constructor"!==p&&h||(s=!0),o+="."+p,q(I,i="%"+o+"%"))a=I[i];else if(null!=a){if(!(p in a)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(_&&d+1>=r.length){var y=_(a,p);a=(h=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[p]}else h=q(a,p),a=a[p];h&&!s&&(I[i]=a)}}return a}},67209:(e,t,r)=>{"use strict";var o=r(15678);e.exports=o.getPrototypeOf||null},32395:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},93941:(e,t,r)=>{"use strict";var o=r(32395),n=r(67209),i=r(62344);e.exports=o?function(e){return o(e)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return n(e)}:i?function(e){return i(e)}:null},62980:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},86737:(e,t,r)=>{"use strict";var o=r(62980);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},91976:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(12522);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},12522:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},64995:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,i=r(31660);e.exports=i.call(o,n)},95175:e=>{"use strict";e.exports=Math.abs},22334:e=>{"use strict";e.exports=Math.floor},61781:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},46082:e=>{"use strict";e.exports=Math.max},40430:e=>{"use strict";e.exports=Math.min},24210:e=>{"use strict";e.exports=Math.pow},70792:e=>{"use strict";e.exports=Math.round},92615:(e,t,r)=>{"use strict";var o=r(61781);e.exports=function(e){return o(e)||0===e?e:e<0?-1:1}},19966:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,h="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,m=Boolean.prototype.valueOf,f=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,E=RegExp.prototype.test,b=Array.prototype.concat,S=Array.prototype.join,O=Array.prototype.slice,x=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,G="function"==typeof Symbol&&"object"==typeof Symbol.iterator,C="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===G?"object":"symbol")?Symbol.toStringTag:null,j=Object.prototype.propertyIsEnumerable,I=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function k(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-x(-e):x(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var D=r(71660),N=D.custom,M=z(N)?N:null,q={__proto__:null,double:'"',single:"'"},F={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function U(e,t,r){var o=q[r.quoteStyle||t];return o+e+o}function L(e){return!C||!("object"==typeof e&&(C in e||void 0!==e[C]))}function H(e){return"[object Array]"===K(e)&&L(e)}function $(e){return"[object RegExp]"===K(e)&&L(e)}function z(e){if(G)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!R)return!1;try{return R.call(e),!0}catch(e){}return!1}e.exports=function e(t,r,o,n){var s=r||{};if(W(s,"quoteStyle")&&!W(q,s.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(W(s,"maxStringLength")&&("number"==typeof s.maxStringLength?s.maxStringLength<0&&s.maxStringLength!==1/0:null!==s.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=!W(s,"customInspect")||s.customInspect;if("boolean"!=typeof l&&"symbol"!==l)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(W(s,"indent")&&null!==s.indent&&"	"!==s.indent&&!(parseInt(s.indent,10)===s.indent&&s.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(W(s,"numericSeparator")&&"boolean"!=typeof s.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var f=s.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+"... "+o+" more character"+(o>1?"s":"")}var n=F[r.quoteStyle||"single"];return n.lastIndex=0,U(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,J),"single",r)}(t,s);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var T=String(t);return f?k(t,T):T}if("bigint"==typeof t){var E=String(t)+"n";return f?k(t,E):E}var x=void 0===s.depth?5:s.depth;if(void 0===o&&(o=0),o>=x&&x>0&&"object"==typeof t)return H(t)?"[Array]":"[Object]";var A=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=S.call(Array(e.indent+1)," ")}return{base:r,prev:S.call(Array(t+1),r)}}(s,o);if(void 0===n)n=[];else if(V(n,t)>=0)return"[Circular]";function N(t,r,i){if(r&&(n=O.call(n)).push(r),i){var a={depth:s.depth};return W(s,"quoteStyle")&&(a.quoteStyle=s.quoteStyle),e(t,a,o+1,n)}return e(t,s,o+1,n)}if("function"==typeof t&&!$(t)){var B=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),et=ee(t,N);return"[Function"+(B?": "+B:" (anonymous)")+"]"+(et.length>0?" { "+S.call(et,", ")+" }":"")}if(z(t)){var er=G?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):R.call(t);return"object"!=typeof t||G?er:Q(er)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var eo,en="<"+_.call(String(t.nodeName)),ei=t.attributes||[],ea=0;ea<ei.length;ea++)en+=" "+ei[ea].name+"="+U((eo=ei[ea].value,g.call(String(eo),/"/g,"&quot;")),"double",s);return en+=">",t.childNodes&&t.childNodes.length&&(en+="..."),en+="</"+_.call(String(t.nodeName))+">"}if(H(t)){if(0===t.length)return"[]";var es=ee(t,N);return A&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(es)?"["+Z(es,A)+"]":"[ "+S.call(es,", ")+" ]"}if("[object Error]"===K(t)&&L(t)){var el=ee(t,N);return"cause"in Error.prototype||!("cause"in t)||j.call(t,"cause")?0===el.length?"["+String(t)+"]":"{ ["+String(t)+"] "+S.call(el,", ")+" }":"{ ["+String(t)+"] "+S.call(b.call("[cause]: "+N(t.cause),el),", ")+" }"}if("object"==typeof t&&l){if(M&&"function"==typeof t[M]&&D)return D(t,{depth:x-o});if("symbol"!==l&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var eu=[];return a&&a.call(t,function(e,r){eu.push(N(r,t,!0)+" => "+N(e,t))}),Y("Map",i.call(t),eu,A)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ec=[];return c&&c.call(t,function(e){ec.push(N(e,t))}),Y("Set",u.call(t),ec,A)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{h.call(e,h)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return X("WeakMap");if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{h.call(e,h);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return X("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return X("WeakRef");if("[object Number]"===K(t)&&L(t))return Q(N(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return Q(N(w.call(t)));if("[object Boolean]"===K(t)&&L(t))return Q(m.call(t));if("[object String]"===K(t)&&L(t))return Q(N(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||"undefined"!=typeof global&&t===global)return"{ [object globalThis] }";if(!("[object Date]"===K(t)&&L(t))&&!$(t)){var ed=ee(t,N),eh=I?I(t)===Object.prototype:t instanceof Object||t.constructor===Object,ep=t instanceof Object?"":"null prototype",em=!eh&&C&&Object(t)===t&&C in t?P.call(K(t),8,-1):ep?"Object":"",ef=(eh||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(em||ep?"["+S.call(b.call([],em||[],ep||[]),": ")+"] ":"");return 0===ed.length?ef+"{}":A?ef+"{"+Z(ed,A)+"}":ef+"{ "+S.call(ed,", ")+" }"}return String(t)};var B=Object.prototype.hasOwnProperty||function(e){return e in this};function W(e,t){return B.call(e,t)}function K(e){return f.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function J(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+T.call(t.toString(16))}function Q(e){return"Object("+e+")"}function X(e){return e+" { ? }"}function Y(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):S.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+S.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r,o=H(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=W(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(G){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)W(e,l)&&(!o||String(Number(l))!==l||!(l<e.length))&&(G&&r["$"+l]instanceof Symbol||(E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof A)for(var u=0;u<a.length;u++)j.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},71660:(e,t,r)=>{e.exports=r(21764).inspect},26815:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:o.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},6684:(e,t,r)=>{"use strict";var o=r(52503),n=r(23273),i=r(26815);e.exports={formats:i,parse:n,stringify:o}},23273:(e,t,r)=>{"use strict";var o=r(31847),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},s=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,t.throwOnLimitExceeded?u+1:u);if(t.throwOnLimitExceeded&&c.length>u)throw RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var d=-1,h=t.charset;if(t.charsetSentinel)for(p=0;p<c.length;++p)0===c[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[p]?h="utf-8":"utf8=%26%2310003%3B"===c[p]&&(h="iso-8859-1"),d=p,p=c.length);for(p=0;p<c.length;++p)if(p!==d){var p,m,f,y=c[p],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(m=t.decoder(y,a.decoder,h,"key"),f=t.strictNullHandling?null:""):(m=t.decoder(y.slice(0,P),a.decoder,h,"key"),f=o.maybeMap(s(y.slice(P+1),t,i(r[m])?r[m].length:0),function(e){return t.decoder(e,a.decoder,h,"value")})),f&&t.interpretNumericEntities&&"iso-8859-1"===h&&(f=String(f).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(f=i(f)?[f]:f);var g=n.call(r,m);g&&"combine"===t.duplicates?r[m]=o.combine(r[m],f):g&&"last"!==t.duplicates||(r[m]=f)}return r},u=function(e,t,r,n){var i=0;if(e.length>0&&"[]"===e[e.length-1]){var a=e.slice(0,-1).join("");i=Array.isArray(t)&&t[a]?t[a].length:0}for(var l=n?t:s(t,r,i),u=e.length-1;u>=0;--u){var c,d=e[u];if("[]"===d&&r.parseArrays)c=r.allowEmptyArrays&&(""===l||r.strictNullHandling&&null===l)?[]:o.combine([],l);else{c=r.plainObjects?{__proto__:null}:{};var h="["===d.charAt(0)&&"]"===d.charAt(d.length-1)?d.slice(1,-1):d,p=r.decodeDotInKeys?h.replace(/%2E/g,"."):h,m=parseInt(p,10);r.parseArrays||""!==p?!isNaN(m)&&d!==p&&String(m)===p&&m>=0&&r.parseArrays&&m<=r.arrayLimit?(c=[])[m]=l:"__proto__"!==p&&(c[p]=l):c={0:l}}l=c}return l},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var d=0;r.depth>0&&null!==(s=a.exec(i))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},d=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}};e.exports=function(e,t){var r=d(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],h=c(u,n[u],r,"string"==typeof e);i=o.merge(i,h,r)}return!0===r.allowSparse?i:o.compact(i)}},52503:(e,t,r)=>{"use strict";var o=r(49211),n=r(31847),i=r(26815),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},d=Date.prototype.toISOString,h=i.default,p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:h,formatter:i.formatters[h],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},m={},f=function e(t,r,i,a,s,u,d,h,f,y,v,P,g,T,_,E,b,S){for(var O,x,w=t,A=S,R=0,G=!1;void 0!==(A=A.get(m))&&!G;){var C=A.get(t);if(R+=1,void 0!==C){if(C===R)throw RangeError("Cyclic object value");G=!0}void 0===A.get(m)&&(R=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return f&&!E?f(r,p.encoder,b,"key",T):r;w=""}if("string"==typeof(O=w)||"number"==typeof O||"boolean"==typeof O||"symbol"==typeof O||"bigint"==typeof O||n.isBuffer(w))return f?[_(E?r:f(r,p.encoder,b,"key",T))+"="+_(f(w,p.encoder,b,"value",T))]:[_(r)+"="+_(String(w))];var j=[];if(void 0===w)return j;if("comma"===i&&l(w))E&&f&&(w=n.maybeMap(w,f)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))x=y;else{var I=Object.keys(w);x=v?I.sort(v):I}var k=h?String(r).replace(/\./g,"%2E"):String(r),D=a&&l(w)&&1===w.length?k+"[]":k;if(s&&l(w)&&0===w.length)return D+"[]";for(var N=0;N<x.length;++N){var M=x[N],q="object"==typeof M&&M&&void 0!==M.value?M.value:w[M];if(!d||null!==q){var F=P&&h?String(M).replace(/\./g,"%2E"):String(M),U=l(w)?"function"==typeof i?i(D,F):D:D+(P?"."+F:"["+F+"]");S.set(t,R);var L=o();L.set(m,S),c(j,e(q,U,i,a,s,u,d,h,"comma"===i&&E&&l(w)?null:f,y,v,P,g,T,_,E,b,L))}}return j},y=function(e){if(!e)return p;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=p.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||p.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}};e.exports=function(e,t){var r,n=e,i=y(t);"function"==typeof i.filter?n=(0,i.filter)("",n):l(i.filter)&&(r=i.filter);var a=[];if("object"!=typeof n||null===n)return"";var u=s[i.arrayFormat],d="comma"===u&&i.commaRoundTrip;r||(r=Object.keys(n)),i.sort&&r.sort(i.sort);for(var h=o(),p=0;p<r.length;++p){var m=r[p],v=n[m];i.skipNulls&&null===v||c(a,f(v,m,u,d,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,h))}var P=a.join(i.delimiter),g=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),P.length>0?g+P:""}},31847:(e,t,r)=>{"use strict";var o=r(26815),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,d=[],h=0;h<c.length;++h){var p=c.charCodeAt(h);if(45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||i===o.RFC1738&&(40===p||41===p)){d[d.length]=c.charAt(h);continue}if(p<128){d[d.length]=a[p];continue}if(p<2048){d[d.length]=a[192|p>>6]+a[128|63&p];continue}if(p<55296||p>=57344){d[d.length]=a[224|p>>12]+a[128|p>>6&63]+a[128|63&p];continue}h+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(h)),d[d.length]=a[240|p>>18]+a[128|p>>12&63]+a[128|p>>6&63]+a[128|63&p]}l+=d.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},52185:(e,t,r)=>{"use strict";var o=r(19966),n=r(17445),i=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o},a=function(e,t){if(e){var r=i(e,t);return r&&r.value}},s=function(e,t,r){var o=i(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}},l=function(e,t){if(e)return i(e,t,!0)};e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+o(e))},delete:function(t){var r=e&&e.next,o=l(e,t);return o&&r&&r===o&&(e=void 0),!!o},get:function(t){return a(e,t)},has:function(t){var r;return!!(r=e)&&!!i(r,t)},set:function(t,r){e||(e={next:void 0}),s(e,t,r)}};return t}},10417:(e,t,r)=>{"use strict";var o=r(2749),n=r(98363),i=r(19966),a=r(17445),s=o("%Map%",!0),l=n("Map.prototype.get",!0),u=n("Map.prototype.set",!0),c=n("Map.prototype.has",!0),d=n("Map.prototype.delete",!0),h=n("Map.prototype.size",!0);e.exports=!!s&&function(){var e,t={assert:function(e){if(!t.has(e))throw new a("Side channel does not contain "+i(e))},delete:function(t){if(e){var r=d(e,t);return 0===h(e)&&(e=void 0),r}return!1},get:function(t){if(e)return l(e,t)},has:function(t){return!!e&&c(e,t)},set:function(t,r){e||(e=new s),u(e,t,r)}};return t}},33457:(e,t,r)=>{"use strict";var o=r(2749),n=r(98363),i=r(19966),a=r(10417),s=r(17445),l=o("%WeakMap%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),h=n("WeakMap.prototype.delete",!0);e.exports=l?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new s("Side channel does not contain "+i(e))},delete:function(r){if(l&&r&&("object"==typeof r||"function"==typeof r)){if(e)return h(e,r)}else if(a&&t)return t.delete(r);return!1},get:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?u(e,r):t&&t.get(r)},has:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?d(e,r):!!t&&t.has(r)},set:function(r,o){l&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new l),c(e,r,o)):a&&(t||(t=a()),t.set(r,o))}};return r}:a},49211:(e,t,r)=>{"use strict";var o=r(17445),n=r(19966),i=r(52185),a=r(10417),s=r(33457)||a||i;e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+n(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=s()),e.set(t,r)}};return t}},2723:(e,t,r)=>{"use strict";r.d(t,{R:()=>b});var o=Object.defineProperty,n=Object.defineProperties,i=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))s.call(t,r)&&u(e,r,t[r]);if(a)for(var r of a(t))l.call(t,r)&&u(e,r,t[r]);return e},d=(e,t)=>n(e,i(t)),h=(e,t,r)=>new Promise((o,n)=>{var i=e=>{try{s(r.next(e))}catch(e){n(e)}},a=e=>{try{s(r.throw(e))}catch(e){n(e)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);s((r=r.apply(e,t)).next())}),p=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},m=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function f(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var y=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){let o=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}o.push(f(t))}return yield this.resend.post("/emails/batch",o,t)})}},v=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return h(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return h(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return h(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},P=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts/${e.id}`)})}update(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},g=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return h(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},T=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",f(e),t)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return h(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},_="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",E="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.2.0",b=class{constructor(e){if(this.key=e,this.apiKeys=new p(this),this.audiences=new m(this),this.batch=new y(this),this.broadcasts=new v(this),this.contacts=new P(this),this.domains=new g(this),this.emails=new T(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":E,"Content-Type":"application/json"})}fetchRequest(e){return h(this,arguments,function*(e,t={}){try{let r=yield fetch(`${_}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:d(c({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return h(this,arguments,function*(e,t,r={}){let o=c({method:"POST",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,o)})}get(e){return h(this,arguments,function*(e,t={}){let r=c({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return h(this,arguments,function*(e,t,r={}){let o=c({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,o)})}patch(e,t){return h(this,arguments,function*(e,t,r={}){let o=c({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,o)})}delete(e,t){return h(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}},31059:(e,t,r)=>{"use strict";r.d(t,{Z:()=>nU});var o={};r.r(o),r.d(o,{StripeAPIError:()=>j,StripeAuthenticationError:()=>I,StripeCardError:()=>G,StripeConnectionError:()=>N,StripeError:()=>R,StripeIdempotencyError:()=>q,StripeInvalidGrantError:()=>F,StripeInvalidRequestError:()=>C,StripePermissionError:()=>k,StripeRateLimitError:()=>D,StripeSignatureVerificationError:()=>M,StripeUnknownError:()=>U,TemporarySessionExpiredError:()=>L,generateV1Error:()=>w,generateV2Error:()=>A});var n={};r.r(n),r.d(n,{Account:()=>rM,AccountLinks:()=>rF,AccountSessions:()=>rL,Accounts:()=>rM,ApplePayDomains:()=>r$,ApplicationFees:()=>rB,Apps:()=>ny,Balance:()=>rK,BalanceTransactions:()=>rJ,Billing:()=>nv,BillingPortal:()=>nP,Charges:()=>rX,Checkout:()=>ng,Climate:()=>nT,ConfirmationTokens:()=>rZ,CountrySpecs:()=>r0,Coupons:()=>r6,CreditNotes:()=>r3,CustomerSessions:()=>r9,Customers:()=>r7,Disputes:()=>ot,Entitlements:()=>n_,EphemeralKeys:()=>oo,Events:()=>oi,ExchangeRates:()=>os,FileLinks:()=>ou,Files:()=>oh,FinancialConnections:()=>nE,Forwarding:()=>nb,Identity:()=>nS,InvoiceItems:()=>om,InvoicePayments:()=>oy,InvoiceRenderingTemplates:()=>oP,Invoices:()=>oT,Issuing:()=>nO,Mandates:()=>oE,OAuth:()=>oO,PaymentIntents:()=>ow,PaymentLinks:()=>oR,PaymentMethodConfigurations:()=>oC,PaymentMethodDomains:()=>oI,PaymentMethods:()=>oD,Payouts:()=>oM,Plans:()=>oF,Prices:()=>oL,Products:()=>o$,PromotionCodes:()=>oB,Quotes:()=>oK,Radar:()=>nx,Refunds:()=>oJ,Reporting:()=>nw,Reviews:()=>oX,SetupAttempts:()=>oZ,SetupIntents:()=>o0,ShippingRates:()=>o6,Sigma:()=>nA,Sources:()=>o3,SubscriptionItems:()=>o9,SubscriptionSchedules:()=>o7,Subscriptions:()=>nt,Tax:()=>nR,TaxCodes:()=>no,TaxIds:()=>ni,TaxRates:()=>ns,Terminal:()=>nG,TestHelpers:()=>nC,Tokens:()=>nu,Topups:()=>nd,Transfers:()=>np,Treasury:()=>nj,V2:()=>nI,WebhookEndpoints:()=>nf});var i=r(84770),a=r(17702);class s{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}computeSHA256Async(e){throw Error("computeSHA256 not implemented.")}}class l extends Error{}class u extends s{computeHMACSignature(e,t){return i.createHmac("sha256",t).update(e,"utf8").digest("hex")}async computeHMACSignatureAsync(e,t){return await this.computeHMACSignature(e,t)}async computeSHA256Async(e){return new Uint8Array(await i.createHash("sha256").update(e).digest())}}var c=r(32615),d=r.t(c,2),h=r(35240),p=r.t(h,2);class m{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(m.TIMEOUT_ERROR_CODE);return e.code=m.TIMEOUT_ERROR_CODE,e}}m.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],m.TIMEOUT_ERROR_CODE="ETIMEDOUT";class f{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}let y=c||d,v=h||p,P=new y.Agent({keepAlive:!0}),g=new v.Agent({keepAlive:!0});class T extends m{constructor(e){super(),this._agent=e}getClientName(){return"node"}makeRequest(e,t,r,o,n,i,a,s){let l="http"===a,u=this._agent;return u||(u=l?P:g),new Promise((a,c)=>{let d=(l?y:v).request({host:e,port:t,path:r,method:o,agent:u,headers:n,ciphers:"DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5"});d.setTimeout(s,()=>{d.destroy(m.makeTimeoutError())}),d.on("response",e=>{a(new _(e))}),d.on("error",e=>{c(e)}),d.once("socket",e=>{e.connecting?e.once(l?"connect":"secureConnect",()=>{d.write(i),d.end()}):(d.write(i),d.end())})})}}class _ extends f{constructor(e){super(e.statusCode,e.headers||{}),this._res=e}getRawResponse(){return this._res}toStream(e){return this._res.once("end",()=>e()),this._res}toJSON(){return new Promise((e,t)=>{let r="";this._res.setEncoding("utf8"),this._res.on("data",e=>{r+=e}),this._res.once("end",()=>{try{e(JSON.parse(r))}catch(e){t(e)}})})}}class E extends m{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=E.makeFetchWithAbortTimeout(e):this._fetchFn=E.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,o)=>{let n;let i=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(m.makeTimeoutError())},o)});return Promise.race([e(t,r),i]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,o)=>{let n=new AbortController,i=setTimeout(()=>{i=null,n.abort(m.makeTimeoutError())},o);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw m.makeTimeoutError();throw e}finally{i&&clearTimeout(i)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,o,n,i,a,s){let l=new URL(r,`${"http"===a?"http":"https"}://${e}`);l.port=t;let u="POST"==o||"PUT"==o||"PATCH"==o;return new b(await this._fetchFn(l.toString(),{method:o,headers:n,body:i||(u?"":void 0)},s))}}class b extends f{constructor(e){super(e.status,b._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class S extends s{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new l("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=O[n[e]];return i.join("")}async computeSHA256Async(e){return new Uint8Array(await this.subtleCrypto.digest("SHA-256",e))}}let O=Array(256);for(let e=0;e<O.length;e++)O[e]=e.toString(16).padStart(2,"0");class x{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new E(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new S(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}let w=e=>{switch(e.type){case"card_error":return new G(e);case"invalid_request_error":return new C(e);case"api_error":return new j(e);case"authentication_error":return new I(e);case"rate_limit_error":return new D(e);case"idempotency_error":return new q(e);case"invalid_grant":return new F(e);default:return new U(e)}},A=e=>"temporary_session_expired"===e.type?new L(e):"invalid_fields"===e.code?new C(e):w(e);class R extends Error{constructor(e={},t=null){super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.userMessage=e.user_message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}R.generate=w;class G extends R{constructor(e={}){super(e,"StripeCardError")}}class C extends R{constructor(e={}){super(e,"StripeInvalidRequestError")}}class j extends R{constructor(e={}){super(e,"StripeAPIError")}}class I extends R{constructor(e={}){super(e,"StripeAuthenticationError")}}class k extends R{constructor(e={}){super(e,"StripePermissionError")}}class D extends R{constructor(e={}){super(e,"StripeRateLimitError")}}class N extends R{constructor(e={}){super(e,"StripeConnectionError")}}class M extends R{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class q extends R{constructor(e={}){super(e,"StripeIdempotencyError")}}class F extends R{constructor(e={}){super(e,"StripeInvalidGrantError")}}class U extends R{constructor(e={}){super(e,"StripeUnknownError")}}class L extends R{constructor(e={}){super(e,"TemporarySessionExpiredError")}}var H=r(6684);let $=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host","authenticator","stripeContext","additionalHeaders"];function z(e){return e&&"object"==typeof e&&$.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function B(e,t){return H.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString(),arrayFormat:"v2"==t?"repeat":"indices"}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let W=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function K(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!z(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>$.includes(e));return r.length>0&&r.length!==t.length&&X(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function V(e){let t={host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.authenticator=Z(e.pop());else if(z(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!$.includes(e));if(o.length&&X(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.authenticator=Z(r.apiKey)),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.stripeContext){if(t.headers["Stripe-Account"])throw Error("Can't specify both stripeAccount and stripeContext.");t.headers["Stripe-Context"]=r.stripeContext}if(r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host),r.authenticator){if(r.apiKey)throw Error("Can't specify both apiKey and authenticator.");if("function"!=typeof r.authenticator)throw Error("The authenticator must be a function receiving a request as the first parameter.");t.authenticator=r.authenticator}r.additionalHeaders&&(t.headers=r.additionalHeaders)}}return t}function J(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function Q(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function X(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function Y(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}function Z(e){let t=t=>(t.headers.Authorization="Bearer "+e,Promise.resolve());return t._apiKey=e,t}function ee(e,t){return this[e]instanceof Date?Math.floor(this[e].getTime()/1e3).toString():t}function et(e){return e&&e.startsWith("/v2")?"v2":"v1"}var er=r(61282);class eo extends R{}class en extends x{constructor(){super(),this._exec=er.exec,this._UNAME_CACHE=null}uuid4(){return i.randomUUID?i.randomUUID():super.uuid4()}getUname(){return this._UNAME_CACHE||(this._UNAME_CACHE=new Promise((e,t)=>{try{this._exec("uname -a",(t,r)=>{if(t)return e(null);e(r)})}catch(t){e(null)}})),this._UNAME_CACHE}secureCompare(e,t){if(!e||!t)throw Error("secureCompare must receive two arguments");if(e.length!==t.length)return!1;if(i.timingSafeEqual){let r=new TextEncoder,o=r.encode(e),n=r.encode(t);return i.timingSafeEqual(o,n)}return super.secureCompare(e,t)}createEmitter(){return new a.EventEmitter}tryBufferData(e){if(!(e.file.data instanceof a.EventEmitter))return Promise.resolve(e);let t=[];return new Promise((r,o)=>{e.file.data.on("data",e=>{t.push(e)}).once("end",()=>{let o=Object.assign({},e);o.file.data=function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}(t),r(o)}).on("error",e=>{o(new eo({message:"An error occurred while attempting to process the file for upload.",detail:e}))})})}createNodeHttpClient(e){return new T(e)}createDefaultHttpClient(){return new T}createNodeCryptoProvider(){return new u}createDefaultCryptoProvider(){return this.createNodeCryptoProvider()}}class ei{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return J({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=o.toStream(()=>{let r=this._makeResponseEvent(e,o.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r,o){return n=>{let i=n.getHeaders(),a=this._getRequestId(i),s=n.getStatusCode(),l=this._makeResponseEvent(e,s,i);this._stripe._emitter.emit("response",l),n.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=i,e.error.statusCode=s,e.error.requestId=a,401===s?new I(e.error):403===s?new k(e.error):429===s?new D(e.error):"v2"===t?A(e.error):w(e.error);return e},e=>{throw new j({message:"Invalid JSON received from the Stripe API",exception:e,requestId:i["request-id"]})}).then(e=>{this._recordRequestMetrics(a,l.elapsed,r);let t=n.getRawResponse();this._addHeadersDirectlyToObject(t,i),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:t}),o(null,e)},e=>o(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&m.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(2,e-1),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t,r){let o=this._getMaxNetworkRetries(t),n=()=>`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`;if("v2"===r){if("POST"===e||"DELETE"===e)return n()}else if("v1"===r&&"POST"===e&&o>0)return n();return null}_makeHeaders({contentType:e,contentLength:t,apiVersion:r,clientUserAgent:o,method:n,userSuppliedHeaders:i,userSuppliedSettings:a,stripeAccount:s,stripeContext:l,apiMode:u}){let c={Accept:"application/json","Content-Type":e,"User-Agent":this._getUserAgentString(u),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":s,"Stripe-Context":l,"Idempotency-Key":this._defaultIdempotencyKey(n,a,u)},d="POST"==n||"PUT"==n||"PATCH"==n;return(d||t)&&(d||X(`${n} method had non-zero contentLength but no payload is expected for this verb`),c["Content-Length"]=t),Object.assign(J(c),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(e){let t=this._stripe.getConstant("PACKAGE_VERSION"),r=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/${e} NodeBindings/${t} ${r}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e){if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)X("Request metrics buffer is full, dropping telemetry message.");else{let o={request_id:e,request_duration_ms:t};r&&r.length>0&&(o.usage=r),this._stripe._prevRequestMetrics.push(o)}}}_rawRequest(e,t,r,o){return new Promise((n,i)=>{let a;try{let n=e.toUpperCase();if("POST"!==n&&r&&0!==Object.keys(r).length)throw Error("rawRequest only supports params on POST requests. Please pass null and add your parameters to path.");let i=[].slice.call([r,o]),s=K(i),l=Object.assign({},s),u=V(i),c=u.headers,d=u.authenticator;a={requestMethod:n,requestPath:t,bodyData:l,queryData:{},authenticator:d,headers:c,host:null,streaming:!1,settings:{},usage:["raw_request"]}}catch(e){i(e);return}let{headers:s,settings:l}=a,u=a.authenticator;this._request(a.requestMethod,a.host,t,a.bodyData,u,{headers:s,settings:l,streaming:a.streaming},a.usage,function(e,t){e?i(e):n(t)})})}_request(e,t,r,o,n,i,a=[],s,l=null){var u;let c;n=null!==(u=null!=n?n:this._stripe._authenticator)&&void 0!==u?u:null;let d=et(r),h=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),p=(o,l,u)=>{let f=i.settings&&i.settings.timeout&&Number.isInteger(i.settings.timeout)&&i.settings.timeout>=0?i.settings.timeout:this._stripe.getApiField("timeout"),y={host:t||this._stripe.getApiField("host"),port:this._stripe.getApiField("port"),path:r,method:e,headers:Object.assign({},l),body:c,protocol:this._stripe.getApiField("protocol")};n(y).then(()=>{let t=this._stripe.getApiField("httpClient").makeRequest(y.host,y.port,y.path,y.method,y.headers,y.body,y.protocol,f),n=Date.now(),c=J({api_version:o,account:l["Stripe-Account"],idempotency_key:l["Idempotency-Key"],method:e,path:r,request_start_time:n}),v=u||0,P=this._getMaxNetworkRetries(i.settings||{});this._stripe._emitter.emit("request",c),t.then(e=>ei._shouldRetry(e,v,P)?h(p,o,l,v,e.getHeaders()["retry-after"]):i.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(c,a,s)(e):this._jsonResponseHandler(c,d,a,s)(e)).catch(e=>ei._shouldRetry(null,v,P,e)?h(p,o,l,v,null):s(new N({message:e.code&&e.code===m.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${f}ms)`:ei._generateConnectionErrorMessage(v),detail:e})))}).catch(e=>{throw new R({message:"Unable to authenticate the request",exception:e})})},f=(t,r)=>{if(t)return s(t);c=r,this._stripe.getClientUserAgent(t=>{let r=this._stripe.getApiField("version"),o=this._makeHeaders({contentType:"v2"==d?"application/json":"application/x-www-form-urlencoded",contentLength:c.length,apiVersion:r,clientUserAgent:t,method:e,userSuppliedHeaders:i.headers,userSuppliedSettings:i.settings,stripeAccount:"v2"==d?null:this._stripe.getApiField("stripeAccount"),stripeContext:"v2"==d?this._stripe.getApiField("stripeContext"):null,apiMode:d});p(r,o,0)})};if(l)l(e,o,i.headers,f);else{let e;f(null,"v2"==d?o?JSON.stringify(o,ee):"":B(o||{},d))}}}class ea{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=eh(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class es extends ea{getNextPage(e){let t=eh(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class el extends ea{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}class eu{constructor(e,t,r,o){this.currentPageIterator=(async()=>(await e).data[Symbol.iterator]())(),this.nextPageUrl=(async()=>(await e).next_page_url||null)(),this.requestArgs=t,this.spec=r,this.stripeResource=o}async turnPage(){let e=await this.nextPageUrl;if(!e)return null;this.spec.fullPath=e;let t=await this.stripeResource._makeRequest([],this.spec,{});return this.nextPageUrl=Promise.resolve(t.next_page_url),this.currentPageIterator=Promise.resolve(t.data[Symbol.iterator]()),this.currentPageIterator}async next(){{let e=(await this.currentPageIterator).next();if(!e.done)return{done:!1,value:e.value}}let e=await this.turnPage();if(!e)return{done:!0,value:void 0};let t=e.next();return t.done?{done:!0,value:void 0}:{done:!1,value:t.value}}}let ec=(e,t,r,o)=>{let n=et(r.fullPath||r.path);return"v2"!==n&&"search"===r.methodType?ed(new el(o,t,r,e)):"v2"!==n&&"list"===r.methodType?ed(new es(o,t,r,e)):"v2"===n&&"list"===r.methodType?ed(new eu(o,t,r,e)):null},ed=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),o=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return Q(new Promise((e,o)=>{t().then(function o(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?o({done:!0,value:void 0}):t().then(o))}).catch(o)}),o)}),o={autoPagingEach:r,autoPagingToArray:function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return Q(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>o};return o};function eh(e){return!!K([].slice.call(e)).ending_before}function ep(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=W(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=W(this.path),this.initialize(...arguments)}function em(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function ef(e,t){return function(e){return new em(e,t)}}ep.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},ep.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=Q(this._makeRequest(t,e,{}),r);return Object.assign(o,ec(this,t,e,o)),o}},ep.MAX_BUFFERED_REQUEST_METRICS=100,ep.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){var o;let n=(t.method||"GET").toUpperCase(),i=t.usage||[],a=t.urlParams||[],s=t.encode||(e=>e),l=!!t.fullPath,u=W(l?t.fullPath:t.path||""),c=l?t.fullPath:this.createResourcePathWithSymbols(t.path),d=[].slice.call(e),h=a.reduce((e,t)=>{let r=d.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${n} ${c}\`)`);return e[t]=r,e},{}),p=s(Object.assign({},K(d),r)),m=V(d),f=m.host||t.host,y=!!t.streaming;if(d.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${d}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${n} \`${c}\`)`);let v=l?u(h):this.createFullPath(u,h),P=Object.assign(m.headers,t.headers);t.validator&&t.validator(p,{headers:P});let g="GET"===t.method||"DELETE"===t.method;return{requestMethod:n,requestPath:v,bodyData:g?null:p,queryData:g?p:{},authenticator:null!==(o=m.authenticator)&&void 0!==o?o:null,headers:P,host:null!=f?f:null,streaming:y,settings:m.settings,usage:i}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",B(a.queryData,et(a.requestPath))].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.authenticator,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let ey=ep.method,ev=ep.extend({retrieve:ey({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:ey({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:ey({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:ey({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:ey({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:ey({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:ey({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),eP=ep.method,eg=ep.extend({retrieve:eP({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:eP({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),eT=ep.method,e_=ep.extend({create:eT({method:"POST",fullPath:"/v1/billing/alerts"}),retrieve:eT({method:"GET",fullPath:"/v1/billing/alerts/{id}"}),list:eT({method:"GET",fullPath:"/v1/billing/alerts",methodType:"list"}),activate:eT({method:"POST",fullPath:"/v1/billing/alerts/{id}/activate"}),archive:eT({method:"POST",fullPath:"/v1/billing/alerts/{id}/archive"}),deactivate:eT({method:"POST",fullPath:"/v1/billing/alerts/{id}/deactivate"})}),eE=ep.method,eb=ep.extend({create:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),finalizeAmount:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount"}),increment:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),respond:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond"}),reverse:eE({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),eS=ep.method,eO=ep.extend({retrieve:eS({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:eS({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:eS({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:eS({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:eS({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),ex=ep.method,ew=ep.extend({create:ex({method:"POST",fullPath:"/v1/tax/calculations"}),retrieve:ex({method:"GET",fullPath:"/v1/tax/calculations/{calculation}"}),listLineItems:ex({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),eA=ep.method,eR=ep.extend({create:eA({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:eA({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:eA({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:eA({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eG=ep.method,eC=ep.extend({deliverCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"}),submitCard:eG({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/submit"})}),ej=ep.method,eI=ep.extend({create:ej({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:ej({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:ej({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:ej({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),ek=ep.method,eD=ep.extend({create:ek({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:ek({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:ek({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:ek({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),eN=ep.method,eM=ep.extend({create:eN({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:eN({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:eN({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:eN({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:eN({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),eq=ep.method,eF=ep.extend({create:eq({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),eU=ep.method,eL=ep.extend({create:eU({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),eH=ep.method,e$=ep.extend({retrieve:eH({method:"GET",fullPath:"/v1/billing/credit_balance_summary"})}),ez=ep.method,eB=ep.extend({retrieve:ez({method:"GET",fullPath:"/v1/billing/credit_balance_transactions/{id}"}),list:ez({method:"GET",fullPath:"/v1/billing/credit_balance_transactions",methodType:"list"})}),eW=ep.method,eK=ep.extend({create:eW({method:"POST",fullPath:"/v1/billing/credit_grants"}),retrieve:eW({method:"GET",fullPath:"/v1/billing/credit_grants/{id}"}),update:eW({method:"POST",fullPath:"/v1/billing/credit_grants/{id}"}),list:eW({method:"GET",fullPath:"/v1/billing/credit_grants",methodType:"list"}),expire:eW({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/expire"}),voidGrant:eW({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/void"})}),eV=ep.method,eJ=ep.extend({create:eV({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:eV({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:eV({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),eQ=ep.method,eX=ep.extend({fundCashBalance:eQ({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),eY=ep.method,eZ=ep.extend({create:eY({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:eY({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:eY({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),e1=ep.method,e0=ep.extend({create:e1({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:e1({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:e1({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:e1({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:e1({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),e2=ep.method,e6=ep.extend({retrieve:e2({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:e2({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),e8=ep.method,e3=ep.extend({create:e8({method:"POST",fullPath:"/v2/core/event_destinations"}),retrieve:e8({method:"GET",fullPath:"/v2/core/event_destinations/{id}"}),update:e8({method:"POST",fullPath:"/v2/core/event_destinations/{id}"}),list:e8({method:"GET",fullPath:"/v2/core/event_destinations",methodType:"list"}),del:e8({method:"DELETE",fullPath:"/v2/core/event_destinations/{id}"}),disable:e8({method:"POST",fullPath:"/v2/core/event_destinations/{id}/disable"}),enable:e8({method:"POST",fullPath:"/v2/core/event_destinations/{id}/enable"}),ping:e8({method:"POST",fullPath:"/v2/core/event_destinations/{id}/ping"})}),e4=ep.method,e9=ep.extend({retrieve(...e){return e4({method:"GET",fullPath:"/v2/core/events/{id}",transformResponseData:e=>this.addFetchRelatedObjectIfNeeded(e)}).apply(this,e)},list(...e){return e4({method:"GET",fullPath:"/v2/core/events",methodType:"list",transformResponseData:e=>Object.assign(Object.assign({},e),{data:e.data.map(this.addFetchRelatedObjectIfNeeded.bind(this))})}).apply(this,e)},addFetchRelatedObjectIfNeeded(e){return e.related_object&&e.related_object.url?Object.assign(Object.assign({},e),{fetchRelatedObject:()=>e4({method:"GET",fullPath:e.related_object.url}).apply(this,[{stripeAccount:e.context}])}):e}}),e5=ep.method,e7=ep.extend({create:e5({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:e5({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:e5({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:e5({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),te=ep.method,tt=ep.extend({create:te({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:te({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:te({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:te({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),close:te({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/close"}),retrieveFeatures:te({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:te({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),tr=ep.method,to=ep.extend({fail:tr({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:tr({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:tr({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),tn=ep.method,ti=ep.extend({create:tn({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:tn({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:tn({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:tn({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),ta=ep.method,ts=ep.extend({create:ta({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:ta({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:ta({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:ta({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:ta({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),tl=ep.method,tu=ep.extend({create:tl({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),tc=ep.method,td=ep.extend({create:tc({method:"POST",fullPath:"/v2/billing/meter_event_adjustments"})}),th=ep.method,tp=ep.extend({create:th({method:"POST",fullPath:"/v2/billing/meter_event_session"})}),tm=ep.method,tf=ep.extend({create:tm({method:"POST",fullPath:"/v2/billing/meter_event_stream",host:"meter-events.stripe.com"})}),ty=ep.method,tv=ep.extend({create:ty({method:"POST",fullPath:"/v1/billing/meter_events"})}),tP=ep.method,tg=ep.extend({create:tP({method:"POST",fullPath:"/v2/billing/meter_events"})}),tT=ep.method,t_=ep.extend({create:tT({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:tT({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:tT({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:tT({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:tT({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:tT({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:tT({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),tE=ep.method,tb=ep.extend({create:tE({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:tE({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:tE({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:tE({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:tE({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),tS=ep.method,tO=ep.extend({update:tS({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}"}),fail:tS({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:tS({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:tS({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),tx=ep.method,tw=ep.extend({create:tx({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:tx({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:tx({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:tx({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),tA=ep.method,tR=ep.extend({update:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}"}),fail:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),tG=ep.method,tC=ep.extend({create:tG({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:tG({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:tG({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:tG({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),tj=ep.method,tI=ep.extend({activate:tj({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:tj({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:tj({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),tk=ep.method,tD=ep.extend({create:tk({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:tk({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:tk({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:tk({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),tN=ep.method,tM=ep.extend({retrieve:tN({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:tN({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),tq=ep.method,tF=ep.extend({retrieve:tq({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:tq({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),tU=ep.method,tL=ep.extend({presentPaymentMethod:tU({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),tH=ep.method,t$=ep.extend({create:tH({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:tH({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:tH({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:tH({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:tH({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:tH({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:tH({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:tH({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:tH({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:tH({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),tz=ep.method,tB=ep.extend({create:tz({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),tW=ep.method,tK=ep.extend({retrieve:tW({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:tW({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),tV=ep.method,tJ=ep.extend({create:tV({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),tQ=ep.method,tX=ep.extend({retrieve:tQ({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:tQ({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),tY=ep.method,tZ=ep.extend({expire:tY({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),t1=ep.method,t0=ep.extend({create:t1({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:t1({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:t1({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:t1({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),t2=ep.method,t6=ep.extend({create:t2({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:t2({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:t2({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),t8=ep.method,t3=ep.extend({retrieve:t8({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:t8({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),t4=ep.method,t9=ep.extend({create:t4({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:t4({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:t4({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),t5=ep.method,t7=ep.extend({retrieve:t5({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:t5({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),re=ep.method,rt=ep.extend({create:re({method:"POST",fullPath:"/v1/apps/secrets"}),list:re({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:re({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:re({method:"GET",fullPath:"/v1/apps/secrets/find"})}),rr=ep.method,ro=ep.extend({create:rr({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),rn=ep.method,ri=ep.extend({create:rn({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:rn({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),update:rn({method:"POST",fullPath:"/v1/checkout/sessions/{session}"}),list:rn({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:rn({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:rn({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),ra=ep.method,rs=ep.extend({create:ra({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:ra({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),rl=ep.method,ru=ep.extend({retrieve:rl({method:"GET",fullPath:"/v1/tax/settings"}),update:rl({method:"POST",fullPath:"/v1/tax/settings"})}),rc=ep.method,rd=ep.extend({retrieve:rc({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:rc({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),rh=ep.method,rp=ep.extend({create:rh({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:rh({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:rh({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:rh({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:rh({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),rm=ep.method,rf=ep.extend({retrieve:rm({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:rm({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:rm({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),ry=ep.method,rv=ep.extend({retrieve:ry({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:ry({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),rP=ep.method,rg=ep.extend({createForceCapture:rP({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:rP({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:rP({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),rT=ep.method,r_=ep.extend({retrieve:rT({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:rT({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),rE=ep.method,rb=ep.extend({retrieve:rE({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:rE({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:rE({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),rS=ep.method,rO=ep.extend({retrieve:rS({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:rS({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:rS({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:rS({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),rx=ep.method,rw=ep.extend({retrieve:rx({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:rx({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),rA=ep.method,rR=ep.extend({create:rA({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:rA({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:rA({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:rA({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),rG=ep.method,rC=ep.extend({create:rG({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:rG({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:rG({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:rG({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:rG({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),rj=ep.method,rI=ep.extend({retrieve:rj({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:rj({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),rk=ep.method,rD=ep.extend({create:rk({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:rk({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:rk({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:rk({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:rk({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:rk({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),rN=ep.method,rM=ep.extend({create:rN({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?rN({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),rN({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:rN({method:"POST",fullPath:"/v1/accounts/{account}"}),list:rN({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:rN({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:rN({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:rN({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:rN({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:rN({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:rN({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:rN({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:rN({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:rN({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:rN({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:rN({method:"GET",fullPath:"/v1/account"}),retrieveCapability:rN({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:rN({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:rN({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:rN({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:rN({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:rN({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),rq=ep.method,rF=ep.extend({create:rq({method:"POST",fullPath:"/v1/account_links"})}),rU=ep.method,rL=ep.extend({create:rU({method:"POST",fullPath:"/v1/account_sessions"})}),rH=ep.method,r$=ep.extend({create:rH({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:rH({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:rH({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:rH({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),rz=ep.method,rB=ep.extend({retrieve:rz({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:rz({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:rz({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:rz({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:rz({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:rz({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),rW=ep.method,rK=ep.extend({retrieve:rW({method:"GET",fullPath:"/v1/balance"})}),rV=ep.method,rJ=ep.extend({retrieve:rV({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:rV({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),rQ=ep.method,rX=ep.extend({create:rQ({method:"POST",fullPath:"/v1/charges"}),retrieve:rQ({method:"GET",fullPath:"/v1/charges/{charge}"}),update:rQ({method:"POST",fullPath:"/v1/charges/{charge}"}),list:rQ({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:rQ({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:rQ({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),rY=ep.method,rZ=ep.extend({retrieve:rY({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),r1=ep.method,r0=ep.extend({retrieve:r1({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:r1({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),r2=ep.method,r6=ep.extend({create:r2({method:"POST",fullPath:"/v1/coupons"}),retrieve:r2({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:r2({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:r2({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:r2({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),r8=ep.method,r3=ep.extend({create:r8({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:r8({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:r8({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:r8({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:r8({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:r8({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:r8({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:r8({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),r4=ep.method,r9=ep.extend({create:r4({method:"POST",fullPath:"/v1/customer_sessions"})}),r5=ep.method,r7=ep.extend({create:r5({method:"POST",fullPath:"/v1/customers"}),retrieve:r5({method:"GET",fullPath:"/v1/customers/{customer}"}),update:r5({method:"POST",fullPath:"/v1/customers/{customer}"}),list:r5({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:r5({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:r5({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:r5({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:r5({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:r5({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:r5({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:r5({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:r5({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:r5({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:r5({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:r5({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:r5({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:r5({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:r5({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:r5({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:r5({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:r5({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:r5({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:r5({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:r5({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:r5({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:r5({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:r5({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:r5({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),oe=ep.method,ot=ep.extend({retrieve:oe({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:oe({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:oe({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:oe({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),or=ep.method,oo=ep.extend({create:or({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:or({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),on=ep.method,oi=ep.extend({retrieve:on({method:"GET",fullPath:"/v1/events/{id}"}),list:on({method:"GET",fullPath:"/v1/events",methodType:"list"})}),oa=ep.method,os=ep.extend({retrieve:oa({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:oa({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),ol=ep.method,ou=ep.extend({create:ol({method:"POST",fullPath:"/v1/file_links"}),retrieve:ol({method:"GET",fullPath:"/v1/file_links/{link}"}),update:ol({method:"POST",fullPath:"/v1/file_links/{link}"}),list:ol({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),oc=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.entries(e).forEach(([e,n])=>{let i=o?`${o}[${e}]`:e;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(n)){if(!(n instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(n,"data"))return r(n,i);t[i]=n}else t[i]=String(n)})};return r(e,null),t}(t);for(let e in u){if(!Object.prototype.hasOwnProperty.call(u,e))continue;let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},od=ep.method,oh=ep.extend({create:od({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:od({method:"GET",fullPath:"/v1/files/{file}"}),list:od({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,B(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,oc(e,t,r))).catch(e=>o(e,null))}}),op=ep.method,om=ep.extend({create:op({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:op({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:op({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:op({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:op({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),of=ep.method,oy=ep.extend({retrieve:of({method:"GET",fullPath:"/v1/invoice_payments/{invoice_payment}"}),list:of({method:"GET",fullPath:"/v1/invoice_payments",methodType:"list"})}),ov=ep.method,oP=ep.extend({retrieve:ov({method:"GET",fullPath:"/v1/invoice_rendering_templates/{template}"}),list:ov({method:"GET",fullPath:"/v1/invoice_rendering_templates",methodType:"list"}),archive:ov({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/archive"}),unarchive:ov({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/unarchive"})}),og=ep.method,oT=ep.extend({create:og({method:"POST",fullPath:"/v1/invoices"}),retrieve:og({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:og({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:og({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:og({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),addLines:og({method:"POST",fullPath:"/v1/invoices/{invoice}/add_lines"}),createPreview:og({method:"POST",fullPath:"/v1/invoices/create_preview"}),finalizeInvoice:og({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:og({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),markUncollectible:og({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:og({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),removeLines:og({method:"POST",fullPath:"/v1/invoices/{invoice}/remove_lines"}),search:og({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:og({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLines:og({method:"POST",fullPath:"/v1/invoices/{invoice}/update_lines"}),updateLineItem:og({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:og({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),o_=ep.method,oE=ep.extend({retrieve:o_({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),ob=ep.method,oS="connect.stripe.com",oO=ep.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${oS}/${r}?${B(e)}`},token:ob({method:"POST",path:"oauth/token",host:oS}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),ob({method:"POST",path:"oauth/deauthorize",host:oS}).apply(this,[e,...t])}}),ox=ep.method,ow=ep.extend({create:ox({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:ox({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:ox({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:ox({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:ox({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),oA=ep.method,oR=ep.extend({create:oA({method:"POST",fullPath:"/v1/payment_links"}),retrieve:oA({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:oA({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:oA({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:oA({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),oG=ep.method,oC=ep.extend({create:oG({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:oG({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:oG({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:oG({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),oj=ep.method,oI=ep.extend({create:oj({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:oj({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:oj({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:oj({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:oj({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),ok=ep.method,oD=ep.extend({create:ok({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:ok({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:ok({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:ok({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:ok({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:ok({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),oN=ep.method,oM=ep.extend({create:oN({method:"POST",fullPath:"/v1/payouts"}),retrieve:oN({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:oN({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:oN({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:oN({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:oN({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),oq=ep.method,oF=ep.extend({create:oq({method:"POST",fullPath:"/v1/plans"}),retrieve:oq({method:"GET",fullPath:"/v1/plans/{plan}"}),update:oq({method:"POST",fullPath:"/v1/plans/{plan}"}),list:oq({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:oq({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),oU=ep.method,oL=ep.extend({create:oU({method:"POST",fullPath:"/v1/prices"}),retrieve:oU({method:"GET",fullPath:"/v1/prices/{price}"}),update:oU({method:"POST",fullPath:"/v1/prices/{price}"}),list:oU({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:oU({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),oH=ep.method,o$=ep.extend({create:oH({method:"POST",fullPath:"/v1/products"}),retrieve:oH({method:"GET",fullPath:"/v1/products/{id}"}),update:oH({method:"POST",fullPath:"/v1/products/{id}"}),list:oH({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:oH({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:oH({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:oH({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:oH({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:oH({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:oH({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),oz=ep.method,oB=ep.extend({create:oz({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:oz({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:oz({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:oz({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),oW=ep.method,oK=ep.extend({create:oW({method:"POST",fullPath:"/v1/quotes"}),retrieve:oW({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:oW({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:oW({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:oW({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:oW({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:oW({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:oW({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:oW({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:oW({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),oV=ep.method,oJ=ep.extend({create:oV({method:"POST",fullPath:"/v1/refunds"}),retrieve:oV({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:oV({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:oV({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:oV({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),oQ=ep.method,oX=ep.extend({retrieve:oQ({method:"GET",fullPath:"/v1/reviews/{review}"}),list:oQ({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:oQ({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),oY=ep.method,oZ=ep.extend({list:oY({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),o1=ep.method,o0=ep.extend({create:o1({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:o1({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:o1({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:o1({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:o1({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:o1({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:o1({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),o2=ep.method,o6=ep.extend({create:o2({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:o2({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:o2({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:o2({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),o8=ep.method,o3=ep.extend({create:o8({method:"POST",fullPath:"/v1/sources"}),retrieve:o8({method:"GET",fullPath:"/v1/sources/{source}"}),update:o8({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:o8({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:o8({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),o4=ep.method,o9=ep.extend({create:o4({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:o4({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:o4({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:o4({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:o4({method:"DELETE",fullPath:"/v1/subscription_items/{item}"})}),o5=ep.method,o7=ep.extend({create:o5({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:o5({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:o5({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:o5({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:o5({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:o5({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),ne=ep.method,nt=ep.extend({create:ne({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:ne({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:ne({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:ne({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:ne({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:ne({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:ne({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:ne({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),nr=ep.method,no=ep.extend({retrieve:nr({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:nr({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),nn=ep.method,ni=ep.extend({create:nn({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:nn({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:nn({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:nn({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),na=ep.method,ns=ep.extend({create:na({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:na({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:na({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:na({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),nl=ep.method,nu=ep.extend({create:nl({method:"POST",fullPath:"/v1/tokens"}),retrieve:nl({method:"GET",fullPath:"/v1/tokens/{token}"})}),nc=ep.method,nd=ep.extend({create:nc({method:"POST",fullPath:"/v1/topups"}),retrieve:nc({method:"GET",fullPath:"/v1/topups/{topup}"}),update:nc({method:"POST",fullPath:"/v1/topups/{topup}"}),list:nc({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:nc({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),nh=ep.method,np=ep.extend({create:nh({method:"POST",fullPath:"/v1/transfers"}),retrieve:nh({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:nh({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:nh({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:nh({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:nh({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:nh({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:nh({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),nm=ep.method,nf=ep.extend({create:nm({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:nm({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:nm({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:nm({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:nm({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),ny=ef("apps",{Secrets:rt}),nv=ef("billing",{Alerts:e_,CreditBalanceSummary:e$,CreditBalanceTransactions:eB,CreditGrants:eK,MeterEventAdjustments:tu,MeterEvents:tv,Meters:t_}),nP=ef("billingPortal",{Configurations:eD,Sessions:ro}),ng=ef("checkout",{Sessions:ri}),nT=ef("climate",{Orders:tb,Products:tF,Suppliers:rd}),n_=ef("entitlements",{ActiveEntitlements:eg,Features:e7}),nE=ef("financialConnections",{Accounts:ev,Sessions:rs,Transactions:r_}),nb=ef("forwarding",{Requests:t9}),nS=ef("identity",{VerificationReports:rI,VerificationSessions:rD}),nO=ef("issuing",{Authorizations:eO,Cardholders:eR,Cards:eI,Disputes:e0,PersonalizationDesigns:tD,PhysicalBundles:tM,Tokens:rf,Transactions:rb}),nx=ef("radar",{EarlyFraudWarnings:e6,ValueListItems:rR,ValueLists:rC}),nw=ef("reporting",{ReportRuns:t6,ReportTypes:t3}),nA=ef("sigma",{ScheduledQueryRuns:t7}),nR=ef("tax",{Calculations:ew,Registrations:t0,Settings:ru,Transactions:rO}),nG=ef("terminal",{Configurations:eM,ConnectionTokens:eL,Locations:ts,Readers:t$}),nC=ef("testHelpers",{ConfirmationTokens:eF,Customers:eX,Refunds:tZ,TestClocks:rp,Issuing:ef("issuing",{Authorizations:eb,Cards:eC,PersonalizationDesigns:tI,Transactions:rg}),Terminal:ef("terminal",{Readers:tL}),Treasury:ef("treasury",{InboundTransfers:to,OutboundPayments:tO,OutboundTransfers:tR,ReceivedCredits:tB,ReceivedDebits:tJ})}),nj=ef("treasury",{CreditReversals:eJ,DebitReversals:eZ,FinancialAccounts:tt,InboundTransfers:ti,OutboundPayments:tw,OutboundTransfers:tC,ReceivedCredits:tK,ReceivedDebits:tX,TransactionEntries:rv,Transactions:rw}),nI=ef("v2",{Billing:ef("billing",{MeterEventAdjustments:td,MeterEventSession:tp,MeterEventStream:tf,MeterEvents:tg}),Core:ef("core",{EventDestinations:e3,Events:e9})}),nk="api.stripe.com",nD="/v1/",nN="2025-03-31.basil",nM=["name","version","url","partner_id"],nq=["authenticator","apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount","stripeContext"],nF=e=>new ei(e,ep.MAX_BUFFERED_REQUEST_METRICS),nU=function(e,t=nF){function r(n,i={}){if(!(this instanceof r))return new r(n,i);let a=this._getPropsFromConfig(i);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=r.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let s=a.httpAgent||null;this._api={host:a.host||nk,port:a.port||"443",protocol:a.protocol||"https",basePath:nD,version:a.apiVersion||nN,timeout:Y("timeout",a.timeout,8e4),maxNetworkRetries:Y("maxNetworkRetries",a.maxNetworkRetries,2),agent:s,httpClient:a.httpClient||(s?this._platformFunctions.createNodeHttpClient(s):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null,stripeContext:a.stripeContext||null};let l=a.typescript||!1;l!==r.USER_AGENT.typescript&&(r.USER_AGENT.typescript=l),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setAuthenticator(n,a.authenticator),this.errors=o,this.webhooks=r.webhooks,this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=r.StripeResource}return r.PACKAGE_VERSION="18.0.0",r.USER_AGENT=Object.assign({bindings_version:r.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),r.StripeResource=ep,r.resources=n,r.HttpClient=m,r.HttpClientResponse=f,r.CryptoProvider=s,r.webhooks=function(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof l&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){let t=u(e),r=t.signature||t.cryptoProvider.computeHMACSignature(t.payloadString,t.secret);return t.generateHeaderString(r)},generateTestHeaderStringAsync:async function(e){let t=u(e),r=t.signature||await t.cryptoProvider.computeHMACSignatureAsync(t.payloadString,t.secret);return t.generateHeaderString(r)}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r),f=(l=l||s()).computeHMACSignature(o(d,h),r);return i(d,c,h,f,a,p,m,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r);l=l||s();let f=await l.computeHMACSignatureAsync(o(d,h),r);return i(d,c,h,f,a,p,m,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new M(t,e,{message:"No webhook payload was provided."});let o="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new M(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,s="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let o=t.split("=");return"t"===o[0]&&(e.timestamp=parseInt(o[1],10)),o[0]===r&&e.signatures.push(o[1]),e},{timestamp:-1,signatures:[]});if(!s||-1===s.timestamp)throw new M(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!s.signatures.length)throw new M(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:s,suspectPayloadType:o}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://docs.stripe.com/webhooks/signature",d=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new M(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+d});throw new M(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+d})}let h=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&h>i)throw new M(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}function u(e){if(!e)throw new R({message:"Options are required"});let t=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),o=e.scheme||r.EXPECTED_SCHEME,n=e.cryptoProvider||s(),i=`${t}.${e.payload}`;return Object.assign(Object.assign({},e),{timestamp:t,scheme:o,cryptoProvider:n,payloadString:i,generateHeaderString:e=>`t=${t},${o}=${e}`})}return t.signature=r,t}(e),r.errors=o,r.createNodeHttpClient=e.createNodeHttpClient,r.createFetchHttpClient=e.createFetchHttpClient,r.createNodeCryptoProvider=e.createNodeCryptoProvider,r.createSubtleCryptoProvider=e.createSubtleCryptoProvider,r.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,rawRequest(e,t,r,o){return this._requestSender._rawRequest(e,t,r,o)},_setAuthenticator(e,t){if(e&&t)throw Error("Can't specify both apiKey and authenticator");if(!e&&!t)throw Error("Neither apiKey nor config.authenticator provided");this._authenticator=e?Z(e):t},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=nM.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return nk;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return nD;case"DEFAULT_API_VERSION":return nN;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 5;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return r[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=Y(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>5,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(r.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=encodeURIComponent(null!==(o=e[t])&&void 0!==o?o:"null"));n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this))},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!nq.includes(e)).length>0)throw Error(`Config object may only contain the following: ${nq.join(", ")}`);return e},parseThinEvent(e,t,r,o,n,i){return this.webhooks.constructEvent(e,t,r,o,n,i)}},r}(new en)}};