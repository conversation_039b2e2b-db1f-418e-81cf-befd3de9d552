exports.id=8196,exports.ids=[8196],exports.modules={1323:(e,t)=>{"use strict";Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},33592:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(16689);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:s="",children:c,iconNode:d,...l},u)=>(0,r.createElement)("svg",{ref:u,...a,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:o("lucide",s),...l},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},c)=>(0,r.createElement)(s,{ref:c,iconNode:t,className:o(`lucide-${i(e)}`,n),...a}));return n.displayName=`${e}`,n}},82922:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},69972:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},59911:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},48141:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let r=n(50167),i=n(20997),o=r._(n(16689)),a=n(45782);async function s(e){let{Component:t,ctx:n}=e;return{pageProps:await (0,a.loadGetInitialProps)(t,n)}}class c extends o.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,i.jsx)(e,{...t})}}c.origGetInitialProps=s,c.getInitialProps=s,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45597:(e,t,n)=>{"use strict";n.a(e,async(e,r)=>{try{n.d(t,{Z:()=>f});var i=n(20997),o=n(16689),a=n.n(o),s=n(14661),c=n(95364),d=n(59911);!function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}();var l=n(27742),u=e([c,l]);[c,l]=u.then?(await u)():u;let m=a().forwardRef(({className:e,title:t,children:n,icon:r,...o},a)=>i.jsx("li",{children:i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,i.jsxs)("a",{ref:a,className:(0,l.cn)("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",e),...o,children:[i.jsx("div",{className:"text-sm font-medium leading-none",children:t}),i.jsx("p",{className:"line-clamp-2 text-sm leading-snug text-muted-foreground",children:n})]})})}));m.displayName="ListItem";let f=()=>{let e=(0,s.useLocation)(),t="/"===e.pathname;return i.jsx("header",{className:"w-full bg-white border-b border-gray-200",children:i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between items-center h-16",children:[i.jsx("div",{className:"flex items-center",children:(0,i.jsxs)(s.Link,{to:"/",className:"flex items-center gap-2",children:[i.jsx(d.Z,{className:"h-8 w-8 text-primary"}),i.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Legalock"})]})}),t&&i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hidden md:flex",children:(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Features"}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,i.jsxs)("ul",{className:"grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]",children:[i.jsx(m,{href:"#",title:"Asset Management",icon:"Shield",children:"Create a comprehensive inventory of your digital and physical assets"}),i.jsx(m,{href:"#",title:"Digital Vault",icon:"Vault",children:"Securely store sensitive documents for your trustees"}),i.jsx(m,{href:"#",title:"Time Capsule",icon:"Clock",children:"Schedule future messages to loved ones"}),i.jsx(m,{href:"#",title:"Will Writing",icon:"FileText",children:"Document your final wishes with our step-by-step guide"})]})})]}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:i.jsx(s.Link,{to:"#pricing",className:Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}())(),children:"Pricing"})}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:i.jsx(s.Link,{to:"/terms",className:Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}())(),children:"Terms"})})]})}),i.jsx("div",{className:"flex items-center gap-4",children:t?(0,i.jsxs)(i.Fragment,{children:[i.jsx(c.z,{variant:"outline",asChild:!0,children:i.jsx(s.Link,{to:"/login",children:"Sign In"})}),i.jsx(c.z,{asChild:!0,children:i.jsx(s.Link,{to:"/register",children:"Get Started"})})]}):i.jsx(c.z,{variant:"outline",asChild:!0,children:i.jsx(s.Link,{to:"/",children:"Back to Home"})})})]})})})};r()}catch(e){r(e)}})},95364:(e,t,n)=>{"use strict";n.a(e,async(e,r)=>{try{n.d(t,{z:()=>u});var i=n(20997),o=n(16689),a=n(14338),s=n(16926),c=n(27742),d=e([a,s,c]);[a,s,c]=d.then?(await d)():d;let l=(0,s.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{let d=r?a.Slot:"button";return i.jsx(d,{className:(0,c.cn)(l({variant:t,size:n,className:e})),ref:s,...o})});u.displayName="Button",r()}catch(e){r(e)}})},99250:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},87095:(e,t,n)=>{"use strict";n.a(e,async(e,r)=>{try{n.d(t,{I:()=>c});var i=n(20997),o=n(16689),a=n(27742),s=e([a]);a=(s.then?(await s)():s)[0];let c=o.forwardRef(({className:e,type:t,...n},r)=>i.jsx("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));c.displayName="Input",r()}catch(e){r(e)}})},77415:(e,t,n)=>{"use strict";n.a(e,async(e,r)=>{try{n.d(t,{a:()=>c}),n(20997);var i=n(16689);n(70550),n(14661);var o=n(37270),a=e([o]);o=(a.then?(await a)():a)[0];let s=(0,i.createContext)(void 0),c=()=>{let e=(0,i.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};r()}catch(e){r(e)}})},70550:(e,t,n)=>{"use strict";n.d(t,{O:()=>r});let r=(0,n(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,n)=>{"use strict";n.a(e,async(e,r)=>{try{n.d(t,{cn:()=>s});var i=n(16593),o=n(68097),a=e([i,o]);function s(...e){return(0,o.twMerge)((0,i.clsx)(e))}[i,o]=a.then?(await a)():a,r()}catch(e){r(e)}})},35244:(e,t)=>{"use strict";var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))}};