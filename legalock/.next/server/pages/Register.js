"use strict";(()=>{var e={};e.id=4037,e.ids=[4037,660],e.modules={91202:(e,s,r)=>{r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{config:()=>j,default:()=>p,getServerSideProps:()=>g,getStaticPaths:()=>u,getStaticProps:()=>h,reportWebVitals:()=>f,routeModule:()=>S,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>y});var a=r(87093),l=r(35244),i=r(1323),n=r(61682),c=r.n(n),o=r(48141),d=r.n(o),m=r(63395),x=e([m]);m=(x.then?(await x)():x)[0];let p=(0,i.l)(m,"default"),h=(0,i.l)(m,"getStaticProps"),u=(0,i.l)(m,"getStaticPaths"),g=(0,i.l)(m,"getServerSideProps"),j=(0,i.l)(m,"config"),f=(0,i.l)(m,"reportWebVitals"),y=(0,i.l)(m,"unstable_getStaticProps"),v=(0,i.l)(m,"unstable_getStaticPaths"),N=(0,i.l)(m,"unstable_getStaticParams"),b=(0,i.l)(m,"unstable_getServerProps"),w=(0,i.l)(m,"unstable_getServerSideProps"),S=new a.PagesRouteModule({definition:{kind:l.x.PAGES,page:"/Register",pathname:"/Register",bundlePath:"",filename:""},components:{App:d(),Document:c()},userland:m});t()}catch(e){t(e)}})},80906:(e,s,r)=>{r.d(s,{Z:()=>t});let t=(0,r(33592).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},34200:(e,s,r)=>{r.a(e,async(e,t)=>{try{r.d(s,{Z:()=>y});var a=r(20997),l=r(16689),i=r(14661),n=r(45641),c=r(1656),o=r(9926),d=r(95364),m=r(87095),x=r(99250),p=r(82698),h=r(77415),u=r(82922),g=r(69972),j=e([n,c,o,d,m,p,h]);[n,c,o,d,m,p,h]=j.then?(await j)():j;let f=o.z.object({firstName:o.z.string().min(2,{message:"First name must be at least 2 characters"}),lastName:o.z.string().min(2,{message:"Last name must be at least 2 characters"}),email:o.z.string().email({message:"Please enter a valid email address"}),password:o.z.string().min(8,{message:"Password must be at least 8 characters"}),terms:o.z.boolean().refine(e=>!0===e,{message:"You must accept the terms and conditions"})}),y=()=>{(0,i.useNavigate)();let{signUp:e}=(0,h.a)(),[s,r]=(0,l.useState)(!1),[t,o]=(0,l.useState)(!1),j=(0,n.useForm)({resolver:(0,c.zodResolver)(f),defaultValues:{firstName:"",lastName:"",email:"",password:"",terms:!1}}),y=async s=>{r(!0);try{await e(s.email,s.password,s.firstName,s.lastName)}catch(e){console.error("Registration error:",e)}finally{r(!1)}},v=()=>{o(!t)};return(0,a.jsxs)("div",{children:[a.jsx(x.l0,{...j,children:(0,a.jsxs)("form",{onSubmit:j.handleSubmit(y),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[a.jsx(x.Wi,{control:j.control,name:"firstName",render:({field:e})=>(0,a.jsxs)(x.xJ,{children:[a.jsx(x.lX,{children:"First Name"}),a.jsx(x.NI,{children:a.jsx(m.I,{placeholder:"John",...e})}),a.jsx(x.zG,{})]})}),a.jsx(x.Wi,{control:j.control,name:"lastName",render:({field:e})=>(0,a.jsxs)(x.xJ,{children:[a.jsx(x.lX,{children:"Last Name"}),a.jsx(x.NI,{children:a.jsx(m.I,{placeholder:"Doe",...e})}),a.jsx(x.zG,{})]})})]}),a.jsx(x.Wi,{control:j.control,name:"email",render:({field:e})=>(0,a.jsxs)(x.xJ,{children:[a.jsx(x.lX,{children:"Email"}),a.jsx(x.NI,{children:a.jsx(m.I,{placeholder:"<EMAIL>",type:"email",...e})}),a.jsx(x.zG,{})]})}),a.jsx(x.Wi,{control:j.control,name:"password",render:({field:e})=>(0,a.jsxs)(x.xJ,{children:[a.jsx(x.lX,{children:"Password"}),a.jsx(x.NI,{children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(m.I,{placeholder:"••••••••",type:t?"text":"password",...e}),a.jsx(d.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-500",onClick:v,children:t?a.jsx(u.Z,{className:"h-4 w-4"}):a.jsx(g.Z,{className:"h-4 w-4"})})]})}),a.jsx(x.zG,{})]})}),a.jsx(x.Wi,{control:j.control,name:"terms",render:({field:e})=>(0,a.jsxs)(x.xJ,{className:"flex flex-row items-start space-x-3 space-y-0",children:[a.jsx(x.NI,{children:a.jsx(p.X,{checked:e.value,onCheckedChange:e.onChange})}),(0,a.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,a.jsxs)(x.lX,{className:"text-sm font-normal",children:["I agree to the"," ",a.jsx(i.Link,{to:"/terms",className:"text-primary hover:underline",children:"terms of service"})," ","and"," ",a.jsx(i.Link,{to:"/privacy",className:"text-primary hover:underline",children:"privacy policy"})]}),a.jsx(x.zG,{})]})]})}),a.jsx("div",{children:a.jsx(d.z,{type:"submit",className:"w-full",disabled:s,children:s?(0,a.jsxs)("span",{className:"flex items-center",children:[a.jsx("span",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"}),"Creating account..."]}):"Create Account"})})]})}),a.jsx("div",{className:"mt-6 text-center text-sm",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",a.jsx(i.Link,{to:"/login",className:"text-primary hover:underline",children:"Sign in"})]})})]})};t()}catch(e){t(e)}})},82698:(e,s,r)=>{r.a(e,async(e,t)=>{try{r.d(s,{X:()=>d});var a=r(20997),l=r(16689),i=r(11601),n=r(80906),c=r(27742),o=e([i,c]);[i,c]=o.then?(await o)():o;let d=l.forwardRef(({className:e,...s},r)=>a.jsx(i.Root,{ref:r,className:(0,c.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:a.jsx(i.Indicator,{className:(0,c.cn)("flex items-center justify-center text-current"),children:a.jsx(n.Z,{className:"h-4 w-4"})})}));d.displayName=i.Root.displayName,t()}catch(e){t(e)}})},63395:(e,s,r)=>{r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>c});var a=r(20997);r(16689);var l=r(34200),i=r(45597),n=e([l,i]);[l,i]=n.then?(await n)():n;let c=()=>(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-gray-50",children:[a.jsx(i.Z,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[a.jsx("h2",{className:"mt-6 text-2xl font-bold text-gray-900",children:"Create your account"}),a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Start managing your digital legacy today"})]}),a.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:a.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:a.jsx(l.Z,{})})})]})]});t()}catch(e){t(e)}})},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},1656:e=>{e.exports=import("@hookform/resolvers/zod")},11601:e=>{e.exports=import("@radix-ui/react-checkbox")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},45641:e=>{e.exports=import("react-hook-form")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")},9926:e=>{e.exports=import("zod")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[1682,8196],()=>r(91202));module.exports=t})();