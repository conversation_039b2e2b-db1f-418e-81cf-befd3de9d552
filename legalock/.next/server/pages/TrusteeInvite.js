(()=>{var e={};e.id=4343,e.ids=[4343,660],e.modules={1323:(e,t)=>{"use strict";Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},30657:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>m,getServerSideProps:()=>x,getStaticPaths:()=>g,getStaticProps:()=>p,reportWebVitals:()=>b,routeModule:()=>S,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>v});var s=r(87093),i=r(35244),o=r(1323),a=r(61682),u=r.n(a),l=r(48141),c=r.n(l),d=r(65834),f=e([d]);d=(f.then?(await f)():f)[0];let m=(0,o.l)(d,"default"),p=(0,o.l)(d,"getStaticProps"),g=(0,o.l)(d,"getStaticPaths"),x=(0,o.l)(d,"getServerSideProps"),h=(0,o.l)(d,"config"),b=(0,o.l)(d,"reportWebVitals"),v=(0,o.l)(d,"unstable_getStaticProps"),y=(0,o.l)(d,"unstable_getStaticPaths"),j=(0,o.l)(d,"unstable_getStaticParams"),P=(0,o.l)(d,"unstable_getServerProps"),_=(0,o.l)(d,"unstable_getServerSideProps"),S=new s.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/TrusteeInvite",pathname:"/TrusteeInvite",bundlePath:"",filename:""},components:{App:c(),Document:u()},userland:d});n()}catch(e){n(e)}})},33592:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(16689);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:a="",children:u,iconNode:l,...c},d)=>(0,n.createElement)("svg",{ref:d,...o,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:i("lucide",a),...c},[...l.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(u)?u:[u]])),u=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...o},u)=>(0,n.createElement)(a,{ref:u,iconNode:t,className:i(`lucide-${s(e)}`,r),...o}));return r.displayName=`${e}`,r}},99763:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},80906:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},62782:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return i}});let n=r(85575),s=r(45869);function i(e){let t=s.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70031:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u.ReadonlyURLSearchParams},RedirectType:function(){return u.RedirectType},ServerInsertedHTMLContext:function(){return l.ServerInsertedHTMLContext},notFound:function(){return u.notFound},permanentRedirect:function(){return u.permanentRedirect},redirect:function(){return u.redirect},useParams:function(){return m},usePathname:function(){return d},useRouter:function(){return f},useSearchParams:function(){return c},useSelectedLayoutSegment:function(){return g},useSelectedLayoutSegments:function(){return p},useServerInsertedHTML:function(){return l.useServerInsertedHTML}});let n=r(16689),s=r(7443),i=r(27583),o=r(66162),a=r(73737),u=r(46209),l=r(31575);function c(){let e=(0,n.useContext)(i.SearchParamsContext),t=(0,n.useMemo)(()=>e?new u.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(62782);e("useSearchParams()")}return t}function d(){return(0,n.useContext)(i.PathnameContext)}function f(){let e=(0,n.useContext)(s.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function m(){return(0,n.useContext)(i.PathParamsContext)}function p(e){void 0===e&&(e="children");let t=(0,n.useContext)(s.LayoutRouterContext);return t?function e(t,r,n,s){let i;if(void 0===n&&(n=!0),void 0===s&&(s=[]),n)i=t[1][r];else{var u;let e=t[1];i=null!=(u=e.children)?u:Object.values(e)[0]}if(!i)return s;let l=i[0],c=(0,o.getSegmentValue)(l);return!c||c.startsWith(a.PAGE_SEGMENT_KEY)?s:(s.push(c),e(i,r,!1,s))}(t.tree,e):null}function g(e){void 0===e&&(e="children");let t=p(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===a.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46209:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return n.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(67878),s=r(95610);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19598:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67878:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return m},getURLFromRedirectError:function(){return f},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return l}});let s=r(54580),i=r(72934),o=r(19598),a="NEXT_REDIRECT";function u(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let n=Error(a);n.digest=a+";"+t+";"+e+";"+r+";";let i=s.requestAsyncStorage.getStore();return i&&(n.mutableCookies=i.mutableCookies),n}function l(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,s]=e.digest.split(";",4),i=Number(s);return t===a&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(i)&&i in o.RedirectStatusCode}function f(e){return d(e)?e.digest.split(";",3)[2]:null}function m(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66162:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48141:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(50167),s=r(20997),i=n._(r(16689)),o=r(45782);async function a(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,o.loadGetInitialProps)(t,r)}}class u extends i.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,s.jsx)(e,{...t})}}u.origGetInitialProps=a,u.getInitialProps=a,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85575:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return s}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},47723:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{Z:()=>h});var s=r(20997),i=r(16689),o=r(39332),a=r(45641),u=r(1656),l=r(9926),c=r(95364),d=r(87095);!function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}();var f=r(82698),m=r(99250),p=r(27751),g=r(37270),x=e([a,u,l,c,d,f,p,g]);[a,u,l,c,d,f,p,g]=x.then?(await x)():x;let b=l.object({firstName:l.string().min(2,{message:"First name must be at least 2 characters"}),lastName:l.string().min(2,{message:"Last name must be at least 2 characters"}),email:l.string().email({message:"Please enter a valid email address"}),permissions:l.array(l.string()).min(1,{message:"Please select at least one permission"}),message:l.string().optional()});function h({onSuccess:e,onCancel:t,isEditing:r=!1,defaultValues:n,onUpdate:l}){let x=(0,o.useRouter)(),{user:h}=(0,p.a)(),[v,y]=(0,i.useState)(!1),j=[{id:"assets",label:"Assets",description:"Digital and physical assets inventory"},{id:"vault",label:"Digital Vault",description:"Important documents and files"},{id:"contacts",label:"Emergency Contacts",description:"People to notify after your passing"},{id:"services",label:"Service Sunset",description:"Services to cancel after your passing"}],P=(0,a.useForm)({resolver:(0,u.zodResolver)(b),defaultValues:n||{firstName:"",lastName:"",email:"",permissions:["assets","vault","contacts","services"],message:""}}),_=async t=>{try{if(!h){g.toast.error("You must be logged in to manage trustees");return}if(y(!0),r&&l){await l(t),y(!1);return}let n=await fetch("/api/trustees",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t.email,first_name:t.firstName.charAt(0).toUpperCase()+t.firstName.slice(1),last_name:t.lastName.charAt(0).toUpperCase()+t.lastName.slice(1),relationship:"",permissions:t.permissions})});if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to add trustee")}await n.json(),g.toast.success("Trustee added successfully"),e&&e(),P.reset({firstName:"",lastName:"",email:"",permissions:["assets","vault","contacts","services"],message:""})}catch(e){console.error("Error managing trustee:",e),e.message.includes("duplicate")?g.toast.error("This person is already added as your trustee"):g.toast.error(e.message||"Error managing trustee")}finally{y(!1)}};return s.jsx(m.l0,{...P,children:(0,s.jsxs)("form",{onSubmit:P.handleSubmit(_),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(m.Wi,{control:P.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(m.xJ,{children:[s.jsx(m.lX,{children:"First Name*"}),s.jsx(m.NI,{children:s.jsx(d.I,{placeholder:"First name",...e})}),s.jsx(m.zG,{})]})}),s.jsx(m.Wi,{control:P.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(m.xJ,{children:[s.jsx(m.lX,{children:"Last Name*"}),s.jsx(m.NI,{children:s.jsx(d.I,{placeholder:"Last name",...e})}),s.jsx(m.zG,{})]})})]}),s.jsx(m.Wi,{control:P.control,name:"email",render:({field:e})=>(0,s.jsxs)(m.xJ,{children:[s.jsx(m.lX,{children:"Email Address*"}),s.jsx(m.NI,{children:s.jsx(d.I,{type:"email",placeholder:"<EMAIL>",...e,disabled:r})}),s.jsx(m.pf,{children:r?"Email cannot be changed. To change the email, delete this trustee and add a new one.":"Your trustee will receive an invitation at this email address"}),s.jsx(m.zG,{})]})}),s.jsx(m.Wi,{control:P.control,name:"permissions",render:()=>(0,s.jsxs)(m.xJ,{children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx(m.lX,{children:"Permissions*"}),s.jsx(m.pf,{children:"Select what information this trustee will have access to after your passing"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:j.map(e=>s.jsx(m.Wi,{control:P.control,name:"permissions",render:({field:t})=>(0,s.jsxs)(m.xJ,{className:"flex flex-row items-start space-x-3 space-y-0",children:[s.jsx(m.NI,{children:s.jsx(f.X,{checked:t.value?.includes(e.id),onCheckedChange:r=>{let n=r?[...t.value,e.id]:t.value.filter(t=>t!==e.id);t.onChange(n)}})}),(0,s.jsxs)("div",{className:"space-y-1 leading-none",children:[s.jsx(m.lX,{children:e.label}),s.jsx(m.pf,{children:e.description})]})]})},e.id))}),s.jsx(m.zG,{})]})}),s.jsx(m.Wi,{control:P.control,name:"message",render:({field:e})=>(0,s.jsxs)(m.xJ,{children:[s.jsx(m.lX,{children:"Personal Message (Optional)"}),s.jsx(m.NI,{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Add a personal message to your invitation",className:"resize-none h-24",...e})}),s.jsx(m.pf,{children:"This message will be included in the invitation email"}),s.jsx(m.zG,{})]})}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[t?s.jsx(c.z,{type:"button",variant:"outline",onClick:t,children:"Cancel"}):s.jsx(c.z,{type:"button",variant:"outline",onClick:()=>x.push("/trustees"),children:"Cancel"}),s.jsx(c.z,{type:"submit",disabled:v,children:v?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"}),r?"Updating Trustee...":"Adding Trustee..."]}):r?"Update Trustee":"Add Trustee"})]})]})})}n()}catch(e){n(e)}})},95364:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{z:()=>d});var s=r(20997),i=r(16689),o=r(14338),a=r(16926),u=r(27742),l=e([o,a,u]);[o,a,u]=l.then?(await l)():l;let c=(0,a.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...i},a)=>{let l=n?o.Slot:"button";return s.jsx(l,{className:(0,u.cn)(c({variant:t,size:r,className:e})),ref:a,...i})});d.displayName="Button",n()}catch(e){n(e)}})},82698:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{X:()=>c});var s=r(20997),i=r(16689),o=r(11601),a=r(80906),u=r(27742),l=e([o,u]);[o,u]=l.then?(await l)():l;let c=i.forwardRef(({className:e,...t},r)=>s.jsx(o.Root,{ref:r,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:s.jsx(o.Indicator,{className:(0,u.cn)("flex items-center justify-center text-current"),children:s.jsx(a.Z,{className:"h-4 w-4"})})}));c.displayName=o.Root.displayName,n()}catch(e){n(e)}})},99250:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},87095:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{I:()=>u});var s=r(20997),i=r(16689),o=r(27742),a=e([o]);o=(a.then?(await a)():a)[0];let u=i.forwardRef(({className:e,type:t,...r},n)=>s.jsx("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));u.displayName="Input",n()}catch(e){n(e)}})},27751:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{a:()=>u}),r(20997);var s=r(16689);r(39332);var i=r(37270),o=e([i]);i=(o.then?(await o)():o)[0];let a=(0,s.createContext)(void 0),u=()=>{let e=(0,s.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};n()}catch(e){n(e)}})},27742:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{cn:()=>a});var s=r(16593),i=r(68097),o=e([s,i]);function a(...e){return(0,i.twMerge)((0,s.clsx)(e))}[s,i]=o.then?(await o)():o,n()}catch(e){n(e)}})},65834:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>c});var s=r(20997);r(16689);var i=r(99763),o=r(14661),a=r(95364);!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var u=r(47723),l=e([a,u]);[a,u]=l.then?(await l)():l;let c=()=>(0,s.jsxs)("div",{children:[s.jsx("div",{className:"mb-6",children:s.jsx(a.z,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsxs)(o.Link,{to:"/trustees",className:"flex items-center text-gray-500 hover:text-gray-700",children:[s.jsx(i.Z,{className:"mr-2 h-4 w-4"}),"Back to Trustees"]})})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Invite a Trustee",description:"Invite someone you trust to have access to your digital legacy."}),s.jsx("div",{className:"mt-6 bg-white rounded-lg border p-6",children:s.jsx(u.Z,{})})]});n()}catch(e){n(e)}})},35244:(e,t)=>{"use strict";var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},7443:(e,t,r)=>{"use strict";e.exports=r(87093).vendored.contexts.AppRouterContext},27583:(e,t,r)=>{"use strict";e.exports=r(87093).vendored.contexts.HooksClientContext},31575:(e,t,r)=>{"use strict";e.exports=r(87093).vendored.contexts.ServerInsertedHtml},39332:(e,t,r)=>{e.exports=r(70031)},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},62785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{"use strict";e.exports=require("react")},14661:e=>{"use strict";e.exports=require("react-router-dom")},20997:e=>{"use strict";e.exports=require("react/jsx-runtime")},55315:e=>{"use strict";e.exports=require("path")},1656:e=>{"use strict";e.exports=import("@hookform/resolvers/zod")},11601:e=>{"use strict";e.exports=import("@radix-ui/react-checkbox")},14338:e=>{"use strict";e.exports=import("@radix-ui/react-slot")},16926:e=>{"use strict";e.exports=import("class-variance-authority")},16593:e=>{"use strict";e.exports=import("clsx")},45641:e=>{"use strict";e.exports=import("react-hook-form")},37270:e=>{"use strict";e.exports=import("sonner")},68097:e=>{"use strict";e.exports=import("tailwind-merge")},9926:e=>{"use strict";e.exports=import("zod")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1682],()=>r(30657));module.exports=n})();