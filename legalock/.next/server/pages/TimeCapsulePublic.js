"use strict";(()=>{var e={};e.id=4566,e.ids=[4566,660],e.modules={53458:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>O,default:()=>h,getServerSideProps:()=>x,getStaticPaths:()=>p,getStaticProps:()=>f,reportWebVitals:()=>v,routeModule:()=>g,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>N,unstable_getStaticProps:()=>j});var n=r(87093),o=r(35244),s=r(1323),c=r(61682),i=r.n(c),d=r(48141),l=r.n(d),u=r(87607),m=e([u]);u=(m.then?(await m)():m)[0];let h=(0,s.l)(u,"default"),f=(0,s.l)(u,"getStaticProps"),p=(0,s.l)(u,"getStaticPaths"),x=(0,s.l)(u,"getServerSideProps"),O=(0,s.l)(u,"config"),v=(0,s.l)(u,"reportWebVitals"),j=(0,s.l)(u,"unstable_getStaticProps"),N=(0,s.l)(u,"unstable_getStaticPaths"),y=(0,s.l)(u,"unstable_getStaticParams"),b=(0,s.l)(u,"unstable_getServerProps"),w=(0,s.l)(u,"unstable_getServerSideProps"),g=new n.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/TimeCapsulePublic",pathname:"/TimeCapsulePublic",bundlePath:"",filename:""},components:{App:l(),Document:i()},userland:u});a()}catch(e){a(e)}})},11959:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},14132:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},13682:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{Z:()=>f});var n=r(20997),o=r(16689),s=r(70550);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var c=r(95364),i=r(71180),d=r(21427),l=r(63795),u=r(30625),m=r(37270);!function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();var h=e([c,m]);[c,m]=h.then?(await h)():h;let f=({capsule:e,onBack:t,accessCode:r,isPublicView:a=!1})=>{let[h,f]=(0,o.useState)([]),[p,x]=(0,o.useState)(!0),[O,v]=(0,o.useState)(!a);(0,o.useEffect)(()=>{if(a){if(r===e.access_code)v(!0);else{v(!1);return}}(async()=>{if(e?.id)try{x(!0);let{data:t,error:r}=await s.O.from("time_capsule_media").select("*").eq("capsule_id",e.id);if(r)throw r;t&&f(t)}catch(e){console.error("Error fetching media:",e),m.toast.error("Failed to load attachments")}finally{x(!1)}})()},[e,r,a]);let j=e=>n.jsx(i.Z,{className:"h-4 w-4 text-gray-500 mr-2"}),N=async(e,t)=>{try{let{data:r,error:a}=await s.O.storage.from("time_capsule_media").download(e);if(a)throw a;let n=URL.createObjectURL(r),o=document.createElement("a");o.href=n,o.download=t,document.body.appendChild(o),o.click(),URL.revokeObjectURL(n),document.body.removeChild(o)}catch(e){console.error("Error downloading file:",e),m.toast.error("Failed to download file")}};return a&&!O?(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"max-w-2xl mx-auto",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Access Denied"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"The access code is incorrect or has expired."})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("p",{children:"You need a valid access code to view this time capsule. Please check the link in the email you received."})})]}):(0,n.jsxs)("div",{children:[!a&&(0,n.jsxs)(c.z,{variant:"ghost",className:"mb-4",onClick:t,children:[n.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"max-w-3xl mx-auto",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e.title}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a?(0,n.jsxs)(n.Fragment,{children:["From: ",e.sender_name||"Someone who cares about you",n.jsx("br",{}),"Delivered: ",(0,u.WU)(new Date(e.delivery_date),"MMMM d, yyyy")]}):(0,n.jsxs)(n.Fragment,{children:["To: ",e.recipient_name," (",e.recipient_email,")",n.jsx("br",{}),"scheduled"===e.status?`To be delivered on: ${(0,u.WU)(new Date(e.delivery_date),"MMMM d, yyyy")}`:`${"delivered"===e.status?"Delivered":"Cancelled"} on: ${(0,u.WU)(new Date(e.delivery_date),"MMMM d, yyyy")}`]})})]}),!a&&n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:(e=>{switch(e){case"scheduled":return"bg-blue-50";case"delivered":return"bg-green-50";case"cancelled":return"bg-gray-50";default:return""}})(e.status),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-lg font-medium mb-2",children:"Message"}),n.jsx("div",{className:"bg-gray-50 p-4 rounded min-h-[100px] whitespace-pre-wrap",children:e.message||"No message content"})]}),(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-lg font-medium mb-2",children:"Attachments"}),p?n.jsx("p",{className:"text-gray-500",children:"Loading attachments..."}):0===h.length?n.jsx("p",{className:"text-gray-500",children:"No attachments"}):n.jsx("div",{className:"space-y-2",children:h.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[j(e.media_type),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-medium text-sm",children:e.file_name}),(0,n.jsxs)("p",{className:"text-gray-500 text-xs",children:[(e.file_size/1024/1024).toFixed(2)," MB"]})]})]}),(0,n.jsxs)(c.z,{size:"sm",variant:"outline",onClick:()=>N(e.file_path,e.file_name),children:[n.jsx(l.Z,{className:"h-4 w-4 mr-1"}),"Download"]})]},e.id))})]})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a?(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["This time capsule was created for you and delivered on ",(0,u.WU)(new Date(e.delivery_date),"MMMM d, yyyy")]}):(0,n.jsxs)(c.z,{variant:"outline",onClick:t,children:[n.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Back to Time Capsules"]})})]})]})};a()}catch(e){a(e)}})},95364:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{z:()=>u});var n=r(20997),o=r(16689),s=r(14338),c=r(16926),i=r(27742),d=e([s,c,i]);[s,c,i]=d.then?(await d)():d;let l=(0,c.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},c)=>{let d=a?s.Slot:"button";return n.jsx(d,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:c,...o})});u.displayName="Button",a()}catch(e){a(e)}})},87095:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{I:()=>i});var n=r(20997),o=r(16689),s=r(27742),c=e([s]);s=(c.then?(await c)():c)[0];let i=o.forwardRef(({className:e,type:t,...r},a)=>n.jsx("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));i.displayName="Input",a()}catch(e){a(e)}})},7825:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{_:()=>u});var n=r(20997),o=r(16689),s=r(10049),c=r(16926),i=r(27742),d=e([s,c,i]);[s,c,i]=d.then?(await d)():d;let l=(0,c.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=o.forwardRef(({className:e,...t},r)=>n.jsx(s.Root,{ref:r,className:(0,i.cn)(l(),e),...t}));u.displayName=s.Root.displayName,a()}catch(e){a(e)}})},70550:(e,t,r)=>{r.d(t,{O:()=>a});let a=(0,r(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{cn:()=>c});var n=r(16593),o=r(68097),s=e([n,o]);function c(...e){return(0,o.twMerge)((0,n.clsx)(e))}[n,o]=s.then?(await s)():s,a()}catch(e){a(e)}})},87607:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>O});var n=r(20997),o=r(16689),s=r(14661),c=r(70550);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(95364),d=r(87095),l=r(7825),u=r(37270),m=r(13682),h=r(18742),f=r(11959),p=r(14132),x=e([i,d,l,u,m]);[i,d,l,u,m]=x.then?(await x)():x;let O=()=>{let{id:e}=(0,s.useParams)(),t=(0,s.useLocation)(),[r,a]=(0,o.useState)(null),[x,O]=(0,o.useState)(""),[v,j]=(0,o.useState)(!0),[N,y]=(0,o.useState)(!1),[b,w]=(0,o.useState)(null);return(0,o.useEffect)(()=>{let e=new URLSearchParams(t.search).get("code");e&&O(e)},[t]),(0,o.useEffect)(()=>{(async()=>{if(e)try{j(!0),w(null);let{data:t,error:r}=await c.O.from("time_capsules").select("*").eq("id",e).eq("status","delivered").single();if(r){"PGRST116"===r.code?w("Time capsule not found or is not yet delivered"):w("Failed to fetch time capsule");return}a(t),x&&t.access_code===x&&y(!0)}catch(e){console.error("Error fetching time capsule:",e),w("Failed to fetch time capsule")}finally{j(!1)}})()},[e,x]),n.jsx("div",{className:"min-h-screen bg-gray-50 p-4 py-8",children:n.jsx("div",{className:"max-w-4xl mx-auto",children:v?(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full max-w-md mx-auto",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Loading Time Capsule"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Please wait while we retrieve your time capsule..."})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex justify-center py-8",children:n.jsx(h.Z,{className:"h-16 w-16 text-gray-400 animate-pulse"})})]}):b||!r?(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full max-w-md mx-auto",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Time Capsule Not Found"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:b||"This time capsule does not exist or has not been delivered yet."})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex justify-center py-8",children:n.jsx(f.Z,{className:"h-16 w-16 text-gray-400"})})]}):N?n.jsx(m.Z,{capsule:r,onBack:()=>{},accessCode:x,isPublicView:!0}):(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full max-w-md mx-auto",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Protected Time Capsule"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Please enter the access code to view this time capsule"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[n.jsx("div",{className:"flex justify-center py-4",children:n.jsx(p.Z,{className:"h-16 w-16 text-gray-400"})}),(0,n.jsxs)("div",{className:"space-y-2",children:[n.jsx(l._,{htmlFor:"accessCode",children:"Access Code"}),n.jsx(d.I,{id:"accessCode",type:"text",placeholder:"Enter the access code",value:x,onChange:e=>O(e.target.value)}),n.jsx("p",{className:"text-xs text-gray-500",children:"The access code was included in the email notification you received."})]})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(i.z,{className:"w-full",onClick:()=>{x===r.access_code?y(!0):u.toast.error("Invalid access code")},children:"Access Time Capsule"})})]})})})};a()}catch(e){a(e)}})},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},10049:e=>{e.exports=import("@radix-ui/react-label")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1682,7537],()=>r(53458));module.exports=a})();