"use strict";(()=>{var e={};e.id=7655,e.ids=[7655,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},58474:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>v,default:()=>h,getServerSideProps:()=>O,getStaticPaths:()=>p,getStaticProps:()=>f,reportWebVitals:()=>x,routeModule:()=>E,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>N,unstable_getStaticProps:()=>g});var o=r(87093),a=r(35244),s=r(1323),i=r(61682),c=r.n(i),d=r(48141),l=r.n(d),u=r(54300),m=e([u]);u=(m.then?(await m)():m)[0];let h=(0,s.l)(u,"default"),f=(0,s.l)(u,"getStaticProps"),p=(0,s.l)(u,"getStaticPaths"),O=(0,s.l)(u,"getServerSideProps"),v=(0,s.l)(u,"config"),x=(0,s.l)(u,"reportWebVitals"),g=(0,s.l)(u,"unstable_getStaticProps"),N=(0,s.l)(u,"unstable_getStaticPaths"),j=(0,s.l)(u,"unstable_getStaticParams"),b=(0,s.l)(u,"unstable_getServerProps"),_=(0,s.l)(u,"unstable_getServerSideProps"),E=new o.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/Trustees",pathname:"/Trustees",bundlePath:"",filename:""},components:{App:l(),Document:c()},userland:u});n()}catch(e){n(e)}})},33592:(e,t,r)=>{r.d(t,{Z:()=>c});var n=r(16689);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:i="",children:c,iconNode:d,...l},u)=>(0,n.createElement)("svg",{ref:u,...s,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:a("lucide",i),...l},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...s},c)=>(0,n.createElement)(i,{ref:c,iconNode:t,className:a(`lucide-${o(e)}`,r),...s}));return r.displayName=`${e}`,r}},80906:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},76211:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},33656:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},22972:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},48141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(50167),o=r(20997),a=n._(r(16689)),s=r(45782);async function i(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,s.loadGetInitialProps)(t,r)}}class c extends a.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}c.origGetInitialProps=i,c.getInitialProps=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95364:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{z:()=>u});var o=r(20997),a=r(16689),s=r(14338),i=r(16926),c=r(27742),d=e([s,i,c]);[s,i,c]=d.then?(await d)():d;let l=(0,i.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...a},i)=>{let d=n?s.Slot:"button";return o.jsx(d,{className:(0,c.cn)(l({variant:t,size:r,className:e})),ref:i,...a})});u.displayName="Button",n()}catch(e){n(e)}})},27742:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{cn:()=>i});var o=r(16593),a=r(68097),s=e([o,a]);function i(...e){return(0,a.twMerge)((0,o.clsx)(e))}[o,a]=s.then?(await s)():s,n()}catch(e){n(e)}})},54300:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>h});var o=r(20997),a=r(16689),s=r(14661),i=r(76211),c=r(33656),d=r(22972),l=r(80906),u=r(95364);(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}();var m=e([u]);u=(m.then?(await m)():m)[0];let h=()=>{let e=(0,s.useNavigate)(),[t,r]=(0,a.useState)(!1),[n,m]=(0,a.useState)(null),h=e=>{m(e),r(!0)};return(0,o.jsxs)("div",{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Trustees",description:"Manage who will have access to your digital legacy.",actions:(0,o.jsxs)(u.z,{onClick:()=>{e("/trustees/invite")},children:[o.jsx(i.Z,{className:"mr-2 h-4 w-4"}),"Invite Trustee"]})}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"my-trustees",className:"mt-6",children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"my-trustees",children:"My Trustees"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"trustee-for",children:"Trustee For"})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"my-trustees",className:"mt-6",children:o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{id:1,name:"Sarah Johnson",email:"<EMAIL>",relationship:"Sister",status:"active",added:"3 months ago",permissions:["Assets","Vault","Will"]},{id:2,name:"Michael Chen",email:"<EMAIL>",relationship:"Friend",status:"pending",added:"1 week ago",permissions:["Assets","Vault"]}].map(e=>(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-12 w-12",children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{src:""}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-legalock-gray-200 text-legalock-gray-700",children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,o.jsxs)("div",{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg",children:e.name}),o.jsx("p",{className:"text-sm text-gray-500",children:e.relationship})]})]}),"active"===e.status?o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-green-500",children:"Active"}):o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"text-amber-500 border-amber-300",children:"Pending"})]})}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)("div",{className:"flex items-center mb-3",children:[o.jsx(c.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),o.jsx("span",{className:"text-sm",children:e.email})]}),(0,o.jsxs)("div",{className:"mt-4",children:[o.jsx("p",{className:"text-sm font-medium mb-2",children:"Access Permissions:"}),o.jsx("div",{className:"flex flex-wrap gap-2",children:e.permissions.map(e=>o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:e},e))})]})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"border-t border-gray-100 pt-4 flex justify-between",children:[(0,o.jsxs)("span",{className:"text-xs text-gray-500",children:["Added ",e.added]}),(0,o.jsxs)("div",{className:"space-x-2",children:["pending"===e.status?(0,o.jsxs)(u.z,{variant:"outline",size:"sm",children:["Resend ",o.jsx(c.Z,{className:"ml-1 h-3 w-3"})]}):o.jsx(u.z,{variant:"outline",size:"sm",children:"Edit"}),o.jsx(u.z,{variant:"destructive",size:"sm",onClick:()=>h(e),children:"Revoke"})]})]})]},e.id))})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"trustee-for",className:"mt-6",children:o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{id:101,name:"Robert Wilson",email:"<EMAIL>",relationship:"Brother",status:"active",added:"2 months ago"}].map(e=>(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-12 w-12",children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{src:""}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-legalock-gray-200 text-legalock-gray-700",children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,o.jsxs)("div",{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg",children:e.name}),o.jsx("p",{className:"text-sm text-gray-500",children:e.relationship})]})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-green-500",children:"Active"})]})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,o.jsxs)("div",{className:"flex items-center mb-3",children:[o.jsx(c.Z,{className:"h-4 w-4 mr-2 text-gray-400"}),o.jsx("span",{className:"text-sm",children:e.email})]})}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"border-t border-gray-100 pt-4 flex justify-between",children:[(0,o.jsxs)("span",{className:"text-xs text-gray-500",children:["Added ",e.added]}),o.jsx(u.z,{variant:"outline",size:"sm",children:"View Details"})]})]},e.id))})})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{open:t,onOpenChange:r,children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Revoke Trustee Access"}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Are you sure you want to revoke ",n?.name,"'s trustee access? They will no longer have access to your digital legacy."]})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(u.z,{variant:"outline",onClick:()=>r(!1),children:[o.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,o.jsxs)(u.z,{variant:"destructive",onClick:()=>{console.log("Revoking trustee:",n),r(!1)},children:[o.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Confirm Revoke"]})]})]})})]})};n()}catch(e){n(e)}})},35244:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1682],()=>r(58474));module.exports=n})();