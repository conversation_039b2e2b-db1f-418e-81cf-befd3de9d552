"use strict";(()=>{var e={};e.id=86,e.ids=[86,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},10038:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>x,default:()=>f,getServerSideProps:()=>O,getStaticPaths:()=>h,getStaticProps:()=>p,reportWebVitals:()=>v,routeModule:()=>w,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>b});var o=r(87093),a=r(35244),s=r(1323),i=r(61682),c=r.n(i),d=r(48141),l=r.n(d),u=r(83819),m=e([u]);u=(m.then?(await m)():m)[0];let f=(0,s.l)(u,"default"),p=(0,s.l)(u,"getStaticProps"),h=(0,s.l)(u,"getStaticPaths"),O=(0,s.l)(u,"getServerSideProps"),x=(0,s.l)(u,"config"),v=(0,s.l)(u,"reportWebVitals"),b=(0,s.l)(u,"unstable_getStaticProps"),g=(0,s.l)(u,"unstable_getStaticPaths"),N=(0,s.l)(u,"unstable_getStaticParams"),j=(0,s.l)(u,"unstable_getServerProps"),_=(0,s.l)(u,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/Settings",pathname:"/Settings",bundlePath:"",filename:""},components:{App:l(),Document:c()},userland:u});n()}catch(e){n(e)}})},48141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(50167),o=r(20997),a=n._(r(16689)),s=r(45782);async function i(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,s.loadGetInitialProps)(t,r)}}class c extends a.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}c.origGetInitialProps=i,c.getInitialProps=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95364:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{z:()=>u});var o=r(20997),a=r(16689),s=r(14338),i=r(16926),c=r(27742),d=e([s,i,c]);[s,i,c]=d.then?(await d)():d;let l=(0,i.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...a},i)=>{let d=n?s.Slot:"button";return o.jsx(d,{className:(0,c.cn)(l({variant:t,size:r,className:e})),ref:i,...a})});u.displayName="Button",n()}catch(e){n(e)}})},87095:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{I:()=>c});var o=r(20997),a=r(16689),s=r(27742),i=e([s]);s=(i.then?(await i)():i)[0];let c=a.forwardRef(({className:e,type:t,...r},n)=>o.jsx("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));c.displayName="Input",n()}catch(e){n(e)}})},7825:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{_:()=>u});var o=r(20997),a=r(16689),s=r(10049),i=r(16926),c=r(27742),d=e([s,i,c]);[s,i,c]=d.then?(await d)():d;let l=(0,i.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=a.forwardRef(({className:e,...t},r)=>o.jsx(s.Root,{ref:r,className:(0,c.cn)(l(),e),...t}));u.displayName=s.Root.displayName,n()}catch(e){n(e)}})},77415:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{a:()=>c}),r(20997);var o=r(16689);r(70550),r(14661);var a=r(37270),s=e([a]);a=(s.then?(await s)():s)[0];let i=(0,o.createContext)(void 0),c=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};n()}catch(e){n(e)}})},70550:(e,t,r)=>{r.d(t,{O:()=>n});let n=(0,r(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{cn:()=>i});var o=r(16593),a=r(68097),s=e([o,a]);function i(...e){return(0,a.twMerge)((0,o.clsx)(e))}[o,a]=s.then?(await s)():s,n()}catch(e){n(e)}})},83819:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>l});var o=r(20997);r(16689),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var a=r(95364),s=r(87095),i=r(7825),c=r(77415),d=e([a,s,i,c]);[a,s,i,c]=d.then?(await d)():d;let l=()=>{let{user:e}=(0,c.a)();return(0,o.jsxs)("div",{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Settings",description:"Manage your account settings and preferences."}),o.jsx("div",{className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"profile",className:"w-full",children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid w-full md:w-auto grid-cols-3 md:inline-flex",children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"profile",children:"Profile"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"account",children:"Account"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"notifications",children:"Notifications"})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"profile",className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Profile Information"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Update your personal information."})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx(i._,{htmlFor:"firstName",children:"First Name"}),o.jsx(s.I,{id:"firstName",defaultValue:e?.user_metadata?.first_name||"",placeholder:"Enter your first name"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx(i._,{htmlFor:"lastName",children:"Last Name"}),o.jsx(s.I,{id:"lastName",defaultValue:e?.user_metadata?.last_name||"",placeholder:"Enter your last name"})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx(i._,{htmlFor:"email",children:"Email Address"}),o.jsx(s.I,{id:"email",type:"email",value:e?.email||"",readOnly:!0,className:"bg-gray-50"}),o.jsx("p",{className:"text-sm text-gray-500",children:"Your email cannot be changed after registration."})]}),o.jsx(a.z,{className:"mt-4",children:"Save Changes"})]})]})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"account",className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Account Settings"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Manage your account password and security."})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx(i._,{htmlFor:"currentPassword",children:"Current Password"}),o.jsx(s.I,{id:"currentPassword",type:"password",placeholder:"Enter your current password"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx(i._,{htmlFor:"newPassword",children:"New Password"}),o.jsx(s.I,{id:"newPassword",type:"password",placeholder:"Enter your new password"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx(i._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),o.jsx(s.I,{id:"confirmPassword",type:"password",placeholder:"Confirm your new password"})]}),o.jsx(a.z,{className:"mt-4",children:"Update Password"}),(0,o.jsxs)("div",{className:"pt-6 border-t mt-6",children:[o.jsx("h3",{className:"text-lg font-medium text-red-600",children:"Danger Zone"}),o.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Permanently delete your account and all of your content."}),o.jsx(a.z,{variant:"destructive",className:"mt-4",children:"Delete Account"})]})]})]})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"notifications",className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Notification Preferences"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Configure how you receive notifications."})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:o.jsx("p",{className:"text-sm text-gray-500",children:"Notification settings will be available soon."})})]})})]})})]})};n()}catch(e){n(e)}})},35244:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},10049:e=>{e.exports=import("@radix-ui/react-label")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1682],()=>r(10038));module.exports=n})();