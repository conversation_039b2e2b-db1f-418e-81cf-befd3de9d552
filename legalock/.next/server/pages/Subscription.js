"use strict";(()=>{var e={};e.id=222,e.ids=[222,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},53379:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>p,getServerSideProps:()=>f,getStaticPaths:()=>x,getStaticProps:()=>h,reportWebVitals:()=>v,routeModule:()=>N,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>b});var n=r(87093),i=r(35244),s=r(1323),l=r(61682),o=r.n(l),c=r(48141),d=r.n(c),u=r(48243),m=e([u]);u=(m.then?(await m)():m)[0];let p=(0,s.l)(u,"default"),h=(0,s.l)(u,"getStaticProps"),x=(0,s.l)(u,"getStaticPaths"),f=(0,s.l)(u,"getServerSideProps"),g=(0,s.l)(u,"config"),v=(0,s.l)(u,"reportWebVitals"),b=(0,s.l)(u,"unstable_getStaticProps"),y=(0,s.l)(u,"unstable_getStaticPaths"),j=(0,s.l)(u,"unstable_getStaticParams"),w=(0,s.l)(u,"unstable_getServerProps"),P=(0,s.l)(u,"unstable_getServerSideProps"),N=new n.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/Subscription",pathname:"/Subscription",bundlePath:"",filename:""},components:{App:d(),Document:o()},userland:u});a()}catch(e){a(e)}})},33592:(e,t,r)=>{r.d(t,{Z:()=>o});var a=r(16689);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:o,iconNode:c,...d},u)=>(0,a.createElement)("svg",{ref:u,...s,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:i("lucide",l),...d},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(o)?o:[o]])),o=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...s},o)=>(0,a.createElement)(l,{ref:o,iconNode:t,className:i(`lucide-${n(e)}`,r),...s}));return r.displayName=`${e}`,r}},47340:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11959:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},92551:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let a=r(50167),n=r(20997),i=a._(r(16689)),s=r(45782);async function l(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,s.loadGetInitialProps)(t,r)}}class o extends i.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,n.jsx)(e,{...t})}}o.origGetInitialProps=l,o.getInitialProps=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16020:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{Z:()=>h});var n=r(20997),i=r(16689),s=r(11092);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var l=r(95364),o=r(92551),c=r(47340),d=r(11959),u=r(37270),m=e([s,l,u]);[s,l,u]=m.then?(await m)():m;let p=[{name:"Free",price:"$0",period:"forever",description:"Basic digital legacy management for individuals",features:[{name:"Up to 20 assets",included:!0},{name:"1GB vault storage",included:!0},{name:"5 time capsule messages",included:!0},{name:"Basic will template",included:!0},{name:"Up to 2 trustees",included:!0},{name:"Premium encryption",included:!1},{name:"Priority support",included:!1},{name:"Legal document review",included:!1}],buttonText:"Current Plan",buttonVariant:"outline",planId:"free"},{name:"Premium",price:"$9.99",period:"per month",description:"Comprehensive legacy planning for families",features:[{name:"Unlimited assets",included:!0},{name:"10GB vault storage",included:!0},{name:"Unlimited time capsule messages",included:!0},{name:"Advanced will templates",included:!0},{name:"Up to 10 trustees",included:!0},{name:"Premium encryption",included:!0},{name:"Priority support",included:!0},{name:"Legal document review",included:!1}],buttonText:"Upgrade to Premium",buttonVariant:"default",highlight:!0,planId:"premium"},{name:"Family",price:"$19.99",period:"per month",description:"Complete solution for extended families",features:[{name:"Unlimited assets",included:!0},{name:"25GB vault storage",included:!0},{name:"Unlimited time capsule messages",included:!0},{name:"All will templates",included:!0},{name:"Unlimited trustees",included:!0},{name:"Premium encryption",included:!0},{name:"Priority support",included:!0},{name:"Legal document review",included:!0}],buttonText:"Upgrade to Family",buttonVariant:"default",planId:"family"}],h=()=>{let{plan:e,isLoading:t,createCheckout:r}=(0,s.m)(),[a,m]=(0,i.useState)(null),h=async e=>{try{m(e);let t=await r(e);t&&(window.location.href=t)}catch(e){u.toast.error("Failed to initiate checkout process"),console.error("Checkout error:",e)}finally{m(null)}};return t?(0,n.jsxs)("div",{className:"flex justify-center items-center py-20",children:[n.jsx(o.Z,{className:"h-8 w-8 animate-spin text-primary"}),n.jsx("span",{className:"ml-2",children:"Loading subscription information..."})]}):n.jsx("div",{className:"grid gap-8 md:grid-cols-1 lg:grid-cols-3",children:p.map(t=>{let r=t.planId===e,i=a===t.planId;return(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`flex flex-col ${t.highlight?"border-primary shadow-lg relative":""}`,children:[t.highlight&&n.jsx("div",{className:"absolute top-0 left-0 right-0 transform -translate-y-1/2 flex justify-center",children:n.jsx("span",{className:"bg-primary text-white text-sm px-4 py-1 rounded-full font-medium",children:"Most Popular"})}),r&&n.jsx("div",{className:"absolute top-0 right-0 mt-4 mr-4",children:n.jsx("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium",children:"Current Plan"})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:t.name}),(0,n.jsxs)("div",{className:"flex items-baseline mt-2",children:[n.jsx("span",{className:"text-3xl font-extrabold",children:t.price}),(0,n.jsxs)("span",{className:"ml-1 text-gray-500",children:["/",t.period]})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mt-2",children:t.description})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:n.jsx("ul",{className:"space-y-3",children:t.features.map((e,t)=>(0,n.jsxs)("li",{className:"flex items-start",children:[e.included?n.jsx(c.Z,{className:"h-5 w-5 text-green-500 flex-shrink-0 mr-2"}):n.jsx(d.Z,{className:"h-5 w-5 text-gray-400 flex-shrink-0 mr-2"}),n.jsx("span",{className:e.included?"text-gray-900":"text-gray-500",children:e.name})]},t))})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"free"===t.planId?n.jsx(l.z,{variant:t.buttonVariant,className:"w-full",disabled:!0,children:"Free Plan"}):r?n.jsx(l.z,{variant:"outline",className:"w-full",disabled:!0,children:"Current Plan"}):n.jsx(l.z,{variant:t.buttonVariant,className:"w-full",onClick:()=>h(t.planId),disabled:i,children:i?(0,n.jsxs)(n.Fragment,{children:[n.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):t.buttonText})})]},t.name)})})};a()}catch(e){a(e)}})},95364:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{z:()=>u});var n=r(20997),i=r(16689),s=r(14338),l=r(16926),o=r(27742),c=e([s,l,o]);[s,l,o]=c.then?(await c)():c;let d=(0,l.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},l)=>{let c=a?s.Slot:"button";return n.jsx(c,{className:(0,o.cn)(d({variant:t,size:r,className:e})),ref:l,...i})});u.displayName="Button",a()}catch(e){a(e)}})},77415:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{a:()=>o}),r(20997);var n=r(16689);r(70550),r(14661);var i=r(37270),s=e([i]);i=(s.then?(await s)():s)[0];let l=(0,n.createContext)(void 0),o=()=>{let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};a()}catch(e){a(e)}})},11092:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{m:()=>c}),r(20997);var n=r(16689);r(70550);var i=r(77415),s=r(37270),l=e([i,s]);[i,s]=l.then?(await l)():l;let o=(0,n.createContext)(void 0),c=()=>{let e=(0,n.useContext)(o);if(void 0===e)throw Error("useSubscription must be used within a SubscriptionProvider");return e};a()}catch(e){a(e)}})},70550:(e,t,r)=>{r.d(t,{O:()=>a});let a=(0,r(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{cn:()=>l});var n=r(16593),i=r(68097),s=e([n,i]);function l(...e){return(0,i.twMerge)((0,n.clsx)(e))}[n,i]=s.then?(await s)():s,a()}catch(e){a(e)}})},48243:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>c});var n=r(20997);r(16689);var i=r(16020),s=r(11092),l=r(92551),o=e([i,s]);[i,s]=o.then?(await o)():o;let c=()=>{let{plan:e,isSubscribed:t,isLoading:r,subscriptionDetails:a}=(0,s.m)();return(0,n.jsxs)("div",{className:"container max-w-7xl mx-auto px-4 py-8",children:[(0,n.jsxs)("div",{className:"mb-10",children:[n.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Subscription Plans"}),n.jsx("p",{className:"text-gray-600",children:"Choose the plan that best fits your needs. Upgrade anytime to access more features."})]}),r?(0,n.jsxs)("div",{className:"flex items-center justify-center py-20",children:[n.jsx(l.Z,{className:"h-12 w-12 animate-spin text-primary"}),n.jsx("p",{className:"ml-4 text-lg text-gray-600",children:"Loading subscription details..."})]}):(0,n.jsxs)(n.Fragment,{children:[t&&(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 mb-8",children:[(0,n.jsxs)("h2",{className:"text-xl font-semibold text-green-800 mb-2",children:["Active Subscription: ",e.charAt(0).toUpperCase()+e.slice(1)," Plan"]}),a&&(0,n.jsxs)("p",{className:"text-green-700",children:["Your subscription renews on ",new Date(1e3*a.current_period_end).toLocaleDateString()]}),(0,n.jsxs)("p",{className:"mt-2 text-gray-600",children:["You have access to all ",e," features. To manage your subscription or billing, visit your account settings."]})]}),n.jsx(i.Z,{}),(0,n.jsxs)("div",{className:"mt-12 text-center text-sm text-gray-500",children:[n.jsx("p",{children:"All plans include a 14-day money-back guarantee if you're not satisfied."}),(0,n.jsxs)("p",{className:"mt-1",children:["Have questions about our plans? ",n.jsx("a",{href:"#",className:"text-primary hover:underline",children:"Contact our support team"}),"."]})]})]})]})};a()}catch(e){a(e)}})},35244:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1682],()=>r(53379));module.exports=a})();