"use strict";(()=>{var e={};e.id=6562,e.ids=[6562,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},96334:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{config:()=>v,default:()=>f,getServerSideProps:()=>p,getStaticPaths:()=>O,getStaticProps:()=>h,reportWebVitals:()=>x,routeModule:()=>w,unstable_getServerProps:()=>g,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var o=n(87093),a=n(35244),i=n(1323),s=n(61682),c=n.n(s),d=n(48141),l=n.n(d),u=n(94994),m=e([u]);u=(m.then?(await m)():m)[0];let f=(0,i.l)(u,"default"),h=(0,i.l)(u,"getStaticProps"),O=(0,i.l)(u,"getStaticPaths"),p=(0,i.l)(u,"getServerSideProps"),v=(0,i.l)(u,"config"),x=(0,i.l)(u,"reportWebVitals"),b=(0,i.l)(u,"unstable_getStaticProps"),j=(0,i.l)(u,"unstable_getStaticPaths"),N=(0,i.l)(u,"unstable_getStaticParams"),g=(0,i.l)(u,"unstable_getServerProps"),_=(0,i.l)(u,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/Will",pathname:"/Will",bundlePath:"",filename:""},components:{App:l(),Document:c()},userland:u});r()}catch(e){r(e)}})},33592:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(16689);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:s="",children:c,iconNode:d,...l},u)=>(0,r.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:a("lucide",s),...l},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...i},c)=>(0,r.createElement)(s,{ref:c,iconNode:t,className:a(`lucide-${o(e)}`,n),...i}));return n.displayName=`${e}`,n}},63795:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},69972:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},83917:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},48141:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let r=n(50167),o=n(20997),a=r._(n(16689)),i=n(45782);async function s(e){let{Component:t,ctx:n}=e;return{pageProps:await (0,i.loadGetInitialProps)(t,n)}}class c extends a.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}c.origGetInitialProps=s,c.getInitialProps=s,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95364:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{z:()=>u});var o=n(20997),a=n(16689),i=n(14338),s=n(16926),c=n(27742),d=e([i,s,c]);[i,s,c]=d.then?(await d)():d;let l=(0,s.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...a},s)=>{let d=r?i.Slot:"button";return o.jsx(d,{className:(0,c.cn)(l({variant:t,size:n,className:e})),ref:s,...a})});u.displayName="Button",r()}catch(e){r(e)}})},27742:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{cn:()=>s});var o=n(16593),a=n(68097),i=e([o,a]);function s(...e){return(0,a.twMerge)((0,o.clsx)(e))}[o,a]=i.then?(await i)():i,r()}catch(e){r(e)}})},94994:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{default:()=>l});var o=n(20997);n(16689);var a=n(95364),i=n(83917),s=n(69972),c=n(63795);(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();var d=e([a]);a=(d.then?(await d)():d)[0];let l=()=>(0,o.jsxs)("div",{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Will & Testament",description:"Create and manage your legal will and final wishes.",actions:(0,o.jsxs)(a.z,{children:[o.jsx(i.Z,{className:"mr-2 h-4 w-4"}),"Start Will Creation"]})}),o.jsx("div",{className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"overview",children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"overview",children:"Overview"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"documents",children:"Documents"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"witnesses",children:"Witnesses"})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"overview",className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Will Status"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Current status of your will and important information."})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)("div",{className:"flex items-center justify-between p-4 bg-amber-50 rounded-lg border border-amber-200",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[o.jsx("div",{className:"h-10 w-10 rounded-full bg-amber-200 flex items-center justify-center",children:o.jsx(i.Z,{className:"h-5 w-5 text-amber-700"})}),(0,o.jsxs)("div",{className:"ml-4",children:[o.jsx("h3",{className:"font-medium",children:"Will In Progress"}),o.jsx("p",{className:"text-sm text-gray-600",children:"You've started creating your will but haven't finalized it."})]})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-200",children:"Draft"})]}),(0,o.jsxs)("div",{className:"mt-6 space-y-4",children:[o.jsx("h3",{className:"font-medium",children:"Completion Progress"}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[o.jsx("span",{children:"Personal Details"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-green-50 text-green-700",children:"Completed"})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[o.jsx("span",{children:"Assets & Beneficiaries"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-amber-50 text-amber-700",children:"In Progress"})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[o.jsx("span",{children:"Executor Appointment"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-gray-100",children:"Not Started"})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[o.jsx("span",{children:"Witnesses & Signing"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-gray-100",children:"Not Started"})]})]})]})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:o.jsx(a.z,{children:"Continue Will Creation"})})]})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"documents",className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Will Documents"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Access and manage your will documents."})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:o.jsx("div",{className:"divide-y",children:(0,o.jsxs)("div",{className:"py-4 flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[o.jsx("h4",{className:"font-medium",children:"Will - Draft Version"}),o.jsx("p",{className:"text-sm text-gray-500",children:"Last updated: March 15, 2025"})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)(a.z,{size:"sm",variant:"outline",children:[o.jsx(s.Z,{className:"h-4 w-4 mr-1"}),"View"]}),(0,o.jsxs)(a.z,{size:"sm",variant:"outline",children:[o.jsx(c.Z,{className:"h-4 w-4 mr-1"}),"Download"]})]})]})})})]})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"witnesses",className:"mt-6",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Witnesses"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Manage witnesses for your will signing."})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Your will requires at least two witnesses who are not beneficiaries to be legally valid."}),o.jsx(a.z,{children:"Add Witnesses"})]})]})})]})})]});r()}catch(e){r(e)}})},35244:(e,t)=>{var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1682],()=>n(96334));module.exports=r})();