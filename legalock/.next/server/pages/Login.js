"use strict";(()=>{var e={};e.id=3667,e.ids=[3667,660],e.modules={9196:(e,s,t)=>{t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{config:()=>j,default:()=>p,getServerSideProps:()=>g,getStaticPaths:()=>h,getStaticProps:()=>u,reportWebVitals:()=>v,routeModule:()=>N,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>b});var a=t(87093),i=t(35244),l=t(1323),o=t(61682),n=t.n(o),c=t(48141),m=t.n(c),d=t(65327),x=e([d]);d=(x.then?(await x)():x)[0];let p=(0,l.l)(d,"default"),u=(0,l.l)(d,"getStaticProps"),h=(0,l.l)(d,"getStaticPaths"),g=(0,l.l)(d,"getServerSideProps"),j=(0,l.l)(d,"config"),v=(0,l.l)(d,"reportWebVitals"),b=(0,l.l)(d,"unstable_getStaticProps"),S=(0,l.l)(d,"unstable_getStaticPaths"),y=(0,l.l)(d,"unstable_getStaticParams"),f=(0,l.l)(d,"unstable_getServerProps"),w=(0,l.l)(d,"unstable_getServerSideProps"),N=new a.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/Login",pathname:"/Login",bundlePath:"",filename:""},components:{App:m(),Document:n()},userland:d});r()}catch(e){r(e)}})},60738:(e,s,t)=>{t.a(e,async(e,r)=>{try{t.d(s,{Z:()=>v});var a=t(20997),i=t(16689),l=t(14661),o=t(45641),n=t(1656),c=t(9926),m=t(95364),d=t(87095),x=t(99250),p=t(77415),u=t(82922),h=t(69972),g=e([o,n,c,m,d,p]);[o,n,c,m,d,p]=g.then?(await g)():g;let j=c.z.object({email:c.z.string().email({message:"Please enter a valid email address"}),password:c.z.string().min(8,{message:"Password must be at least 8 characters"})}),v=()=>{(0,l.useNavigate)();let{signIn:e}=(0,p.a)(),[s,t]=(0,i.useState)(!1),r=(0,o.useForm)({resolver:(0,n.zodResolver)(j),defaultValues:{email:"",password:""}}),c=async s=>{try{await e(s.email,s.password)}catch(e){console.error("Login error:",e)}},g=()=>{t(!s)};return(0,a.jsxs)("div",{children:[a.jsx(x.l0,{...r,children:(0,a.jsxs)("form",{onSubmit:r.handleSubmit(c),className:"space-y-6",children:[a.jsx(x.Wi,{control:r.control,name:"email",render:({field:e})=>(0,a.jsxs)(x.xJ,{children:[a.jsx(x.lX,{children:"Email"}),a.jsx(x.NI,{children:a.jsx(d.I,{placeholder:"<EMAIL>",type:"email",...e})}),a.jsx(x.zG,{})]})}),a.jsx(x.Wi,{control:r.control,name:"password",render:({field:e})=>(0,a.jsxs)(x.xJ,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(x.lX,{children:"Password"}),a.jsx(l.Link,{to:"/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})]}),a.jsx(x.NI,{children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(d.I,{placeholder:"••••••••",type:s?"text":"password",...e}),a.jsx(m.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-500",onClick:g,children:s?a.jsx(u.Z,{className:"h-4 w-4"}):a.jsx(h.Z,{className:"h-4 w-4"})})]})}),a.jsx(x.zG,{})]})}),a.jsx("div",{children:a.jsx(m.z,{type:"submit",className:"w-full",disabled:r.formState.isSubmitting,children:r.formState.isSubmitting?(0,a.jsxs)("span",{className:"flex items-center",children:[a.jsx("span",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"}),"Signing in..."]}):"Sign In"})})]})}),a.jsx("div",{className:"mt-6 text-center text-sm",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",a.jsx(l.Link,{to:"/register",className:"text-primary hover:underline",children:"Sign up"})]})})]})};r()}catch(e){r(e)}})},65327:(e,s,t)=>{t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{default:()=>n});var a=t(20997);t(16689);var i=t(60738),l=t(45597),o=e([i,l]);[i,l]=o.then?(await o)():o;let n=()=>(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-gray-50",children:[a.jsx(l.Z,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[a.jsx("h2",{className:"mt-6 text-2xl font-bold text-gray-900",children:"Sign in to your account"}),a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Secure access to your digital legacy management"})]}),a.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:a.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:a.jsx(i.Z,{})})})]})]});r()}catch(e){r(e)}})},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},1656:e=>{e.exports=import("@hookform/resolvers/zod")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},45641:e=>{e.exports=import("react-hook-form")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")},9926:e=>{e.exports=import("zod")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1682,8196],()=>t(9196));module.exports=r})();