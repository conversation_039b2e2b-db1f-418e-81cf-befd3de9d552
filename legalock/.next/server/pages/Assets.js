"use strict";(()=>{var e={};e.id=2988,e.ids=[2988,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},75224:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{config:()=>v,default:()=>h,getServerSideProps:()=>x,getStaticPaths:()=>f,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>O});var a=n(87093),o=n(35244),s=n(1323),i=n(61682),c=n.n(i),d=n(48141),l=n.n(d),u=n(7193),m=e([u]);u=(m.then?(await m)():m)[0];let h=(0,s.l)(u,"default"),p=(0,s.l)(u,"getStaticProps"),f=(0,s.l)(u,"getStaticPaths"),x=(0,s.l)(u,"getServerSideProps"),v=(0,s.l)(u,"config"),g=(0,s.l)(u,"reportWebVitals"),O=(0,s.l)(u,"unstable_getStaticProps"),j=(0,s.l)(u,"unstable_getStaticPaths"),N=(0,s.l)(u,"unstable_getStaticParams"),y=(0,s.l)(u,"unstable_getServerProps"),b=(0,s.l)(u,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/Assets",pathname:"/Assets",bundlePath:"",filename:""},components:{App:l(),Document:c()},userland:u});r()}catch(e){r(e)}})},33592:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(16689);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:i="",children:c,iconNode:d,...l},u)=>(0,r.createElement)("svg",{ref:u,...s,width:t,height:t,stroke:e,strokeWidth:a?24*Number(n)/Number(t):n,className:o("lucide",i),...l},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...s},c)=>(0,r.createElement)(i,{ref:c,iconNode:t,className:o(`lucide-${a(e)}`,n),...s}));return n.displayName=`${e}`,n}},76211:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},65784:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},44609:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]])},35131:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},92568:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},19990:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},66152:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},2461:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},48141:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let r=n(50167),a=n(20997),o=r._(n(16689)),s=n(45782);async function i(e){let{Component:t,ctx:n}=e;return{pageProps:await (0,s.loadGetInitialProps)(t,n)}}class c extends o.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,a.jsx)(e,{...t})}}c.origGetInitialProps=i,c.getInitialProps=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76139:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{Z:()=>h});var a=n(20997),o=n(16689),s=n(49752),i=n(95364);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var c=n(2461),d=n(19990),l=n(37270),u=n(70550),m=e([s,i,l]);[s,i,l]=m.then?(await m)():m;let h=({assets:e})=>{let[t,n]=(0,o.useState)(""),{data:r,isLoading:m,error:h,refetch:p}=(0,s.useQuery)({queryKey:["estateRecommendations",e.map(e=>e.id).join(",")],queryFn:async()=>{try{let{data:n,error:r}=await u.O.functions.invoke("estate-recommendations",{body:{assets:e,context:t}});if(r)throw r;return n.recommendations}catch(e){throw console.error("Error fetching recommendations:",e),l.toast.error("Failed to get estate recommendations"),e}},enabled:e.length>0}),f=()=>{p(),l.toast.info("Refreshing recommendations...")};return(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mt-6",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Estate Planning Recommendations"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"AI-powered recommendations based on your assets and estate"})]}),a.jsx(i.z,{variant:"outline",size:"icon",onClick:f,disabled:m,children:a.jsx(c.Z,{className:`h-4 w-4 ${m?"animate-spin":""}`})})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:m?a.jsx("div",{className:"flex items-center justify-center p-6",children:(0,a.jsxs)("div",{className:"animate-pulse text-center",children:[a.jsx("div",{className:"h-6 w-32 bg-gray-200 rounded mx-auto mb-2"}),a.jsx("div",{className:"h-4 w-48 bg-gray-200 rounded mx-auto"})]})}):h?(0,a.jsxs)("div",{className:"text-center p-6 text-red-500",children:[a.jsx("p",{children:"Failed to load recommendations"}),a.jsx(i.z,{variant:"outline",className:"mt-2",onClick:f,children:"Try Again"})]}):r&&r.length>0?a.jsx("div",{className:"space-y-4",children:r.map((e,t)=>(0,a.jsxs)("div",{className:"flex p-4 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"mr-4 mt-1",children:a.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-100",children:a.jsx(d.Z,{className:"h-4 w-4 text-blue-700"})})}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium",children:e.title}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]})]},t))}):(0,a.jsxs)("div",{className:"text-center p-6 text-gray-500",children:[a.jsx("p",{children:"No recommendations available yet"}),a.jsx(i.z,{variant:"outline",className:"mt-2",onClick:f,children:"Generate Recommendations"})]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-col items-start",children:[a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Provide additional context to get more tailored recommendations:"}),a.jsx("textarea",{className:"w-full p-2 border rounded-md text-sm",rows:2,placeholder:"E.g., I have two children and want to minimize estate taxes",value:t,onChange:e=>n(e.target.value)}),a.jsx(i.z,{className:"mt-2",onClick:f,disabled:m,children:"Update Recommendations"})]})]})};r()}catch(e){r(e)}})},95364:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{z:()=>u});var a=n(20997),o=n(16689),s=n(14338),i=n(16926),c=n(27742),d=e([s,i,c]);[s,i,c]=d.then?(await d)():d;let l=(0,i.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{let d=r?s.Slot:"button";return a.jsx(d,{className:(0,c.cn)(l({variant:t,size:n,className:e})),ref:i,...o})});u.displayName="Button",r()}catch(e){r(e)}})},70550:(e,t,n)=>{n.d(t,{O:()=>r});let r=(0,n(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{cn:()=>i});var a=n(16593),o=n(68097),s=e([a,o]);function i(...e){return(0,o.twMerge)((0,a.clsx)(e))}[a,o]=s.then?(await s)():s,r()}catch(e){r(e)}})},7193:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{default:()=>f});var a=n(20997);n(16689);var o=n(14661),s=n(35131),i=n(65784),c=n(92568),d=n(44609),l=n(66152),u=n(76211),m=n(95364);(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var h=n(76139),p=e([m,h]);[m,h]=p.then?(await p)():p;let f=()=>{let e=(0,o.useNavigate)(),t=[{id:1,name:"Bitcoin Wallet",category:"Cryptocurrency",value:"$12,450",icon:a.jsx(s.Z,{className:"h-5 w-5"})},{id:2,name:"Banking Login",category:"Financial Account",value:"$28,930",icon:a.jsx(i.Z,{className:"h-5 w-5"})},{id:3,name:"Google Account",category:"Digital Account",value:"N/A",icon:a.jsx(s.Z,{className:"h-5 w-5"})}],n=[{id:4,name:"Primary Residence",category:"Real Estate",value:"$450,000",icon:a.jsx(c.Z,{className:"h-5 w-5"})},{id:5,name:"Wedding Ring",category:"Jewelry",value:"$5,000",icon:a.jsx(d.Z,{className:"h-5 w-5"})},{id:6,name:"Car - Toyota Camry",category:"Vehicle",value:"$18,000",icon:a.jsx(l.Z,{className:"h-5 w-5"})}],r=[...t,...n];return(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Asset Management",description:"Manage your digital and physical assets in one secure place.",actions:(0,a.jsxs)(m.z,{onClick:()=>{e("/assets")},children:[a.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Add Asset"]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"all",className:"mt-6",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",children:"All Assets"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"digital",children:"Digital"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"physical",children:"Physical"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"recommendations",children:"Recommendations"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",className:"mt-6",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg flex items-center justify-between",children:[a.jsx("span",{children:e.name}),a.jsx("div",{className:"bg-gray-100 p-2 rounded-md",children:e.icon})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("div",{className:"text-sm text-gray-500",children:e.category}),a.jsx("div",{className:"font-medium",children:e.value})]})})]},e.id))})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"digital",className:"mt-6",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg flex items-center justify-between",children:[a.jsx("span",{children:e.name}),a.jsx("div",{className:"bg-gray-100 p-2 rounded-md",children:e.icon})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("div",{className:"text-sm text-gray-500",children:e.category}),a.jsx("div",{className:"font-medium",children:e.value})]})})]},e.id))})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"physical",className:"mt-6",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(e=>(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg flex items-center justify-between",children:[a.jsx("span",{children:e.name}),a.jsx("div",{className:"bg-gray-100 p-2 rounded-md",children:e.icon})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("div",{className:"text-sm text-gray-500",children:e.category}),a.jsx("div",{className:"font-medium",children:e.value})]})})]},e.id))})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"recommendations",className:"mt-6",children:a.jsx(h.Z,{assets:r})})]})]})};r()}catch(e){r(e)}})},35244:(e,t)=>{var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},49752:e=>{e.exports=import("@tanstack/react-query")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1682],()=>n(75224));module.exports=r})();