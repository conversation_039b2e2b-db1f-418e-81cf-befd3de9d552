(()=>{var e={};e.id=1495,e.ids=[1495,660],e.modules={1323:(e,t)=>{"use strict";Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},78839:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>p,getServerSideProps:()=>_,getStaticPaths:()=>m,getStaticProps:()=>h,reportWebVitals:()=>b,routeModule:()=>j,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>E,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>y});var a=r(87093),s=r(35244),o=r(1323),i=r(61682),u=r.n(i),l=r(48141),c=r.n(l),d=r(66679),f=e([d]);d=(f.then?(await f)():f)[0];let p=(0,o.l)(d,"default"),h=(0,o.l)(d,"getStaticProps"),m=(0,o.l)(d,"getStaticPaths"),_=(0,o.l)(d,"getServerSideProps"),g=(0,o.l)(d,"config"),b=(0,o.l)(d,"reportWebVitals"),y=(0,o.l)(d,"unstable_getStaticProps"),v=(0,o.l)(d,"unstable_getStaticPaths"),x=(0,o.l)(d,"unstable_getStaticParams"),P=(0,o.l)(d,"unstable_getServerProps"),E=(0,o.l)(d,"unstable_getServerSideProps"),j=new a.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/Index",pathname:"/Index",bundlePath:"",filename:""},components:{App:c(),Document:u()},userland:d});n()}catch(e){n(e)}})},33592:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(16689);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:u,iconNode:l,...c},d)=>(0,n.createElement)("svg",{ref:d,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:s("lucide",i),...c},[...l.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(u)?u:[u]])),u=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...o},u)=>(0,n.createElement)(i,{ref:u,iconNode:t,className:s(`lucide-${a(e)}`,r),...o}));return r.displayName=`${e}`,r}},76211:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},83917:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},66152:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},56286:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41412:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return s}});let n=r(77652),a=r(68796);function s(e,t){return(0,a.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28878:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(68796);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75132:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_FAST_REFRESH:function(){return l},ACTION_NAVIGATE:function(){return s},ACTION_PREFETCH:function(){return u},ACTION_REFRESH:function(){return a},ACTION_RESTORE:function(){return o},ACTION_SERVER_ACTION:function(){return c},ACTION_SERVER_PATCH:function(){return i},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return r},isThenable:function(){return d}});let a="refresh",s="navigate",o="restore",i="server-patch",u="prefetch",l="fast-refresh",c="server-action";function d(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92842:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(68796),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71838:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return a}});let n=r(25298);function a(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return b}});let n=r(50167),a=r(20997),s=n._(r(16689)),o=r(64813),i=r(65853),u=r(5058),l=r(45782),c=r(28878),d=r(5469),f=r(7443),p=r(91487),h=r(92842),m=r(41412),_=r(75132);function g(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}let b=s.default.forwardRef(function(e,t){let r,n;let{href:u,as:b,children:y,prefetch:v=null,passHref:x,replace:P,shallow:E,scroll:j,locale:R,onClick:O,onMouseEnter:A,onTouchStart:S,legacyBehavior:N=!1,...T}=e;r=y,N&&("string"==typeof r||"number"==typeof r)&&(r=(0,a.jsx)("a",{children:r}));let I=s.default.useContext(d.RouterContext),M=s.default.useContext(f.AppRouterContext),C=null!=I?I:M,w=!I,L=!1!==v,k=null===v?_.PrefetchKind.AUTO:_.PrefetchKind.FULL,{href:D,as:U}=s.default.useMemo(()=>{if(!I){let e=g(u);return{href:e,as:b?g(b):e}}let[e,t]=(0,o.resolveHref)(I,u,!0);return{href:e,as:b?(0,o.resolveHref)(I,b):t||e}},[I,u,b]),F=s.default.useRef(D),H=s.default.useRef(U);N&&(n=s.default.Children.only(r));let G=N?n&&"object"==typeof n&&n.ref:t,[X,Z,V]=(0,p.useIntersection)({rootMargin:"200px"}),W=s.default.useCallback(e=>{(H.current!==U||F.current!==D)&&(V(),H.current=U,F.current=D),X(e),G&&("function"==typeof G?G(e):"object"==typeof G&&(G.current=e))},[U,G,D,V,X]);s.default.useEffect(()=>{},[U,D,Z,R,L,null==I?void 0:I.locale,C,w,k]);let z={ref:W,onClick(e){N||"function"!=typeof O||O(e),N&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),C&&!e.defaultPrevented&&function(e,t,r,n,a,o,u,l,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,i.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==u||u;"beforePopState"in t?t[a?"replace":"push"](r,n,{shallow:o,locale:l,scroll:e}):t[a?"replace":"push"](n||r,{scroll:e})};c?s.default.startTransition(f):f()}(e,C,D,U,P,E,j,R,w)},onMouseEnter(e){N||"function"!=typeof A||A(e),N&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){N||"function"!=typeof S||S(e),N&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,l.isAbsoluteUrl)(U))z.href=U;else if(!N||x||"a"===n.type&&!("href"in n.props)){let e=void 0!==R?R:null==I?void 0:I.locale,t=(null==I?void 0:I.isLocaleDomain)&&(0,h.getDomainLocale)(U,e,null==I?void 0:I.locales,null==I?void 0:I.domainLocales);z.href=t||(0,m.addBasePath)((0,c.addLocale)(U,e,null==I?void 0:I.defaultLocale))}return N?s.default.cloneElement(n,z):(0,a.jsx)("a",{...T,...z,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68796:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return s}});let n=r(33575),a=r(80626),s=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:s}=(0,a.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+s};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66078:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64813:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(38600),a=r(5058),s=r(12795),o=r(45782),i=r(68796),u=r(65853),l=r(72189),c=r(37399);function d(e,t,r){let d;let f="string"==typeof t?t:(0,a.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,o.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,u.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,i.normalizePathTrailingSlash)(e.pathname);let t="";if((0,l.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:o,params:i}=(0,c.interpolateAs)(e.pathname,e.pathname,r);o&&(t=(0,a.formatWithValidation)({pathname:o,hash:e.hash,query:(0,s.omit)(r,i)}))}let o=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[o,t||o]:o}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91487:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let n=r(16689),a=r(66078),s="function"==typeof IntersectionObserver,o=new Map,i=[];function u(e){let{rootRef:t,rootMargin:r,disabled:u}=e,l=u||!s,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(s){if(l||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:a,elements:s}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=i.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=o.get(n)))return t;let a=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=a.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:a},i.push(r),o.set(r,t),t}(r);return s.set(e,t),a.observe(e),function(){if(s.delete(e),a.unobserve(e),0===s.size){a.disconnect(),o.delete(n);let e=i.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,a.requestIdleCallback)(()=>d(!0));return()=>(0,a.cancelIdleCallback)(e)}},[l,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48141:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(50167),a=r(20997),s=n._(r(16689)),o=r(45782);async function i(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,o.loadGetInitialProps)(t,r)}}class u extends s.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,a.jsx)(e,{...t})}}u.origGetInitialProps=i,u.getInitialProps=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34592:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return a}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function a(e){return r.test(e)?e.replace(n,"\\$&"):e}},77652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return a}});let n=r(80626);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:s}=(0,n.parsePath)(e);return""+t+r+a+s}},5058:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return s},formatWithValidation:function(){return i},urlObjectKeys:function(){return o}});let n=r(28760)._(r(38600)),a=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:r}=e,s=e.protocol||"",o=e.pathname||"",i=e.hash||"",u=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||a.test(s))&&!1!==l?(l="//"+(l||""),o&&"/"!==o[0]&&(o="/"+o)):l||(l=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+s+l+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return s(e)}},37399:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return s}});let n=r(43323),a=r(36309);function s(e,t,r){let s="",o=(0,a.getRouteRegex)(e),i=o.groups,u=(t!==e?(0,n.getRouteMatcher)(o)(t):"")||r;s=e;let l=Object.keys(i);return l.every(e=>{let t=u[e]||"",{repeat:r,optional:n}=i[e],a="["+(r?"...":"")+e+"]";return n&&(a=(t?"":"/")+"["+a+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in u)&&(s=s.replace(a,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(s=""),{params:l,result:s}}},65853:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=r(45782),a=r(71838);function s(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,a.hasBasePath)(r.pathname)}catch(e){return!1}}},12795:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},80626:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},25298:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return a}});let n=r(80626);function a(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},38600:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append(r,n(e))):t.set(r,n(a))}),t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},33575:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},43323:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return a}});let n=r(45782);function a(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},o={};return Object.keys(r).forEach(e=>{let t=r[e],n=a[t.pos];void 0!==n&&(o[e]=~n.indexOf("/")?n.split("/").map(e=>s(e)):t.repeat?[s(n)]:s(n))}),o}}},36309:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return p},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return l},parseParameter:function(){return i}});let n=r(92350),a=r(92407),s=r(34592),o=r(33575);function i(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e){let t=(0,o.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=a.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:a,repeat:u}=i(o[1]);return r[e]={pos:n++,repeat:u,optional:a},"/"+(0,s.escapeStringRegexp)(t)+"([^/]+?)"}if(!o)return"/"+(0,s.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:a}=i(o[1]);return r[e]={pos:n++,repeat:t,optional:a},t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function l(e){let{parameterizedRoute:t,groups:r}=u(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function c(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:o}=e,{key:u,optional:l,repeat:c}=i(n),d=u.replace(/\W/g,"");o&&(d=""+o+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),o?a[d]=""+o+u:a[d]=u;let p=t?(0,s.escapeStringRegexp)(t):"";return c?l?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let i=(0,o.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),l={};return{namedParameterizedRoute:i.map(e=>{let r=a.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&o){let[r]=e.split(o[0]);return c({getSafeRouteKey:u,interceptionMarker:r,segment:o[1],routeKeys:l,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return o?c({getSafeRouteKey:u,segment:o[1],routeKeys:l,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,s.escapeStringRegexp)(e)}).join(""),routeKeys:l}}function f(e,t){let r=d(e,t);return{...l(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function p(e,t){let{parameterizedRoute:r}=u(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:a}=d(e,!1);return{namedRegex:"^"+a+(n?"(?:(/.*)?)":"")+"$"}}},38386:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(20997);r(16689);var a=r(66152),s=r(76211),o=r(41664),i=r.n(o);let u=()=>(0,n.jsxs)("div",{children:[n.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx("div",{className:"feature-card-icon bg-blue-100",children:n.jsx(a.Z,{className:"h-5 w-5 text-blue-600"})}),(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"feature-card-title",children:"Assets"}),n.jsx("p",{className:"feature-card-subtitle",children:"Track your valuable assets"})]})]})}),(0,n.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[n.jsx(a.Z,{className:"feature-card-empty-icon text-blue-500"}),n.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Manage Your Assets"}),n.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Track your physical and digital assets for your legacy plan."}),(0,n.jsxs)(i(),{href:"/assets",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[n.jsx(s.Z,{className:"mr-2 h-4 w-4"}),"Manage Assets"]})]})]})},29207:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(20997);r(16689);var a=r(56286),s=r(41664),o=r.n(s);let i=()=>(0,n.jsxs)("div",{children:[n.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx("div",{className:"feature-card-icon bg-blue-100",children:n.jsx(a.Z,{className:"h-5 w-5 text-blue-600"})}),(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"feature-card-title",children:"Trustees"}),n.jsx("p",{className:"feature-card-subtitle",children:"Manage your trusted contacts"})]})]})}),(0,n.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[n.jsx(a.Z,{className:"feature-card-empty-icon text-blue-500"}),n.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Manage Trustees"}),n.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Designate trusted individuals to handle your digital legacy."}),(0,n.jsxs)(o(),{href:"/trustees",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[n.jsx(a.Z,{className:"mr-2 h-4 w-4"}),"Manage Trustees"]})]})]})},61940:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(20997);r(16689);var a=r(83917),s=r(76211),o=r(41664),i=r.n(o);let u=()=>(0,n.jsxs)("div",{children:[n.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx("div",{className:"feature-card-icon bg-blue-100",children:n.jsx(a.Z,{className:"h-5 w-5 text-blue-600"})}),(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"feature-card-title",children:"Digital Vault"}),n.jsx("p",{className:"feature-card-subtitle",children:"Store important documents"})]})]})}),(0,n.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[n.jsx(a.Z,{className:"feature-card-empty-icon text-blue-500"}),n.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Document Storage"}),n.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Securely store important documents in your digital vault."}),(0,n.jsxs)(i(),{href:"/vault",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[n.jsx(s.Z,{className:"mr-2 h-4 w-4"}),"Upload Document"]})]})]})},95364:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{z:()=>d});var a=r(20997),s=r(16689),o=r(14338),i=r(16926),u=r(27742),l=e([o,i,u]);[o,i,u]=l.then?(await l)():l;let c=(0,i.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...s},i)=>{let l=n?o.Slot:"button";return a.jsx(l,{className:(0,u.cn)(c({variant:t,size:r,className:e})),ref:i,...s})});d.displayName="Button",n()}catch(e){n(e)}})},27742:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{cn:()=>i});var a=r(16593),s=r(68097),o=e([a,s]);function i(...e){return(0,s.twMerge)((0,a.clsx)(e))}[a,s]=o.then?(await o)():o,n()}catch(e){n(e)}})},66679:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>f});var a=r(20997);r(16689);var s=r(14661),o=r(76211),i=r(95364);!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var u=r(38386),l=r(61940),c=r(29207),d=e([i]);i=(d.then?(await d)():d)[0];let f=()=>{let e=(0,s.useNavigate)();return(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Dashboard",description:"Manage your digital legacy in one secure place.",actions:(0,a.jsxs)(i.z,{onClick:()=>{e("/assets")},children:[a.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Add Asset"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[a.jsx(u.Z,{}),a.jsx(l.Z,{}),a.jsx(c.Z,{})]}),(0,a.jsxs)("div",{className:"mt-10",children:[a.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Getting Started"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"legalock-card p-6",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Complete Your Profile"}),a.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Update your personal information to ensure trustees can verify your identity."}),a.jsx(i.z,{variant:"outline",size:"sm",asChild:!0,children:a.jsx("a",{href:"/settings/profile",children:"Update Profile"})})]}),(0,a.jsxs)("div",{className:"legalock-card p-6",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Add Your First Asset"}),a.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Start by adding an important digital or physical asset to your inventory."}),a.jsx(i.z,{variant:"outline",size:"sm",asChild:!0,children:a.jsx("a",{href:"/assets",children:"Add Asset"})})]}),(0,a.jsxs)("div",{className:"legalock-card p-6",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Invite a Trustee"}),a.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Select someone you trust to manage your digital legacy when needed."}),a.jsx(i.z,{variant:"outline",size:"sm",asChild:!0,children:a.jsx("a",{href:"/trustees/invite",children:"Invite Trustee"})})]})]})]})]})};n()}catch(e){n(e)}})},92350:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return u},APP_DIR_ALIAS:function(){return A},CACHE_ONE_YEAR:function(){return v},DOT_NEXT_ALIAS:function(){return R},ESLINT_DEFAULT_DIRS:function(){return W},GSP_NO_RETURNED_VALUE:function(){return F},GSSP_COMPONENT_MEMBER_ERROR:function(){return X},GSSP_NO_RETURNED_VALUE:function(){return H},INSTRUMENTATION_HOOK_FILENAME:function(){return E},MIDDLEWARE_FILENAME:function(){return x},MIDDLEWARE_LOCATION_REGEXP:function(){return P},NEXT_BODY_SUFFIX:function(){return d},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return y},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return h},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return m},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return p},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return b},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return _},NEXT_CACHE_TAG_MAX_LENGTH:function(){return g},NEXT_DATA_SUFFIX:function(){return l},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return c},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return Z},PAGES_DIR_ALIAS:function(){return j},PRERENDER_REVALIDATE_HEADER:function(){return a},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return s},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return C},ROOT_DIR_ALIAS:function(){return O},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return M},RSC_ACTION_ENCRYPTION_ALIAS:function(){return I},RSC_ACTION_PROXY_ALIAS:function(){return T},RSC_ACTION_VALIDATE_ALIAS:function(){return N},RSC_MOD_REF_PROXY_ALIAS:function(){return S},RSC_PREFETCH_SUFFIX:function(){return o},RSC_SUFFIX:function(){return i},SERVER_PROPS_EXPORT_ERROR:function(){return U},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return L},SERVER_PROPS_SSG_CONFLICT:function(){return k},SERVER_RUNTIME:function(){return z},SSG_FALLBACK_EXPORT_ERROR:function(){return V},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return w},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return D},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return G},WEBPACK_LAYERS:function(){return Y},WEBPACK_RESOURCE_QUERIES:function(){return B}});let r="nxtP",n="nxtI",a="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",o=".prefetch.rsc",i=".rsc",u=".action",l=".json",c=".meta",d=".body",f="x-next-cache-tags",p="x-next-cache-soft-tags",h="x-next-revalidated-tags",m="x-next-revalidate-tag-token",_=128,g=256,b=1024,y="_N_T_",v=31536e3,x="middleware",P=`(?:src/)?${x}`,E="instrumentation",j="private-next-pages",R="private-dot-next",O="private-next-root-dir",A="private-next-app-dir",S="private-next-rsc-mod-ref-proxy",N="private-next-rsc-action-validate",T="private-next-rsc-server-reference",I="private-next-rsc-action-encryption",M="private-next-rsc-action-client-wrapper",C="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",w="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",L="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",k="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",D="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",U="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",F="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",H="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",G="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",X="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",Z='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',V="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",W=["app","pages","components","lib","src"],z={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},q={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},Y={...q,GROUP:{serverOnly:[q.reactServerComponents,q.actionBrowser,q.appMetadataRoute,q.appRouteHandler,q.instrument],clientOnly:[q.serverSideRendering,q.appPagesBrowser],nonClientServerTarget:[q.middleware,q.api],app:[q.reactServerComponents,q.actionBrowser,q.appMetadataRoute,q.appRouteHandler,q.serverSideRendering,q.appPagesBrowser,q.shared,q.instrument]}},B={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},35244:(e,t)=>{"use strict";var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},7443:(e,t,r)=>{"use strict";e.exports=r(87093).vendored.contexts.AppRouterContext},5469:(e,t,r)=>{"use strict";e.exports=r(87093).vendored.contexts.RouterContext},41664:(e,t,r)=>{e.exports=r(63500)},62785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{"use strict";e.exports=require("react")},14661:e=>{"use strict";e.exports=require("react-router-dom")},20997:e=>{"use strict";e.exports=require("react/jsx-runtime")},55315:e=>{"use strict";e.exports=require("path")},14338:e=>{"use strict";e.exports=import("@radix-ui/react-slot")},16926:e=>{"use strict";e.exports=import("class-variance-authority")},16593:e=>{"use strict";e.exports=import("clsx")},68097:e=>{"use strict";e.exports=import("tailwind-merge")},28760:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=t._interop_require_wildcard=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=s?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(a,o,i):a[o]=e[o]}return a.default=e,n&&n.set(e,a),a}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1682],()=>r(78839));module.exports=n})();