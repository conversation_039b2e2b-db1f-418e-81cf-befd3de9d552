"use strict";(()=>{var e={};e.id=5106,e.ids=[5106,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},36365:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{config:()=>v,default:()=>f,getServerSideProps:()=>x,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>O});var o=n(87093),i=n(35244),a=n(1323),s=n(61682),c=n.n(s),l=n(48141),d=n.n(l),u=n(55247),m=e([u]);u=(m.then?(await m)():m)[0];let f=(0,a.l)(u,"default"),h=(0,a.l)(u,"getStaticProps"),p=(0,a.l)(u,"getStaticPaths"),x=(0,a.l)(u,"getServerSideProps"),v=(0,a.l)(u,"config"),g=(0,a.l)(u,"reportWebVitals"),O=(0,a.l)(u,"unstable_getStaticProps"),j=(0,a.l)(u,"unstable_getStaticPaths"),N=(0,a.l)(u,"unstable_getStaticParams"),b=(0,a.l)(u,"unstable_getServerProps"),y=(0,a.l)(u,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/Verify",pathname:"/Verify",bundlePath:"",filename:""},components:{App:d(),Document:c()},userland:u});r()}catch(e){r(e)}})},33592:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(16689);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:s="",children:c,iconNode:l,...d},u)=>(0,r.createElement)("svg",{ref:u,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:i("lucide",s),...d},[...l.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},c)=>(0,r.createElement)(s,{ref:c,iconNode:t,className:i(`lucide-${o(e)}`,n),...a}));return n.displayName=`${e}`,n}},99763:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},2461:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},59911:(e,t,n)=>{n.d(t,{Z:()=>r});let r=(0,n(33592).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},48141:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let r=n(50167),o=n(20997),i=r._(n(16689)),a=n(45782);async function s(e){let{Component:t,ctx:n}=e;return{pageProps:await (0,a.loadGetInitialProps)(t,n)}}class c extends i.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}c.origGetInitialProps=s,c.getInitialProps=s,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45597:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{Z:()=>f});var o=n(20997),i=n(16689),a=n.n(i),s=n(14661),c=n(95364),l=n(59911);!function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}();var d=n(27742),u=e([c,d]);[c,d]=u.then?(await u)():u;let m=a().forwardRef(({className:e,title:t,children:n,icon:r,...i},a)=>o.jsx("li",{children:o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,o.jsxs)("a",{ref:a,className:(0,d.cn)("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",e),...i,children:[o.jsx("div",{className:"text-sm font-medium leading-none",children:t}),o.jsx("p",{className:"line-clamp-2 text-sm leading-snug text-muted-foreground",children:n})]})})}));m.displayName="ListItem";let f=()=>{let e=(0,s.useLocation)(),t="/"===e.pathname;return o.jsx("header",{className:"w-full bg-white border-b border-gray-200",children:o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"flex justify-between items-center h-16",children:[o.jsx("div",{className:"flex items-center",children:(0,o.jsxs)(s.Link,{to:"/",className:"flex items-center gap-2",children:[o.jsx(l.Z,{className:"h-8 w-8 text-primary"}),o.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Legalock"})]})}),t&&o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hidden md:flex",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Features"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,o.jsxs)("ul",{className:"grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]",children:[o.jsx(m,{href:"#",title:"Asset Management",icon:"Shield",children:"Create a comprehensive inventory of your digital and physical assets"}),o.jsx(m,{href:"#",title:"Digital Vault",icon:"Vault",children:"Securely store sensitive documents for your trustees"}),o.jsx(m,{href:"#",title:"Time Capsule",icon:"Clock",children:"Schedule future messages to loved ones"}),o.jsx(m,{href:"#",title:"Will Writing",icon:"FileText",children:"Document your final wishes with our step-by-step guide"})]})})]}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:o.jsx(s.Link,{to:"#pricing",className:Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}())(),children:"Pricing"})}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:o.jsx(s.Link,{to:"/terms",className:Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}())(),children:"Terms"})})]})}),o.jsx("div",{className:"flex items-center gap-4",children:t?(0,o.jsxs)(o.Fragment,{children:[o.jsx(c.z,{variant:"outline",asChild:!0,children:o.jsx(s.Link,{to:"/login",children:"Sign In"})}),o.jsx(c.z,{asChild:!0,children:o.jsx(s.Link,{to:"/register",children:"Get Started"})})]}):o.jsx(c.z,{variant:"outline",asChild:!0,children:o.jsx(s.Link,{to:"/",children:"Back to Home"})})})]})})})};r()}catch(e){r(e)}})},95364:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{z:()=>u});var o=n(20997),i=n(16689),a=n(14338),s=n(16926),c=n(27742),l=e([a,s,c]);[a,s,c]=l.then?(await l)():l;let d=(0,s.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...i},s)=>{let l=r?a.Slot:"button";return o.jsx(l,{className:(0,c.cn)(d({variant:t,size:n,className:e})),ref:s,...i})});u.displayName="Button",r()}catch(e){r(e)}})},77415:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{a:()=>c}),n(20997);var o=n(16689);n(70550),n(14661);var i=n(37270),a=e([i]);i=(a.then?(await a)():a)[0];let s=(0,o.createContext)(void 0),c=()=>{let e=(0,o.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};r()}catch(e){r(e)}})},70550:(e,t,n)=>{n.d(t,{O:()=>r});let r=(0,n(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.d(t,{cn:()=>s});var o=n(16593),i=n(68097),a=e([o,i]);function s(...e){return(0,i.twMerge)((0,o.clsx)(e))}[o,i]=a.then?(await a)():a,r()}catch(e){r(e)}})},55247:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{default:()=>p});var o=n(20997),i=n(16689),a=n(14661);!function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}();var s=n(95364),c=n(37270),l=n(77415),d=n(45597);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var u=n(2461),m=n(99763),f=n(70550),h=e([s,c,l,d]);[s,c,l,d]=h.then?(await h)():h;let p=()=>{let[e]=(0,a.useSearchParams)(),t=e.get("email")||"",[n,r]=(0,i.useState)(""),{verifyOtp:h}=(0,l.a)(),[p,x]=(0,i.useState)(!1),[v,g]=(0,i.useState)(!1),O=(0,a.useNavigate)();(0,i.useEffect)(()=>{t||(c.toast.error("Email is required for verification"),O("/register"))},[t,O]);let j=async()=>{if(6!==n.length){c.toast.error("Please enter a valid 6-digit verification code");return}x(!0);try{await h(t,n)}catch(e){console.error("Verification error:",e)}finally{x(!1)}},N=async()=>{if(!t){c.toast.error("Email is required to resend verification code");return}g(!0);try{let{error:e}=await f.O.auth.resend({type:"signup",email:t});if(e)throw e;c.toast.success("A new verification code has been sent to your email")}catch(e){c.toast.error(e.message||"Failed to resend verification code")}finally{g(!1)}};return(0,o.jsxs)("div",{className:"min-h-screen flex flex-col bg-gray-50",children:[o.jsx(d.Z,{}),o.jsx("div",{className:"flex-1 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:o.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-1",children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-2xl font-bold text-center",children:"Verify your email"}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:["We've sent a verification code to ",t||"your email"]})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[o.jsx("label",{className:"text-sm font-medium text-gray-700 block",children:"Enter your 6-digit code"}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{maxLength:6,value:n,onChange:r,children:(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{index:0}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{index:1}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{index:2}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{index:3}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{index:4}),o.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input-otp'");throw e.code="MODULE_NOT_FOUND",e}()),{index:5})]})})]}),o.jsx(s.z,{onClick:j,className:"w-full",disabled:p||6!==n.length,children:p?(0,o.jsxs)("span",{className:"flex items-center",children:[o.jsx("span",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"}),"Verifying..."]}):"Verify Email"})]}),(0,o.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-col space-y-4",children:[o.jsx("div",{className:"text-sm text-center w-full",children:o.jsx("button",{onClick:N,className:"inline-flex items-center text-primary hover:underline",disabled:v,children:v?(0,o.jsxs)(o.Fragment,{children:[o.jsx(u.Z,{className:"mr-1 h-4 w-4 animate-spin"}),"Sending..."]}):(0,o.jsxs)(o.Fragment,{children:[o.jsx(u.Z,{className:"mr-1 h-4 w-4"}),"Resend code"]})})}),o.jsx("div",{className:"text-sm text-center w-full",children:(0,o.jsxs)(a.Link,{to:"/register",className:"inline-flex items-center text-gray-600 hover:text-gray-800",children:[o.jsx(m.Z,{className:"mr-1 h-4 w-4"}),"Back to registration"]})})]})]})})})]})};r()}catch(e){r(e)}})},35244:(e,t)=>{var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1682],()=>n(36365));module.exports=r})();