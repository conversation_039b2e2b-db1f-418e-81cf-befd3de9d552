"use strict";(()=>{var e={};e.id=6598,e.ids=[6598,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},26624:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>j,default:()=>h,getServerSideProps:()=>v,getStaticPaths:()=>x,getStaticProps:()=>f,reportWebVitals:()=>p,routeModule:()=>_,unstable_getServerProps:()=>O,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>N});var s=r(87093),a=r(35244),i=r(1323),o=r(61682),c=r.n(o),d=r(48141),l=r.n(d),u=r(21561),m=e([u]);u=(m.then?(await m)():m)[0];let h=(0,i.l)(u,"default"),f=(0,i.l)(u,"getStaticProps"),x=(0,i.l)(u,"getStaticPaths"),v=(0,i.l)(u,"getServerSideProps"),j=(0,i.l)(u,"config"),p=(0,i.l)(u,"reportWebVitals"),N=(0,i.l)(u,"unstable_getStaticProps"),g=(0,i.l)(u,"unstable_getStaticPaths"),b=(0,i.l)(u,"unstable_getStaticParams"),O=(0,i.l)(u,"unstable_getServerProps"),y=(0,i.l)(u,"unstable_getServerSideProps"),_=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/ServiceSunset",pathname:"/ServiceSunset",bundlePath:"",filename:""},components:{App:l(),Document:c()},userland:u});n()}catch(e){n(e)}})},33592:(e,t,r)=>{r.d(t,{Z:()=>c});var n=r(16689);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:o="",children:c,iconNode:d,...l},u)=>(0,n.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:a("lucide",o),...l},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},c)=>(0,n.createElement)(o,{ref:c,iconNode:t,className:a(`lucide-${s(e)}`,r),...i}));return r.displayName=`${e}`,r}},80906:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},83917:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},22972:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},48141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(50167),s=r(20997),a=n._(r(16689)),i=r(45782);async function o(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,i.loadGetInitialProps)(t,r)}}class c extends a.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,s.jsx)(e,{...t})}}c.origGetInitialProps=o,c.getInitialProps=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95364:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{z:()=>u});var s=r(20997),a=r(16689),i=r(14338),o=r(16926),c=r(27742),d=e([i,o,c]);[i,o,c]=d.then?(await d)():d;let l=(0,o.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...a},o)=>{let d=n?i.Slot:"button";return s.jsx(d,{className:(0,c.cn)(l({variant:t,size:r,className:e})),ref:o,...a})});u.displayName="Button",n()}catch(e){n(e)}})},27742:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{cn:()=>o});var s=r(16593),a=r(68097),i=e([s,a]);function o(...e){return(0,a.twMerge)((0,s.clsx)(e))}[s,a]=i.then?(await i)():i,n()}catch(e){n(e)}})},21561:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>l});var s=r(20997);r(16689);var a=r(95364),i=r(83917),o=r(22972),c=r(80906);(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();var d=e([a]);a=(d.then?(await d)():d)[0];let l=()=>(0,s.jsxs)("div",{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Service Sunset",description:"Manage the transfer of your digital assets in case of extended inactivity.",actions:(0,s.jsxs)(a.z,{children:[s.jsx(i.Z,{className:"mr-2 h-4 w-4"}),"Set Up Sunset Plan"]})}),s.jsx("div",{className:"mt-6",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"overview",children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"overview",children:"Overview"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"settings",children:"Settings"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"history",children:"Activity Log"})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"overview",className:"mt-6",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Sunset Status"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Current status of your sunset plan and configuration."})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-amber-50 rounded-lg border border-amber-200",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"h-10 w-10 rounded-full bg-amber-200 flex items-center justify-center",children:s.jsx(i.Z,{className:"h-5 w-5 text-amber-700"})}),(0,s.jsxs)("div",{className:"ml-4",children:[s.jsx("h3",{className:"font-medium",children:"Sunset Plan Inactive"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Configure your sunset plan to ensure your digital assets are transferred according to your wishes."})]})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-200",children:"Not Configured"})]}),(0,s.jsxs)("div",{className:"mt-6 space-y-4",children:[s.jsx("h3",{className:"font-medium",children:"Configuration Status"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-3 rounded-lg bg-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:s.jsx(o.Z,{className:"h-4 w-4 text-gray-600"})}),s.jsx("span",{children:"Inactivity Period"})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-gray-100",children:"Not Set"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center p-3 rounded-lg bg-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:s.jsx(o.Z,{className:"h-4 w-4 text-gray-600"})}),s.jsx("span",{children:"Verification Method"})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-gray-100",children:"Not Set"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center p-3 rounded-lg bg-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:s.jsx(o.Z,{className:"h-4 w-4 text-gray-600"})}),s.jsx("span",{children:"Beneficiaries"})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-gray-100",children:"Not Set"})]})]})]})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(a.z,{children:"Configure Sunset Plan"})})]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"settings",className:"mt-6",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Sunset Plan Settings"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Configure how your digital assets will be handled in case of extended inactivity."})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-medium mb-2",children:"Inactivity Period"}),s.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Choose how long the system should wait after your last activity before initiating the sunset process."}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx(a.z,{variant:"outline",className:"justify-start",children:"3 Months"}),s.jsx(a.z,{variant:"outline",className:"justify-start",children:"6 Months"}),s.jsx(a.z,{variant:"outline",className:"justify-start",children:"1 Year"}),s.jsx(a.z,{variant:"outline",className:"justify-start",children:"18 Months"}),s.jsx(a.z,{variant:"outline",className:"justify-start",children:"2 Years"}),s.jsx(a.z,{variant:"outline",className:"justify-start",children:"Custom"})]})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-medium mb-2",children:"Verification Method"}),s.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Choose how the system will verify your status before initiating the sunset process."}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center p-3 border rounded-lg",children:[s.jsx("input",{type:"radio",id:"email",name:"verification",className:"mr-3"}),(0,s.jsxs)("label",{htmlFor:"email",className:"flex-1",children:[s.jsx("div",{className:"font-medium",children:"Email Verification"}),s.jsx("div",{className:"text-sm text-gray-600",children:"You'll receive emails to verify your status."})]})]}),(0,s.jsxs)("div",{className:"flex items-center p-3 border rounded-lg",children:[s.jsx("input",{type:"radio",id:"phone",name:"verification",className:"mr-3"}),(0,s.jsxs)("label",{htmlFor:"phone",className:"flex-1",children:[s.jsx("div",{className:"font-medium",children:"Phone Verification"}),s.jsx("div",{className:"text-sm text-gray-600",children:"You'll receive SMS messages to verify your status."})]})]}),(0,s.jsxs)("div",{className:"flex items-center p-3 border rounded-lg",children:[s.jsx("input",{type:"radio",id:"both",name:"verification",className:"mr-3"}),(0,s.jsxs)("label",{htmlFor:"both",className:"flex-1",children:[s.jsx("div",{className:"font-medium",children:"Both Email and Phone"}),s.jsx("div",{className:"text-sm text-gray-600",children:"You'll receive both emails and SMS messages."})]})]})]})]})]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(a.z,{children:"Save Settings"})})]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"history",className:"mt-6",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Activity Log"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Track all activities related to your sunset plan."})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"h-10 w-10 rounded-full bg-green-100 flex items-center justify-center",children:s.jsx(c.Z,{className:"h-5 w-5 text-green-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[s.jsx("h4",{className:"font-medium",children:"Activity Verification Confirmed"}),s.jsx("p",{className:"text-sm text-gray-600",children:"You confirmed your activity status via email."}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"March 15, 2025 at 10:23 AM"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:s.jsx(i.Z,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[s.jsx("h4",{className:"font-medium",children:"Sunset Plan Settings Updated"}),s.jsx("p",{className:"text-sm text-gray-600",children:"You updated your sunset plan settings."}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"February 28, 2025 at 3:45 PM"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:s.jsx(i.Z,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[s.jsx("h4",{className:"font-medium",children:"Sunset Plan Created"}),s.jsx("p",{className:"text-sm text-gray-600",children:"You created your sunset plan."}),s.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"January 10, 2025 at 9:12 AM"})]})]})]})})]})})]})})]});n()}catch(e){n(e)}})},35244:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1682],()=>r(26624));module.exports=n})();