"use strict";(()=>{var e={};e.id=3939,e.ids=[3939,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},20392:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>h,getServerSideProps:()=>p,getStaticPaths:()=>f,getStaticProps:()=>x,reportWebVitals:()=>v,routeModule:()=>w,unstable_getServerProps:()=>O,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>j});var a=r(87093),s=r(35244),o=r(1323),i=r(61682),l=r.n(i),c=r(48141),d=r.n(c),u=r(10135),m=e([u]);u=(m.then?(await m)():m)[0];let h=(0,o.l)(u,"default"),x=(0,o.l)(u,"getStaticProps"),f=(0,o.l)(u,"getStaticPaths"),p=(0,o.l)(u,"getServerSideProps"),g=(0,o.l)(u,"config"),v=(0,o.l)(u,"reportWebVitals"),j=(0,o.l)(u,"unstable_getStaticProps"),b=(0,o.l)(u,"unstable_getStaticPaths"),N=(0,o.l)(u,"unstable_getStaticParams"),O=(0,o.l)(u,"unstable_getServerProps"),y=(0,o.l)(u,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/Contact",pathname:"/Contact",bundlePath:"",filename:""},components:{App:d(),Document:l()},userland:u});n()}catch(e){n(e)}})},33592:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(16689);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:l,iconNode:c,...d},u)=>(0,n.createElement)("svg",{ref:u,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:s("lucide",i),...d},[...c.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...o},l)=>(0,n.createElement)(i,{ref:l,iconNode:t,className:s(`lucide-${a(e)}`,r),...o}));return r.displayName=`${e}`,r}},99763:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},33656:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},93012:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},4021:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},59911:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(33592).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},48141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(50167),a=r(20997),s=n._(r(16689)),o=r(45782);async function i(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,o.loadGetInitialProps)(t,r)}}class l extends s.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,a.jsx)(e,{...t})}}l.origGetInitialProps=i,l.getInitialProps=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45597:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{Z:()=>h});var a=r(20997),s=r(16689),o=r.n(s),i=r(14661),l=r(95364),c=r(59911);!function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}();var d=r(27742),u=e([l,d]);[l,d]=u.then?(await u)():u;let m=o().forwardRef(({className:e,title:t,children:r,icon:n,...s},o)=>a.jsx("li",{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,a.jsxs)("a",{ref:o,className:(0,d.cn)("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",e),...s,children:[a.jsx("div",{className:"text-sm font-medium leading-none",children:t}),a.jsx("p",{className:"line-clamp-2 text-sm leading-snug text-muted-foreground",children:r})]})})}));m.displayName="ListItem";let h=()=>{let e=(0,i.useLocation)(),t="/"===e.pathname;return a.jsx("header",{className:"w-full bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[a.jsx("div",{className:"flex items-center",children:(0,a.jsxs)(i.Link,{to:"/",className:"flex items-center gap-2",children:[a.jsx(c.Z,{className:"h-8 w-8 text-primary"}),a.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Legalock"})]})}),t&&a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hidden md:flex",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Features"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("ul",{className:"grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]",children:[a.jsx(m,{href:"#",title:"Asset Management",icon:"Shield",children:"Create a comprehensive inventory of your digital and physical assets"}),a.jsx(m,{href:"#",title:"Digital Vault",icon:"Vault",children:"Securely store sensitive documents for your trustees"}),a.jsx(m,{href:"#",title:"Time Capsule",icon:"Clock",children:"Schedule future messages to loved ones"}),a.jsx(m,{href:"#",title:"Will Writing",icon:"FileText",children:"Document your final wishes with our step-by-step guide"})]})})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx(i.Link,{to:"#pricing",className:Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}())(),children:"Pricing"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx(i.Link,{to:"/terms",className:Object(function(){var e=Error("Cannot find module '@/components/ui/navigation-menu'");throw e.code="MODULE_NOT_FOUND",e}())(),children:"Terms"})})]})}),a.jsx("div",{className:"flex items-center gap-4",children:t?(0,a.jsxs)(a.Fragment,{children:[a.jsx(l.z,{variant:"outline",asChild:!0,children:a.jsx(i.Link,{to:"/login",children:"Sign In"})}),a.jsx(l.z,{asChild:!0,children:a.jsx(i.Link,{to:"/register",children:"Get Started"})})]}):a.jsx(l.z,{variant:"outline",asChild:!0,children:a.jsx(i.Link,{to:"/",children:"Back to Home"})})})]})})})};n()}catch(e){n(e)}})},95364:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{z:()=>u});var a=r(20997),s=r(16689),o=r(14338),i=r(16926),l=r(27742),c=e([o,i,l]);[o,i,l]=c.then?(await c)():c;let d=(0,i.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...s},i)=>{let c=n?o.Slot:"button";return a.jsx(c,{className:(0,l.cn)(d({variant:t,size:r,className:e})),ref:i,...s})});u.displayName="Button",n()}catch(e){n(e)}})},87095:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{I:()=>l});var a=r(20997),s=r(16689),o=r(27742),i=e([o]);o=(i.then?(await i)():i)[0];let l=s.forwardRef(({className:e,type:t,...r},n)=>a.jsx("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));l.displayName="Input",n()}catch(e){n(e)}})},27742:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{cn:()=>i});var a=r(16593),s=r(68097),o=e([a,s]);function i(...e){return(0,s.twMerge)((0,a.clsx)(e))}[a,s]=o.then?(await o)():o,n()}catch(e){n(e)}})},10135:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>x});var a=r(20997);r(16689);var s=r(14661),o=r(95364),i=r(99763),l=r(33656),c=r(4021),d=r(93012),u=r(87095);(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();var m=r(45597);!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var h=e([o,u,m]);[o,u,m]=h.then?(await h)():h;let x=()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(m.Z,{}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[a.jsx(o.z,{variant:"outline",className:"mb-6",asChild:!0,children:(0,a.jsxs)(s.Link,{to:"/",children:[a.jsx(i.Z,{className:"mr-2 h-4 w-4"}),"Back to Home"]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Contact Us",description:"We're here to help with any questions you may have"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"md:col-span-1 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(l.Z,{className:"h-5 w-5 text-blue-600 mr-3"}),a.jsx("h3",{className:"font-medium",children:"Email Us"})]}),a.jsx("p",{className:"text-gray-600 text-sm mb-2",children:"For general inquiries:"}),a.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"}),a.jsx("p",{className:"text-gray-600 text-sm mt-4 mb-2",children:"For support:"}),a.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(c.Z,{className:"h-5 w-5 text-blue-600 mr-3"}),a.jsx("h3",{className:"font-medium",children:"Call Us"})]}),a.jsx("p",{className:"text-gray-600 text-sm mb-2",children:"Customer Support:"}),a.jsx("a",{href:"tel:+***********",className:"text-blue-600 hover:underline",children:"+****************"}),a.jsx("p",{className:"text-gray-600 text-sm mt-4 mb-2",children:"Business Hours:"}),(0,a.jsxs)("p",{className:"text-gray-700",children:["Monday-Friday: 9am - 6pm EST",a.jsx("br",{}),"Weekends: Closed"]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(d.Z,{className:"h-5 w-5 text-blue-600 mr-3"}),a.jsx("h3",{className:"font-medium",children:"Live Chat"})]}),a.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Chat with our support team in real-time for immediate assistance."}),a.jsx(o.z,{className:"w-full",variant:"outline",children:"Start Chat"})]})]}),a.jsx("div",{className:"md:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[a.jsx("h2",{className:"text-xl font-semibold mb-6",children:"Send Us a Message"}),(0,a.jsxs)("form",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"firstName",className:"text-sm font-medium text-gray-700",children:"First Name"}),a.jsx(u.I,{id:"firstName",placeholder:"Your first name"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"lastName",className:"text-sm font-medium text-gray-700",children:"Last Name"}),a.jsx(u.I,{id:"lastName",placeholder:"Your last name"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),a.jsx(u.I,{id:"email",type:"email",placeholder:"Your email address"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"subject",className:"text-sm font-medium text-gray-700",children:"Subject"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select a topic"})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"general",children:"General Inquiry"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"support",children:"Technical Support"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"billing",children:"Billing Question"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"feedback",children:"Feedback"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"other",children:"Other"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"message",className:"text-sm font-medium text-gray-700",children:"Message"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"message",placeholder:"How can we help you?",rows:5})]}),a.jsx(o.z,{className:"w-full md:w-auto",type:"submit",children:"Send Message"})]})]})})]})]})]});n()}catch(e){n(e)}})},35244:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1682],()=>r(20392));module.exports=n})();