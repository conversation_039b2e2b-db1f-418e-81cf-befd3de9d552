"use strict";(()=>{var e={};e.id=6076,e.ids=[6076,660],e.modules={1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},84870:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>f,getServerSideProps:()=>y,getStaticPaths:()=>h,getStaticProps:()=>m,reportWebVitals:()=>x,routeModule:()=>N,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>O,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>v});var n=r(87093),o=r(35244),i=r(1323),l=r(61682),s=r.n(l),c=r(48141),d=r.n(c),u=r(49673),p=e([u]);u=(p.then?(await p)():p)[0];let f=(0,i.l)(u,"default"),m=(0,i.l)(u,"getStaticProps"),h=(0,i.l)(u,"getStaticPaths"),y=(0,i.l)(u,"getServerSideProps"),g=(0,i.l)(u,"config"),x=(0,i.l)(u,"reportWebVitals"),v=(0,i.l)(u,"unstable_getStaticProps"),b=(0,i.l)(u,"unstable_getStaticPaths"),w=(0,i.l)(u,"unstable_getStaticParams"),j=(0,i.l)(u,"unstable_getServerProps"),O=(0,i.l)(u,"unstable_getServerSideProps"),N=new n.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/DocumentUpload",pathname:"/DocumentUpload",bundlePath:"",filename:""},components:{App:d(),Document:s()},userland:u});a()}catch(e){a(e)}})},33592:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(16689);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:s,iconNode:c,...d},u)=>(0,a.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:o("lucide",l),...d},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(s)?s:[s]])),s=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...i},s)=>(0,a.createElement)(l,{ref:s,iconNode:t,className:o(`lucide-${n(e)}`,r),...i}));return r.displayName=`${e}`,r}},23973:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},71180:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},6391:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},22972:(e,t,r)=>{r.d(t,{Z:()=>a});let a=(0,r(33592).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},48141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let a=r(50167),n=r(20997),o=a._(r(16689)),i=r(45782);async function l(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,i.loadGetInitialProps)(t,r)}}class s extends o.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,n.jsx)(e,{...t})}}s.origGetInitialProps=l,s.getInitialProps=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82010:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{b:()=>f});var n=r(20997),o=r(16689),i=r(23973),l=r(6391),s=r(71180),c=r(22972),d=r(95364),u=r(27742),p=e([d,u]);[d,u]=p.then?(await p)():p;let f=({onFileSelected:e,maxSizeMB:t=5120,acceptedFileTypes:r=[]})=>{let[a,p]=(0,o.useState)(null),[f,m]=(0,o.useState)(null),h=(0,o.useRef)(null),y=1048576*t,g=e=>{if(e.size>y)return m(`File size exceeds the maximum limit of ${t}MB`),!1;if(r.length>0){let t=e.type,a=e.name.split(".").pop()?.toLowerCase();if(!r.some(e=>!!(t===e||e.startsWith(".")&&a===e.substring(1))))return m("File type not accepted. Please upload a document, image, video, audio, or archive file."),!1}return m(null),!0};return n.jsx("div",{className:"w-full",children:a?n.jsx("div",{className:"border rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx(s.Z,{className:"h-5 w-5 text-primary mr-2"}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"text-sm font-medium truncate max-w-[200px] md:max-w-xs",children:a.name}),n.jsx("p",{className:"text-xs text-gray-500",children:(0,u.formatFileSize)(a.size)})]})]}),(0,n.jsxs)(d.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700",onClick:()=>{p(null),m(null),h.current&&(h.current.value="")},children:[n.jsx(c.Z,{className:"h-4 w-4"}),n.jsx("span",{className:"sr-only",children:"Remove file"})]})]})}):(0,n.jsxs)("div",{className:`border-2 border-dashed ${f?"border-red-300 bg-red-50":"border-gray-300 hover:bg-gray-50"} rounded-lg p-6 text-center cursor-pointer transition-colors`,onClick:()=>h.current?.click(),onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:t=>{t.preventDefault(),t.stopPropagation();let r=t.dataTransfer.files;if(r&&r.length>0){let t=r[0];g(t)&&(p(t),e(t))}},children:[f?n.jsx(i.Z,{className:"mx-auto h-12 w-12 text-red-400"}):n.jsx(l.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),n.jsx("p",{className:`mt-2 text-sm ${f?"text-red-600 font-medium":"text-gray-600"}`,children:f||"Drag and drop a file here, or click to select a file"}),r.length>0&&!f&&n.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Accepted file types: PDF, Word, Excel, PowerPoint, Images, Videos, Audio, and more"}),n.jsx("input",{type:"file",className:"hidden",onChange:t=>{let r=t.target.files;if(r&&r.length>0){let t=r[0];g(t)?(p(t),e(t)):h.current&&(h.current.value="")}},ref:h,accept:r.join(",")})]})})};a()}catch(e){a(e)}})},95364:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{z:()=>u});var n=r(20997),o=r(16689),i=r(14338),l=r(16926),s=r(27742),c=e([i,l,s]);[i,l,s]=c.then?(await c)():c;let d=(0,l.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},l)=>{let c=a?i.Slot:"button";return n.jsx(c,{className:(0,s.cn)(d({variant:t,size:r,className:e})),ref:l,...o})});u.displayName="Button",a()}catch(e){a(e)}})},87095:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{I:()=>s});var n=r(20997),o=r(16689),i=r(27742),l=e([i]);i=(l.then?(await l)():l)[0];let s=o.forwardRef(({className:e,type:t,...r},a)=>n.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));s.displayName="Input",a()}catch(e){a(e)}})},7825:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{_:()=>u});var n=r(20997),o=r(16689),i=r(10049),l=r(16926),s=r(27742),c=e([i,l,s]);[i,l,s]=c.then?(await c)():c;let d=(0,l.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=o.forwardRef(({className:e,...t},r)=>n.jsx(i.Root,{ref:r,className:(0,s.cn)(d(),e),...t}));u.displayName=i.Root.displayName,a()}catch(e){a(e)}})},77415:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{a:()=>s}),r(20997);var n=r(16689);r(70550),r(14661);var o=r(37270),i=e([o]);o=(i.then?(await i)():i)[0];let l=(0,n.createContext)(void 0),s=()=>{let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e};a()}catch(e){a(e)}})},70550:(e,t,r)=>{r.d(t,{O:()=>a});let a=(0,r(92885).createClient)("https://ccwvtcudztphwwzzgwvg.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps")},27742:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.d(t,{cn:()=>l});var n=r(16593),o=r(68097),i=e([n,o]);function l(...e){return(0,o.twMerge)((0,n.clsx)(e))}[n,o]=i.then?(await i)():i,a()}catch(e){a(e)}})},49673:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>g});var n=r(20997),o=r(16689),i=r(14661),l=r(77415),s=r(70550),c=r(7825),d=r(87095),u=r(95364);(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var p=r(82010),f=r(64923),m=r(37270),h=e([l,c,d,u,p,m]);[l,c,d,u,p,m]=h.then?(await h)():h;let y=[{value:"will",label:"Will"},{value:"trust",label:"Trust"},{value:"financial",label:"Financial"},{value:"medical",label:"Medical"},{value:"insurance",label:"Insurance"},{value:"property",label:"Property"},{value:"digital",label:"Digital"},{value:"other",label:"Other"}],g=()=>{let[e,t]=(0,o.useState)(""),[r,a]=(0,o.useState)(""),[h,g]=(0,o.useState)(null),[x,v]=(0,o.useState)(!1),b=(0,i.useNavigate)(),{user:w}=(0,l.a)(),j=async()=>{if(!h||!e||!r||!w){m.toast.error("Please fill in all fields and select a file");return}try{v(!0);let t=crypto.getRandomValues(new Uint8Array(32)),a=btoa(String.fromCharCode(...t)),n=await (0,f.Po)(h),o=`${w.id}/${Date.now()}_${h.name}`,{error:i}=await s.O.storage.from("documents").upload(o,n);if(i)throw i;let{error:l}=await s.O.from("documents").insert({name:e,user_id:w.id,category:r,file_name:o,encryption_key:a,file_type:h.type,file_size:h.size});if(l)throw l;m.toast.success("Document uploaded successfully"),b("/vault")}catch(e){console.error("Upload error:",e),m.toast.error(`Upload failed: ${e.message}`)}finally{v(!1)}};return n.jsx("div",{className:"container mx-auto py-10",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full max-w-2xl mx-auto",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Upload New Document"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Securely store your important documents in your digital vault."})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[n.jsx(c._,{htmlFor:"documentName",children:"Document Name"}),n.jsx(d.I,{type:"text",id:"documentName",placeholder:"e.g., Last Will and Testament",value:e,onChange:e=>t(e.target.value)})]}),(0,n.jsxs)("div",{children:[n.jsx(c._,{htmlFor:"category",children:"Category"}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{onValueChange:a,children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select a category"})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:y.map(e=>n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]})]}),(0,n.jsxs)("div",{children:[n.jsx(c._,{children:"File"}),n.jsx(p.b,{onFileSelected:e=>g(e)})]})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(u.z,{onClick:j,disabled:x,className:"w-full",children:x?"Uploading...":"Upload Document"})})]})})};a()}catch(e){a(e)}})},64923:(e,t,r)=>{r.d(t,{F2:()=>a,Po:()=>i,np:()=>l});let a=e=>{let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",r="";for(let a=0;a<e;a++){let e=Math.floor(Math.random()*t.length);r+=t[e]}return r},n=e=>{let t=new Uint8Array(e),r="";for(let e=0;e<t.byteLength;e++)r+=String.fromCharCode(t[e]);return btoa(r)},o=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},i=async e=>{try{let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.generateKey({name:"AES-GCM",length:256},!0,["encrypt","decrypt"]),a=await crypto.subtle.exportKey("raw",r);sessionStorage.setItem("lastEncryptionKey",n(a));let o=e.size,i=Math.ceil(o/10485760);if(1===i){let a=await e.arrayBuffer(),n=await crypto.subtle.encrypt({name:"AES-GCM",iv:t},r,a),o=new Uint8Array(1+t.length+n.byteLength);return o[0]=t.length,o.set(t,1),o.set(new Uint8Array(n),1+t.length),new File([o],e.name,{type:"application/encrypted",lastModified:e.lastModified})}{let a=new Uint8Array(1+t.length);a[0]=t.length,a.set(t,1);let n=[a];for(let a=0;a<i;a++){let i=10485760*a,l=Math.min(i+10485760,o),s=await e.slice(i,l).arrayBuffer(),c=await crypto.subtle.encrypt({name:"AES-GCM",iv:t},r,s);n.push(new Uint8Array(c))}return new File(n,e.name,{type:"application/encrypted",lastModified:e.lastModified})}}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt file")}},l=async(e,t,r,a)=>{try{let n="string"==typeof t?o(t):t,i=await crypto.subtle.importKey("raw",n,{name:"AES-GCM",length:256},!1,["decrypt"]),l=await e.slice(0,100).arrayBuffer(),s=new Uint8Array(l),c=s[0];if(c<1||c>16)throw console.error("Invalid IV length:",c),Error("Invalid file format or encryption");let d=s.slice(1,1+c),u=e.size,p=1+c;try{if(u-p<10485760){let t=await e.arrayBuffer(),n=new Uint8Array(t).slice(p),o=await crypto.subtle.decrypt({name:"AES-GCM",iv:d},i,n);return new File([o],r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}{let t=Math.ceil((u-p)/10485760),n=[];for(let r=0;r<t;r++){let t=p+10485760*r,a=Math.min(t+10485760,u),o=await e.slice(t,a).arrayBuffer(),l=await crypto.subtle.decrypt({name:"AES-GCM",iv:d},i,o);n.push(new Uint8Array(l))}return new File(n,r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}}catch(t){console.error("Crypto operation error:",t);try{let t=await e.arrayBuffer(),n=new Uint8Array(12).fill(1),o=await crypto.subtle.decrypt({name:"AES-GCM",iv:n},i,t);return new File([o],r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}catch(e){throw console.error("Fallback decryption failed:",e),Error("Failed to decrypt file: "+e.message)}}}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt file: "+(e.message||"Unknown error"))}}},35244:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},92885:e=>{e.exports=require("@supabase/supabase-js")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},14661:e=>{e.exports=require("react-router-dom")},20997:e=>{e.exports=require("react/jsx-runtime")},55315:e=>{e.exports=require("path")},10049:e=>{e.exports=import("@radix-ui/react-label")},14338:e=>{e.exports=import("@radix-ui/react-slot")},16926:e=>{e.exports=import("class-variance-authority")},16593:e=>{e.exports=import("clsx")},37270:e=>{e.exports=import("sonner")},68097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1682],()=>r(84870));module.exports=a})();