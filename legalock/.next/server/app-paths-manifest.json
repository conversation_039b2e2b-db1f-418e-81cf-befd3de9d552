{"/_not-found/page": "app/_not-found/page.js", "/api/assets/route": "app/api/assets/route.js", "/api/auth/resend-verification/route": "app/api/auth/resend-verification/route.js", "/api/auth/session/route": "app/api/auth/session/route.js", "/api/auth/update-email/route": "app/api/auth/update-email/route.js", "/api/auth/mark-code-used/route": "app/api/auth/mark-code-used/route.js", "/api/auth/update-password/route": "app/api/auth/update-password/route.js", "/api/contacts/route": "app/api/contacts/route.js", "/api/auth/update-profile/route": "app/api/auth/update-profile/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/auth/verify/route": "app/api/auth/verify/route.js", "/api/documents/recent/route": "app/api/documents/recent/route.js", "/api/documents/[id]/download/route": "app/api/documents/[id]/download/route.js", "/api/documents/upload/route": "app/api/documents/upload/route.js", "/api/documents/route": "app/api/documents/route.js", "/api/mark-code-used/route": "app/api/mark-code-used/route.js", "/api/resend-verification/route": "app/api/resend-verification/route.js", "/api/last-wishes/route": "app/api/last-wishes/route.js", "/api/send-trustee-invitation/route": "app/api/send-trustee-invitation/route.js", "/api/send-verification-code/route": "app/api/send-verification-code/route.js", "/api/storage/upload/route": "app/api/storage/upload/route.js", "/api/auth/signin/route": "app/api/auth/signin/route.js", "/api/stripe/checkout/route": "app/api/stripe/checkout/route.js", "/api/service-sunset/route": "app/api/service-sunset/route.js", "/api/time-capsules/[id]/route": "app/api/time-capsules/[id]/route.js", "/api/stripe/webhook/route": "app/api/stripe/webhook/route.js", "/api/time-capsules/media/route": "app/api/time-capsules/media/route.js", "/api/dashboard/route": "app/api/dashboard/route.js", "/api/auth/signout/route": "app/api/auth/signout/route.js", "/api/verify-email/route": "app/api/verify-email/route.js", "/api/stripe/subscription/route": "app/api/stripe/subscription/route.js", "/api/trustees/route": "app/api/trustees/route.js", "/api/time-capsules/route": "app/api/time-capsules/route.js", "/auth-debug/page": "app/auth-debug/page.js", "/api/will-advisor/progress/route": "app/api/will-advisor/progress/route.js", "/api/vault/documents/route": "app/api/vault/documents/route.js", "/auth/callback/route": "app/auth/callback/route.js", "/auth-fix/page": "app/auth-fix/page.js", "/assets/page": "app/assets/page.js", "/contacts/page": "app/contacts/page.js", "/debug/page": "app/debug/page.js", "/dashboard/page": "app/dashboard/page.js", "/api/documents/[id]/route": "app/api/documents/[id]/route.js", "/api/will-progress/route": "app/api/will-progress/route.js", "/lander/page": "app/lander/page.js", "/page": "app/page.js", "/register/page": "app/register/page.js", "/last-wishes/page": "app/last-wishes/page.js", "/pricing/page": "app/pricing/page.js", "/service-sunset/page": "app/service-sunset/page.js", "/settings/profile/page": "app/settings/profile/page.js", "/settings/page": "app/settings/page.js", "/subscription/page": "app/subscription/page.js", "/settings/security/page": "app/settings/security/page.js", "/login/page": "app/login/page.js", "/time-capsule/[id]/page": "app/time-capsule/[id]/page.js", "/trustee/[userId]/last-wishes/page": "app/trustee/[userId]/last-wishes/page.js", "/trustee/[userId]/services/page": "app/trustee/[userId]/services/page.js", "/time-capsule/page": "app/time-capsule/page.js", "/trustee/[userId]/vault/page": "app/trustee/[userId]/vault/page.js", "/trustee/accept/page": "app/trustee/accept/page.js", "/trustee/dashboard/page": "app/trustee/dashboard/page.js", "/trustees/page": "app/trustees/page.js", "/vault/page": "app/vault/page.js", "/verify-password/page": "app/verify-password/page.js", "/verify/page": "app/verify/page.js", "/will-advisor/advisor/page": "app/will-advisor/advisor/page.js", "/will/advisor/page": "app/will/advisor/page.js", "/will-advisor/page": "app/will-advisor/page.js", "/will/page": "app/will/page.js", "/features/emergency-contacts/page": "app/features/emergency-contacts/page.js", "/blog/page": "app/blog/page.js", "/contact/page": "app/contact/page.js", "/api/time-capsules/upload/route": "app/api/time-capsules/upload/route.js", "/features/trustee-management/page": "app/features/trustee-management/page.js", "/features/digital-assets/page": "app/features/digital-assets/page.js", "/features/last-wishes/page": "app/features/last-wishes/page.js", "/help/page": "app/help/page.js", "/features/digital-vault/page": "app/features/digital-vault/page.js", "/security/page": "app/security/page.js", "/privacy/page": "app/privacy/page.js", "/terms/page": "app/terms/page.js", "/features/service-sunset/page": "app/features/service-sunset/page.js"}