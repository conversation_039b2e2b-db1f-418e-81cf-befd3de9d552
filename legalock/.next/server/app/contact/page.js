(()=>{var e={};e.id=1327,e.ids=[1327],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},38274:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(64824),t(56752),t(35866);var s=t(23191),n=t(88716),a=t(37922),o=t.n(a),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64824)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/contact/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/contact/page.tsx"],u="/contact/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},90179:(e,r,t)=>{Promise.resolve().then(t.bind(t,34478)),Promise.resolve().then(t.t.bind(t,79404,23)),Promise.resolve().then(t.bind(t,71986))},62881:(e,r,t)=>{"use strict";t.d(r,{Z:()=>l});var s=t(17577);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:l,iconNode:c,...d},u)=>(0,s.createElement)("svg",{ref:u,...o,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:a("lucide",i),...d},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(l)?l:[l]])),l=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...o},l)=>(0,s.createElement)(i,{ref:l,iconNode:r,className:a(`lucide-${n(e)}`,t),...o}));return t.displayName=`${e}`,t}},86333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},90434:(e,r,t)=>{"use strict";t.d(r,{default:()=>n.a});var s=t(79404),n=t.n(s)},71986:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var s=t(10326);t(17577);var n=t(90434),a=t(86333);function o({title:e,children:r}){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("header",{className:"bg-white border-b border-gray-200 py-6",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex items-center",children:(0,s.jsxs)(n.default,{href:"/",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm",children:[s.jsx(a.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]})})})}),s.jsx("div",{className:"py-12",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:e}),r]})})}),s.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"text-center text-gray-500 text-sm",children:(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," Legalock. All rights reserved."]})})})})]})}},14120:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(27162).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},21319:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(27162).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},11242:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(27162).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},64824:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y,metadata:()=>b});var s=t(19510),n=t(71159),a=t(57371),o=t(27162);let i=(0,o.Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var l=t(21319);let c=(0,o.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var d=t(14120),u=t(11242),m=t(82355),p=t(27039),x=t(49922),h=t(68570);(0,h.createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/node_modules/@radix-ui/react-label/dist/index.mjs#Label`);let f=(0,h.createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/node_modules/@radix-ui/react-label/dist/index.mjs#Root`);var g=t(46145),v=t(40644);let j=(0,g.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),N=n.forwardRef(({className:e,...r},t)=>s.jsx(f,{ref:t,className:(0,v.cn)(j(),e),...r}));N.displayName=f.displayName,function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}();let b={title:"Contact Us | Legalock",description:"Get in touch with the Legalock team for support, feedback, or partnership inquiries."};function y(){return(0,s.jsxs)(m.Z,{title:"Contact Us",children:[s.jsx("div",{className:"mb-12",children:s.jsx("p",{className:"text-lg text-gray-600",children:"Have questions or need assistance? We're here to help. Fill out the form below or use one of our other contact methods."})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[s.jsx("div",{className:"lg:col-span-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Send Us a Message"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"We'll get back to you as soon as possible."})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("form",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"firstName",children:"First Name"}),s.jsx(x.I,{id:"firstName",placeholder:"John"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"lastName",children:"Last Name"}),s.jsx(x.I,{id:"lastName",placeholder:"Doe"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"email",children:"Email Address"}),s.jsx(x.I,{id:"email",type:"email",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(N,{children:"Inquiry Type"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"support",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"support",id:"support"}),s.jsx(N,{htmlFor:"support",children:"Support"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"feedback",id:"feedback"}),s.jsx(N,{htmlFor:"feedback",children:"Feedback"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"billing",id:"billing"}),s.jsx(N,{htmlFor:"billing",children:"Billing"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"partnership",id:"partnership"}),s.jsx(N,{htmlFor:"partnership",children:"Partnership"})]})]})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(N,{htmlFor:"message",children:"Message"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"message",placeholder:"Please describe your question or issue in detail...",className:"min-h-[150px]"})]}),(0,s.jsxs)(p.z,{type:"submit",className:"w-full",children:[s.jsx(i,{className:"mr-2 h-4 w-4"}),"Send Message"]})]})})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Contact Information"})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx(l.Z,{className:"h-5 w-5 text-primary mr-3 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium",children:"Email"}),s.jsx("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx(c,{className:"h-5 w-5 text-primary mr-3 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium",children:"Phone"}),s.jsx("a",{href:"tel:+18005551234",className:"text-primary hover:underline",children:"+****************"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx(d.Z,{className:"h-5 w-5 text-primary mr-3 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium",children:"Support Hours"}),s.jsx("p",{className:"text-gray-600",children:"Monday - Friday"}),s.jsx("p",{className:"text-gray-600",children:"9:00 AM - 5:00 PM EST"})]})]})]})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Frequently Asked Questions"})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[s.jsx("p",{className:"text-gray-600",children:"Find quick answers to common questions in our Help Center."}),s.jsx(p.z,{variant:"outline",className:"w-full",asChild:!0,children:(0,s.jsxs)(a.default,{href:"/help",children:[s.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Visit Help Center"]})})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-100 rounded-lg p-8 text-center",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Join Our Newsletter"}),s.jsx("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Stay updated with the latest news, feature releases, and digital legacy planning tips from Legalock."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[s.jsx(x.I,{placeholder:"Your email address",type:"email"}),s.jsx(p.z,{children:"Subscribe"})]})]})]})}},82355:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/components/Layout/SimplePageLayout.tsx#default`)},27039:(e,r,t)=>{"use strict";t.d(r,{z:()=>c});var s=t(19510),n=t(71159),a=t(43025),o=t(46145),i=t(40644);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...o},c)=>{let d=n?a.g7:"button";return s.jsx(d,{className:(0,i.cn)(l({variant:r,size:t,className:e})),ref:c,...o})});c.displayName="Button"},49922:(e,r,t)=>{"use strict";t.d(r,{I:()=>o});var s=t(19510),n=t(71159),a=t(40644);let o=n.forwardRef(({className:e,type:r,...t},n)=>s.jsx("input",{type:r,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t}));o.displayName="Input"},40644:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(55761),n=t(62386);function a(...e){return(0,n.m6)((0,s.W)(e))}},48051:(e,r,t)=>{"use strict";t.d(r,{F:()=>a,e:()=>o});var s=t(17577);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}function o(...e){return s.useCallback(a(...e),e)}},34478:(e,r,t)=>{"use strict";t.d(r,{Root:()=>i});var s=t(17577),n=t(45226),a=t(10326),o=s.forwardRef((e,r)=>(0,a.jsx)(n.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var i=o},45226:(e,r,t)=>{"use strict";t.d(r,{WV:()=>o});var s=t(17577);t(60962);var n=t(34214),a=t(10326),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,n.Z8)(`Primitive.${r}`),o=s.forwardRef((e,s)=>{let{asChild:n,...o}=e,i=n?t:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...o,ref:s})});return o.displayName=`Primitive.${r}`,{...e,[r]:o}},{})},34214:(e,r,t)=>{"use strict";t.d(r,{Z8:()=>o,g7:()=>i});var s=t(17577),n=t(48051),a=t(10326);function o(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...a}=e;if(s.isValidElement(t)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,l=function(e,r){let t={...r};for(let s in r){let n=e[s],a=r[s];/^on[A-Z]/.test(s)?n&&a?t[s]=(...e)=>{a(...e),n(...e)}:n&&(t[s]=n):"style"===s?t[s]={...n,...a}:"className"===s&&(t[s]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==s.Fragment&&(l.ref=r?(0,n.F)(r,i):i),s.cloneElement(t,l)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:n,...o}=e,i=s.Children.toArray(n),l=i.find(c);if(l){let e=l.props.children,n=i.map(r=>r!==l?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...o,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...o,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var i=o("Slot"),l=Symbol("radix.slottable");function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,8987,9168,5,8002],()=>t(38274));module.exports=s})();