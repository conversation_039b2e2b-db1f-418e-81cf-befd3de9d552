(()=>{var e={};e.id=5521,e.ids=[5521],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},29200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u}),s(46794),s(56752),s(35866);var r=s(23191),i=s(88716),a=s(37922),o=s.n(a),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let u=["",{children:["auth-fix",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46794)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/auth-fix/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/auth-fix/page.tsx"],d="/auth-fix/page",h={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/auth-fix/page",pathname:"/auth-fix",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},71173:(e,t,s)=>{Promise.resolve().then(s.bind(s,40246))},40246:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(10326),i=s(17577),a=s(35047),o=s(2777),n=s(68136),l=s(91664),u=s(35797),c=s(85999);function d(){let{user:e}=(0,n.a)(),t=(0,a.useRouter)(),[s,d]=(0,i.useState)(!1),[h,p]=(0,i.useState)(null),[x,m]=(0,i.useState)(null),b=async()=>{try{if(d(!0),p(null),m(null),!e){p("You need to be logged in to fix authentication issues.");return}let{data:s,error:r}=await o.supabase.auth.signInWithPassword({email:e.email,password:prompt("Please enter your password to fix authentication:")||""});if(r)throw Error(`Authentication failed: ${r.message}`);if(s.session)m("Authentication fixed successfully! Your session has been restored."),c.Am.success("Authentication fixed successfully!"),setTimeout(()=>{t.push("/dashboard")},2e3);else throw Error("Failed to create a new session.")}catch(e){console.error("Auth fix error:",e),p(e.message||"An error occurred while fixing authentication"),c.Am.error("Failed to fix authentication")}finally{d(!1)}};return r.jsx(u.Z,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Fix Authentication Issues"}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Authentication Problem Detected"}),r.jsx("p",{className:"mb-4",children:"We've detected an issue with your authentication that's causing API errors. This can happen when:"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 mb-4 space-y-2",children:[r.jsx("li",{children:"Your session has expired"}),r.jsx("li",{children:"You're logged in with the custom auth system but not with Supabase"}),r.jsx("li",{children:"Your browser cookies are out of sync"})]}),r.jsx("p",{className:"mb-6",children:"Click the button below to fix these issues. You'll need to enter your password to re-authenticate."}),h&&r.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mb-4",children:r.jsx("p",{className:"text-red-600",children:h})}),x&&r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-md p-4 mb-4",children:r.jsx("p",{className:"text-green-600",children:x})}),r.jsx(l.z,{onClick:b,disabled:s,className:"w-full",children:s?"Fixing Authentication...":"Fix Authentication Issues"})]}),(0,r.jsxs)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-6",children:[r.jsx("h3",{className:"text-lg font-semibold mb-2 text-amber-800",children:"Why am I seeing this?"}),r.jsx("p",{className:"text-amber-700 mb-4",children:"Your dashboard is experiencing API errors because of authentication issues. The errors you're seeing (404 Not Found and 406 Not Acceptable) occur when API requests don't have the proper authentication."}),r.jsx("p",{className:"text-amber-700",children:"After fixing this issue, you should be able to use all features of the application without errors."})]})]})})}},46794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/auth-fix/page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,8987,9168,5981,6292,285,8002,5797],()=>s(29200));module.exports=r})();