"use strict";(()=>{var e={};e.id=3235,e.ids=[3235],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},802:(e,s,t)=>{t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>o}),t(48578),t(56752),t(35866);var a=t(23191),r=t(88716),i=t(37922),n=t.n(i),l=t(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o=["",{children:["features",{children:["digital-vault",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,48578)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/features/digital-vault/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/features/digital-vault/page.tsx"],m="/features/digital-vault/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/features/digital-vault/page",pathname:"/features/digital-vault",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},83113:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(27162).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},57956:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(27162).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},48578:(e,s,t)=>{t.r(s),t.d(s,{default:()=>h,metadata:()=>x});var a=t(19510);t(71159);var r=t(57371),i=t(33515),n=t(83113),l=t(57956),c=t(83218),o=t(1733),d=t(49985);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var m=t(27039);let x={title:"Digital Vault | Legalock",description:"Store important documents in an encrypted vault accessible only to designated trustees after your passing."};function h(){return a.jsx(d.Z,{title:"Digital Vault",description:"Store important documents in an encrypted vault accessible only to designated trustees after your passing.",iconName:"HardDrive",iconColor:"text-indigo-100",iconBgColor:"bg-gradient-to-r from-indigo-600 to-indigo-800",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Secure Storage for Your Most Important Documents"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"Your important documents—wills, insurance policies, property deeds, and personal letters—deserve the highest level of protection and accessibility when needed."}),a.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"Legalock's Digital Vault uses advanced encryption to keep your documents secure during your lifetime while ensuring they're accessible to your trustees when the time comes."})]}),(0,a.jsxs)("div",{className:"bg-gray-100 p-6 rounded-lg",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Why Use a Digital Vault?"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Physical documents can be lost, damaged, or destroyed"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Trustees may not know where to find critical documents"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Encrypted storage protects sensitive information from unauthorized access"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Centralized access simplifies the estate management process"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"How It Works"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4",children:a.jsx(n.Z,{className:"h-6 w-6 text-indigo-600"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Upload Documents"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Securely store important files"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx("p",{className:"text-gray-700",children:"Upload wills, insurance policies, property deeds, personal letters, and other important documents to your secure vault."})})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4",children:a.jsx(l.Z,{className:"h-6 w-6 text-indigo-600"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Automatic Encryption"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Military-grade protection"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx("p",{className:"text-gray-700",children:"All documents are automatically encrypted using AES-256 encryption, ensuring they remain private and secure during your lifetime."})})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4",children:a.jsx(c.Z,{className:"h-6 w-6 text-indigo-600"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Trustee Access"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Available when needed"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx("p",{className:"text-gray-700",children:"After your passing is verified, your designated trustees gain access to the documents you've specified for them."})})]})]})]}),(0,a.jsxs)("div",{className:"mb-16 bg-gray-50 p-8 rounded-xl border border-gray-200",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"The Trustee's Role"}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("p",{className:"text-lg text-gray-700",children:"When you designate someone as a trustee for your Digital Vault, you're entrusting them with access to your most important documents. Here's what your trustees will do:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Before Your Passing"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Receive notification of their designation as a vault trustee"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Accept the trustee role and responsibilities"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Have no access to your vault documents"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"After Your Passing"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Report your passing through the Legalock platform"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Gain access to your vault documents after verification"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Download and view the documents you've shared with them"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Use these documents to manage your estate according to your wishes"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(i.Z,{className:"h-5 w-5 text-indigo-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Share documents with relevant parties as specified in your instructions"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Key Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(i.Z,{className:"h-5 w-5 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"AES-256 Encryption"}),a.jsx("p",{className:"text-gray-700",children:"Military-grade encryption ensures your documents remain private and secure until they need to be accessed."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(i.Z,{className:"h-5 w-5 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Document Categories"}),a.jsx("p",{className:"text-gray-700",children:"Organize documents by category for easy management: legal, financial, personal, medical, and more."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(i.Z,{className:"h-5 w-5 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Granular Access Control"}),a.jsx("p",{className:"text-gray-700",children:"Specify which trustees can access which documents, ensuring sensitive information is only shared with appropriate individuals."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(i.Z,{className:"h-5 w-5 text-indigo-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Document Notes"}),a.jsx("p",{className:"text-gray-700",children:"Add detailed notes to each document, providing context and instructions for your trustees."})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Recommended Documents to Store"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Legal Documents"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Last Will and Testament"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Power of Attorney"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Trust Documents"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Property Deeds"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Marriage Certificates"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Financial Documents"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Insurance Policies"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Investment Statements"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Tax Returns"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Pension Information"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Loan Documents"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Personal Documents"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Birth Certificates"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Medical Records"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Personal Letters"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Family Photos"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(n.Z,{className:"h-4 w-4 text-indigo-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Funeral Instructions"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Secure Your Important Documents Today"}),a.jsx("p",{className:"text-lg text-gray-700 mb-8 max-w-2xl mx-auto",children:"Don't leave your loved ones searching for critical documents. Create your Digital Vault now and ensure everything is accessible when needed."}),a.jsx(m.z,{size:"lg",asChild:!0,children:(0,a.jsxs)(r.default,{href:"/register",children:["Get Started for Free",a.jsx(o.Z,{className:"ml-2 h-5 w-5"})]})})]})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,8987,9168,5981,5,8002,8585],()=>t(802));module.exports=a})();