"use strict";(()=>{var e={};e.id=435,e.ids=[435],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},63732:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o}),r(9930),r(56752),r(35866);var a=r(23191),t=r(88716),n=r(37922),i=r.n(n),l=r(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let o=["",{children:["features",{children:["service-sunset",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9930)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/features/service-sunset/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/features/service-sunset/page.tsx"],x="/features/service-sunset/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/features/service-sunset/page",pathname:"/features/service-sunset",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},14120:(e,s,r)=>{r.d(s,{Z:()=>a});let a=(0,r(27162).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26299:(e,s,r)=>{r.d(s,{Z:()=>a});let a=(0,r(27162).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},9930:(e,s,r)=>{r.r(s),r.d(s,{default:()=>u,metadata:()=>h});var a=r(19510);r(71159);var t=r(57371),n=r(33515),i=r(26299),l=r(14120),c=r(83218);let o=(0,r(27162).Z)("Sunset",[["path",{d:"M12 10V2",key:"16sf7g"}],["path",{d:"m4.93 10.93 1.41 1.41",key:"2a7f42"}],["path",{d:"M2 18h2",key:"j10viu"}],["path",{d:"M20 18h2",key:"wocana"}],["path",{d:"m19.07 10.93-1.41 1.41",key:"15zs5n"}],["path",{d:"M22 22H2",key:"19qnx5"}],["path",{d:"m16 6-4 4-4-4",key:"6wukr"}],["path",{d:"M16 18a4 4 0 0 0-8 0",key:"1lzouq"}]]);var d=r(1733),x=r(49985),m=r(27039);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();let h={title:"Service Sunset | Legalock",description:"List services and subscriptions that should be canceled after your passing with detailed cancellation instructions."};function u(){return a.jsx(x.Z,{title:"Service Sunset",description:"List services and subscriptions that should be canceled after your passing with detailed cancellation instructions.",iconName:"Sunset",iconColor:"text-orange-100",iconBgColor:"bg-gradient-to-r from-orange-600 to-orange-800",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Why Use Service Sunset?"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"In today's subscription economy, most of us have numerous recurring payments for digital services, memberships, and subscriptions. When someone passes away, these services continue to bill until manually canceled, potentially draining estate resources."}),a.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"Legalock's Service Sunset feature helps you create a comprehensive list of services that should be canceled after your passing, complete with account details and cancellation instructions for your trustees."})]}),(0,a.jsxs)("div",{className:"bg-gray-100 p-6 rounded-lg",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Did You Know?"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"The average American has 12 active subscriptions"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Uncanceled subscriptions cost estates an average of $348 per year"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Many services have no automatic process for cancellation after death"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Some services require specific documentation to cancel accounts of deceased users"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"How It Works"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4",children:a.jsx(i.Z,{className:"h-6 w-6 text-orange-600"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Document Services"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Create a comprehensive list"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx("p",{className:"text-gray-700",children:"Document all your subscriptions, memberships, and recurring services, including account details and cancellation methods."})})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4",children:a.jsx(l.Z,{className:"h-6 w-6 text-orange-600"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Prioritize Cancellations"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Set importance levels"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx("p",{className:"text-gray-700",children:"Mark services as high, medium, or low priority to help your trustees know which ones to cancel first to prevent unnecessary charges."})})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4",children:a.jsx(c.Z,{className:"h-6 w-6 text-orange-600"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Trustee Access"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Available when needed"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx("p",{className:"text-gray-700",children:"After your passing is verified, your trustees gain access to your service list and can methodically cancel each one according to your instructions."})})]})]})]}),(0,a.jsxs)("div",{className:"mb-16 bg-gray-50 p-8 rounded-xl border border-gray-200",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"The Trustee's Role"}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("p",{className:"text-lg text-gray-700",children:"When you designate someone as a trustee for your Service Sunset, you're entrusting them with canceling your subscriptions and services. Here's what your trustees will do:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Before Your Passing"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Receive notification of their designation as a trustee"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Accept the trustee role and responsibilities"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Have no access to your service information"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"After Your Passing"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Report your passing through the Legalock platform"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Gain access to your service list after verification"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Cancel services according to priority level"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Follow your detailed cancellation instructions"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Mark services as canceled in the platform"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[a.jsx(n.Z,{className:"h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5"}),a.jsx("span",{className:"text-gray-700",children:"Request refunds for prepaid services when applicable"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Key Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(n.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Service Categories"}),a.jsx("p",{className:"text-gray-700",children:"Organize services by category: streaming, utilities, memberships, subscriptions, and more for easy management."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(n.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Priority Levels"}),a.jsx("p",{className:"text-gray-700",children:"Assign high, medium, or low priority to each service, helping trustees focus on canceling the most important or expensive ones first."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(n.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Detailed Cancellation Instructions"}),a.jsx("p",{className:"text-gray-700",children:"Provide step-by-step instructions for canceling each service, including phone numbers, websites, and any special requirements."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(n.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Auto-Renewal Tracking"}),a.jsx("p",{className:"text-gray-700",children:"Flag services with auto-renewal and track renewal dates to help trustees cancel before the next billing cycle."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(n.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Cost Tracking"}),a.jsx("p",{className:"text-gray-700",children:"Record the cost of each service and billing frequency to help trustees understand the financial impact of each subscription."})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-4 flex-shrink-0",children:a.jsx(n.Z,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Completion Tracking"}),a.jsx("p",{className:"text-gray-700",children:"Trustees can mark services as canceled, creating a clear record of which subscriptions have been addressed."})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Common Services to Include"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Digital Subscriptions"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Streaming Services (Netflix, Hulu, etc.)"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Music Services (Spotify, Apple Music)"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Cloud Storage (Dropbox, Google Drive)"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Software Subscriptions (Adobe, Microsoft)"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Gaming Services (Xbox Game Pass, PlayStation Plus)"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Utilities & Services"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Mobile Phone Plans"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Internet Service"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Cable TV"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Electricity & Gas"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Water & Sewage"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Memberships & Other"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Gym Memberships"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Club Memberships"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Subscription Boxes"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Magazine & Newspaper Subscriptions"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o,{className:"h-4 w-4 text-orange-500 mr-2"}),a.jsx("span",{className:"text-gray-700",children:"Charitable Recurring Donations"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Start Documenting Your Services Today"}),a.jsx("p",{className:"text-lg text-gray-700 mb-8 max-w-2xl mx-auto",children:"Don't leave your loved ones dealing with unwanted subscriptions and recurring charges. Create your Service Sunset plan now and ensure a smooth transition."}),a.jsx(m.z,{size:"lg",asChild:!0,children:(0,a.jsxs)(t.default,{href:"/register",children:["Get Started for Free",a.jsx(d.Z,{className:"ml-2 h-5 w-5"})]})})]})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[9276,8987,9168,5981,5,8002,8585],()=>r(63732));module.exports=a})();