(()=>{var e={};e.id=6291,e.ids=[6291],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},64214:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>d}),r(30658),r(56752),r(35866);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["features",{children:["trustee-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30658)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/features/trustee-management/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/features/trustee-management/page.tsx"],u="/features/trustee-management/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/features/trustee-management/page",pathname:"/features/trustee-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81587:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79404,23)),Promise.resolve().then(r.bind(r,60327))},86333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},88319:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},55002:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},67427:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70677:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Sunset",[["path",{d:"M12 10V2",key:"16sf7g"}],["path",{d:"m4.93 10.93 1.41 1.41",key:"2a7f42"}],["path",{d:"M2 18h2",key:"j10viu"}],["path",{d:"M20 18h2",key:"wocana"}],["path",{d:"m19.07 10.93-1.41 1.41",key:"15zs5n"}],["path",{d:"M22 22H2",key:"19qnx5"}],["path",{d:"m16 6-4 4-4-4",key:"6wukr"}],["path",{d:"M16 18a4 4 0 0 0-8 0",key:"1lzouq"}]])},74975:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},24061:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(79404),a=r.n(s)},60327:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(10326);r(17577);var a=r(90434),n=r(88319),i=r(55002),o=r(24061),l=r(74975),d=r(70677),c=r(67427),u=r(86333),x=r(91664),g=r(51223);function h({title:e,description:t,iconName:r,iconColor:h,iconBgColor:m,children:p}){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("header",{className:"bg-white border-b border-gray-200 py-6",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex items-center",children:(0,s.jsxs)(a.default,{href:"/",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm",children:[s.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]})})})}),s.jsx("div",{className:(0,g.cn)("py-16",m),children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[s.jsx("div",{className:"inline-flex items-center justify-center p-3 rounded-full bg-white/20 mb-6",children:(()=>{switch(r){case"Database":default:return s.jsx(n.Z,{className:(0,g.cn)("h-10 w-10",h)});case"HardDrive":return s.jsx(i.Z,{className:(0,g.cn)("h-10 w-10",h)});case"Users":return s.jsx(o.Z,{className:(0,g.cn)("h-10 w-10",h)});case"UserPlus":return s.jsx(l.Z,{className:(0,g.cn)("h-10 w-10",h)});case"Sunset":return s.jsx(d.Z,{className:(0,g.cn)("h-10 w-10",h)});case"Heart":return s.jsx(c.Z,{className:(0,g.cn)("h-10 w-10",h)})}})()}),s.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:e}),s.jsx("p",{className:"text-xl text-white/90 mb-8",children:t}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[s.jsx(x.z,{size:"lg",className:"bg-white text-gray-900 hover:bg-gray-100",asChild:!0,children:s.jsx(a.default,{href:"/register",children:"Get Started"})}),s.jsx(x.z,{size:"lg",variant:"outline",className:"border-white text-white bg-white/10 hover:bg-white/20",asChild:!0,children:s.jsx(a.default,{href:"/login",children:"Sign In"})})]})]})})}),s.jsx("div",{className:"py-16",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:p})}),s.jsx("div",{className:"bg-gray-100 py-16",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Ready to secure your digital legacy?"}),s.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Join thousands of users who trust Legalock to protect their digital assets and final wishes."}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[s.jsx(x.z,{size:"lg",asChild:!0,children:s.jsx(a.default,{href:"/register",children:"Get Started for Free"})}),s.jsx(x.z,{size:"lg",variant:"outline",asChild:!0,children:s.jsx(a.default,{href:"/login",children:"Sign In"})})]})]})})}),s.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"text-center text-gray-500 text-sm",children:(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," Legalock. All rights reserved."]})})})})]})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(10326),a=r(17577),n=r(34214),i=r(79360),o=r(51223);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},d)=>{let c=a?n.g7:"button";return s.jsx(c,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:d,...i})});d.displayName="Button"},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(41135),a=r(31009);function n(...e){return(0,a.m6)((0,s.W)(e))}},1733:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(27162).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},30658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(19510);r(71159);var a=r(57371),n=r(1733),i=r(49985),o=r(27039);let l={title:"Trustee Management | Legalock",description:"Designate trusted individuals to manage your digital legacy and control what information each trustee can access."};function d(){return s.jsx(i.Z,{title:"Trustee Management",description:"Designate trusted individuals to manage your digital legacy and control what information each trustee can access.",iconName:"Users",iconColor:"text-purple-100",iconBgColor:"bg-gradient-to-r from-purple-600 to-purple-800",children:s.jsx("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"text-center py-16",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Coming Soon"}),s.jsx("p",{className:"text-lg text-gray-700 mb-8 max-w-2xl mx-auto",children:"We're currently working on detailed information about our Trustee Management feature. Check back soon for a comprehensive guide on how to designate and manage trustees for your digital legacy."}),s.jsx(o.z,{size:"lg",asChild:!0,children:(0,s.jsxs)(a.default,{href:"/register",children:["Get Started for Free",s.jsx(n.Z,{className:"ml-2 h-5 w-5"})]})})]})})})}},49985:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/components/Layout/FeaturePageLayout.tsx#default`)},27039:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(19510),a=r(71159),n=r(43025),i=r(46145),o=r(40644);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},d)=>{let c=a?n.g7:"button";return s.jsx(c,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:d,...i})});d.displayName="Button"},40644:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(55761),a=r(62386);function n(...e){return(0,a.m6)((0,s.W)(e))}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,8987,9168,5981,5,8002],()=>r(64214));module.exports=s})();