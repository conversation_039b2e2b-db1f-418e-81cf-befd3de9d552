"use strict";(()=>{var e={};e.id=7936,e.ids=[7936],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},51095:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var s={};o.r(s),o.d(s,{GET:()=>u});var t=o(49303),n=o(88716),a=o(60670),i=o(20344),l=o(71615),c=o(87070);async function u(e){let r=new URL(e.url),o=r.searchParams.get("code"),s=r.searchParams.get("provider"),t=r.searchParams.get("redirect_to"),n={};r.searchParams.forEach((e,r)=>{n[r]=e}),console.log("Auth callback route triggered",{code:!!o,provider:s,redirectTo:t,url:e.url,allParams:n,headers:Object.fromEntries(e.headers)});try{if(!o)return console.error("No code parameter in callback URL"),c.NextResponse.redirect(new URL("/login?error=no-code",e.url));{let r=(0,l.cookies)(),s=(0,i.createRouteHandlerClient)({cookies:()=>r});console.log("Exchanging code for session...");let{data:t,error:n}=await s.auth.exchangeCodeForSession(o);if(n)return console.error("Error exchanging code for session:",n),c.NextResponse.redirect(new URL(`/login?error=auth&message=${encodeURIComponent(n.message)}`,e.url));if(!t.session)return console.error("No session data returned"),c.NextResponse.redirect(new URL("/login?error=no-session",e.url));{if(console.log("Session obtained successfully"),t.session.user?.app_metadata?.provider==="google"){console.log("Google user detected, checking for profile");let{data:e,error:r}=await s.from("profiles").select("*").eq("id",t.session.user.id).single();if(r&&"PGRST116"!==r.code&&console.error("Error checking for existing profile:",r),e)console.log("Existing profile found for Google user");else{console.log("No profile found, creating one for Google user");let e="",r="",o=t.session.user.user_metadata?.full_name||t.session.user.user_metadata?.name||t.session.user.user_metadata?.email?.split("@")[0]||"";if(o){let s=o.split(" ");e=s[0]||"",r=s.slice(1).join(" ")||""}!e&&t.session.user.email&&(e=t.session.user.email.split("@")[0]||"User");let n={id:t.session.user.id,first_name:e,last_name:r,email:t.session.user.email,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};console.log("Creating profile with data:",n);let{error:a}=await s.from("profiles").insert(n);a?console.error("Error creating profile for Google user:",a):console.log("Profile created successfully for Google user")}}console.log("Redirecting to dashboard"),await new Promise(e=>setTimeout(e,500));let e=new URL("/dashboard","https://legalock.com").toString();return console.log("Redirecting directly to dashboard:",e),c.NextResponse.redirect(e)}}}catch(r){return console.error("Unexpected error in auth callback:",r),c.NextResponse.redirect(new URL(`/login?error=unexpected&message=${encodeURIComponent(r instanceof Error?r.message:"Unknown error")}`,e.url))}}let d=new t.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/auth/callback/route",pathname:"/auth/callback",filename:"route",bundlePath:"app/auth/callback/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/auth/callback/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:g,serverHooks:m}=d,f="/auth/callback/route";function x(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}}};var r=require("../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),s=r.X(0,[9276,135,958],()=>o(51095));module.exports=s})();