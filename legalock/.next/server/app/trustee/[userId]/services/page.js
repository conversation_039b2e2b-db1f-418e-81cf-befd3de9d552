(()=>{var e={};e.id=821,e.ids=[821,2777],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},46733:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(79148),r(56752),r(35866);var s=r(23191),a=r(88716),n=r(37922),o=r.n(n),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["trustee",{children:["[userId]",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,79148)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/services/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/services/page.tsx"],u="/trustee/[userId]/services/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/trustee/[userId]/services/page",pathname:"/trustee/[userId]/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96009:(e,t,r)=>{Promise.resolve().then(r.bind(r,73153))},37358:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},18019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85962:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},88307:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},50949:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},73153:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>D});var s=r(10326),a=r(17577),n=r(35047),o=r(68136);r(2777);var i=r(78388),l=r(50949),c=r(18019),d=r(30361),u=r(86333),m=r(88307),p=r(85962),h=r(62881);let x=(0,h.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),f=(0,h.Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var v=r(37358);let b=(0,h.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var g=r(91664),N=r(41190),j=r(9280),O=r(44794),y=r(85999),w=r(65720),_=r(90434);function D(){(0,n.useRouter)();let e=(0,n.useParams)().userId,{user:t,loading:r}=(0,o.a)(),[h,D]=(0,a.useState)([]),[U,E]=(0,a.useState)(""),[M,k]=(0,a.useState)("all"),[C,I]=(0,a.useState)("all"),[L,T]=(0,a.useState)(!0),[S,Z]=(0,a.useState)("all"),[F,P]=(0,a.useState)(""),[q,A]=(0,a.useState)(!1),[z,R]=(0,a.useState)({}),J=(t,r)=>{D(h.map(e=>e.id===t?{...e,completed:r}:e));let s={...z,[t]:r};R(s),localStorage.setItem(`completed_services_${e}`,JSON.stringify(s)),r?y.Am.success("Service marked as canceled"):y.Am.info("Service marked as pending")},V=e=>{let t=i.ND.find(t=>t.value===e);return t?t.label:e},X=e=>{let t=i.ww.find(t=>t.value===e);return t?t.label:e},B=e=>{switch(e){case"high":return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"destructive",className:"flex items-center gap-1",children:[s.jsx(l.Z,{className:"h-3 w-3"})," High"]});case"medium":return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"default",className:"flex items-center gap-1 bg-amber-500",children:[s.jsx(c.Z,{className:"h-3 w-3"})," Medium"]});case"low":return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"flex items-center gap-1 text-green-600 border-green-200 bg-green-50",children:[s.jsx(d.Z,{className:"h-3 w-3"})," Low"]});default:return s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e})}};return r||!q?(0,s.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-500",children:"Checking access..."})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx(g.z,{variant:"outline",asChild:!0,className:"mb-6",children:(0,s.jsxs)(_.default,{href:"/trustee/dashboard",children:[s.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Back to Trustee Dashboard"]})}),s.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Service Sunset List"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-6",children:["Services that need to be canceled for ",F]}),s.jsx("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx(c.Z,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-blue-800",children:"Trustee Instructions"}),(0,s.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["As a trustee, you are responsible for canceling these services on behalf of ",F,". Please follow the cancellation instructions for each service and mark them as completed when done."]})]})]})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"all",value:S,onValueChange:Z,className:"mb-6",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",children:"All Services"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"pending",children:"Pending"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"completed",children:"Completed"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"auto_renewal",children:"Auto-Renewal"})]})}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-1/2",children:[s.jsx(N.I,{type:"text",placeholder:"Search services...",value:U,onChange:e=>E(e.target.value),className:"pr-10"}),s.jsx(m.Z,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:M,onValueChange:k,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full md:w-[180px]",children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"All Categories"})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",children:"All Categories"}),i.ND.map(e=>s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:C,onValueChange:I,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full md:w-[180px]",children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"All Priorities"})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",children:"All Priorities"}),i.BG.map(e=>s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))]})]})]})]}),L?(0,s.jsxs)("div",{className:"text-center py-10",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"Loading services..."})]}):0===h.length?(0,s.jsxs)("div",{className:"text-center py-16 bg-gray-50 rounded-lg border border-gray-200",children:[s.jsx(p.Z,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No services found"}),s.jsx("p",{className:"text-gray-500 mb-6 max-w-md mx-auto",children:"There are no services to cancel for this user based on your current filters."})]}):s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.filter(e=>"pending"===S?!e.completed:"completed"===S?e.completed:"auto_renewal"!==S||e.auto_renewal).map(e=>(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`overflow-hidden hover:shadow-md transition-shadow ${e.completed?"bg-gray-50 border-gray-200":""}`,children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`text-lg ${e.completed?"text-gray-500":""}`,children:e.name}),B(e.priority)]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:V(e.category)})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("div",{className:"space-y-4",children:[e.description&&s.jsx("p",{className:`text-sm ${e.completed?"text-gray-500":"text-gray-600"}`,children:e.description}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.website&&(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(x,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsxs)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline truncate max-w-[150px] flex items-center",children:["Website",s.jsx(f,{className:"h-3 w-3 ml-1"})]})]}),e.renewal_date&&(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(v.Z,{className:"h-4 w-4 text-gray-500 mr-2"}),s.jsx("span",{className:e.completed?"text-gray-500":"text-gray-700",children:(0,w.WU)(new Date(e.renewal_date),"MMM d, yyyy")})]}),e.cost_per_period&&(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(b,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,s.jsxs)("span",{className:e.completed?"text-gray-500":"text-gray-700",children:["$",e.cost_per_period,e.period?`/${e.period.charAt(0)}`:""]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(p.Z,{className:"h-4 w-4 text-gray-500 mr-2"}),s.jsx("span",{className:e.completed?"text-gray-500":"text-gray-700",children:X(e.cancellation_method)})]})]}),e.auto_renewal&&(0,s.jsxs)("div",{className:"bg-amber-50 text-amber-800 text-xs p-2 rounded-md flex items-center",children:[s.jsx(l.Z,{className:"h-3 w-3 mr-1"}),"Auto-renewal enabled"]}),e.cancellation_instructions&&(0,s.jsxs)("div",{className:"mt-4",children:[s.jsx("h4",{className:"text-sm font-medium mb-2",children:"Cancellation Instructions:"}),s.jsx("p",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-200",children:e.cancellation_instructions})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 pt-2",children:[s.jsx(j.X,{id:`completed-${e.id}`,checked:e.completed,onCheckedChange:t=>J(e.id,t)}),s.jsx(O._,{htmlFor:`completed-${e.id}`,className:"text-sm cursor-pointer",children:"Mark as canceled"})]})]})})]},e.id))})]})}(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var s=r(10326),a=r(17577),n=r(34214),o=r(79360),i=r(51223);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},c)=>{let d=a?n.g7:"button";return s.jsx(d,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:c,...o})});c.displayName="Button"},9280:(e,t,r)=>{"use strict";r.d(t,{X:()=>l});var s=r(10326),a=r(17577),n=r(46074),o=r(32933),i=r(51223);let l=a.forwardRef(({className:e,...t},r)=>s.jsx(n.fC,{ref:r,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:s.jsx(n.z$,{className:(0,i.cn)("flex items-center justify-center text-current"),children:s.jsx(o.Z,{className:"h-4 w-4"})})}));l.displayName=n.fC.displayName},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>o});var s=r(10326),a=r(17577),n=r(51223);let o=a.forwardRef(({className:e,type:t,...r},a)=>s.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));o.displayName="Input"},44794:(e,t,r)=>{"use strict";r.d(t,{_:()=>c});var s=r(10326),a=r(17577),n=r(34478),o=r(79360),i=r(51223);let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},r)=>s.jsx(n.Root,{ref:r,className:(0,i.cn)(l(),e),...t}));c.displayName=n.Root.displayName},2777:(e,t,r)=>{"use strict";r.d(t,{supabase:()=>i});var s=r(56292);let a="https://ccwvtcudztphwwzzgwvg.supabase.co",n=null,o=null,i=(0,s.eI)(a,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(o)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";o=(0,s.eI)(a,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(41135),a=r(31009);function n(...e){return(0,a.m6)((0,s.W)(e))}},78388:(e,t,r)=>{"use strict";r.d(t,{BG:()=>n,ND:()=>s,ww:()=>a});let s=[{value:"subscription",label:"Subscription Service"},{value:"utility",label:"Utility"},{value:"financial",label:"Financial Service"},{value:"entertainment",label:"Entertainment"},{value:"other",label:"Other"}],a=[{value:"online",label:"Online"},{value:"phone",label:"Phone"},{value:"email",label:"Email"},{value:"mail",label:"Mail"},{value:"in_person",label:"In Person"},{value:"automatic",label:"Automatic"}],n=[{value:"high",label:"High",description:"Services that should be canceled immediately (e.g., financial services with recurring charges)"},{value:"medium",label:"Medium",description:"Services that should be canceled within a month (e.g., utilities, subscriptions)"},{value:"low",label:"Low",description:"Services that can be canceled eventually (e.g., free accounts, email lists)"}]},79148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/services/page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{Root:()=>i});var s=r(17577),a=r(45226),n=r(10326),o=s.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,8987,9168,5981,6292,7600,4272,8002],()=>r(46733));module.exports=s})();