(()=>{var e={};e.id=948,e.ids=[948,2777],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},67562:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>d}),t(27113),t(56752),t(35866);var s=t(23191),n=t(88716),o=t(37922),a=t.n(o),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(r,c);let d=["",{children:["trustee",{children:["[userId]",{children:["last-wishes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27113)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/last-wishes/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/last-wishes/page.tsx"],u="/trustee/[userId]/last-wishes/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/trustee/[userId]/last-wishes/page",pathname:"/trustee/[userId]/last-wishes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63753:(e,r,t)=>{Promise.resolve().then(t.bind(t,84946))},86333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},30361:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},69669:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},67427:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},18019:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},53080:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},76846:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("PawPrint",[["circle",{cx:"11",cy:"4",r:"2",key:"vol9p0"}],["circle",{cx:"18",cy:"8",r:"2",key:"17gozi"}],["circle",{cx:"20",cy:"16",r:"2",key:"1v9bxh"}],["path",{d:"M9 10a5 5 0 0 1 5 5v3.5a3.5 3.5 0 0 1-6.84 1.045Q6.52 17.48 4.46 16.84A3.5 3.5 0 0 1 5.5 10Z",key:"1ydw1z"}]])},24061:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},90434:(e,r,t)=>{"use strict";t.d(r,{default:()=>n.a});var s=t(79404),n=t.n(s)},84946:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>_});var s=t(10326),n=t(17577),o=t(35047),a=t(90434),i=t(68136);t(2777);var c=t(86333),d=t(67427),l=t(53080),u=t(30361),m=t(69669),h=t(76846),p=t(24061),x=t(18019),f=t(91664);function _(){(0,o.useRouter)(),(0,o.useParams)().userId;let{user:e,loading:r}=(0,i.a)(),[t,_]=(0,n.useState)(null),[O,N]=(0,n.useState)(!0),[g,j]=(0,n.useState)(""),[b,y]=(0,n.useState)(!1);return r||!b?(0,s.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-500",children:"Checking access..."})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[s.jsx(f.z,{variant:"outline",asChild:!0,className:"mb-6",children:(0,s.jsxs)(a.default,{href:"/trustee/dashboard",children:[s.jsx(c.Z,{className:"mr-2 h-4 w-4"}),"Back to Trustee Dashboard"]})}),(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[s.jsx(d.Z,{className:"h-8 w-8 text-red-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:[g,"'s Last Wishes"]}),s.jsx("p",{className:"text-gray-600",children:"Final wishes and instructions"})]})]}),O?(0,s.jsxs)("div",{className:"text-center py-10",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"Loading last wishes..."})]}):t?(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[t.funeral_wishes&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Funeral & Memorial Preferences"})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx("p",{className:"whitespace-pre-wrap",children:t.funeral_wishes})})]}),(t.burial_wishes||t.burial_option)&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Burial & Remains Preferences"})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.burial_option&&(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Preferred Option:"}),(0,s.jsxs)("p",{className:"font-medium",children:["burial"===t.burial_option&&"Traditional Burial","cremation"===t.burial_option&&"Cremation","green_burial"===t.burial_option&&"Green/Natural Burial","donation"===t.burial_option&&"Body Donation to Science","other"===t.burial_option&&"Other"]})]}),t.burial_wishes&&(0,s.jsxs)("div",{children:[t.burial_option&&s.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Additional Details:"}),s.jsx("p",{className:"whitespace-pre-wrap",children:t.burial_wishes})]})]})]}),t.personal_messages&&t.show_personal_messages&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Personal Messages"})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx("p",{className:"whitespace-pre-wrap",children:t.personal_messages})})]}),t.other_wishes&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Other Wishes"})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx("p",{className:"whitespace-pre-wrap",children:t.other_wishes})})]})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[s.jsx(l.Z,{className:"h-5 w-5 text-green-600 mr-2"}),"Organ Donation"]})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx("div",{className:"flex items-center mb-4",children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{className:t.is_organ_donor?"bg-green-100 text-green-800 border-green-200":"bg-gray-100 text-gray-800 border-gray-200",children:t.is_organ_donor?(0,s.jsxs)(s.Fragment,{children:[s.jsx(u.Z,{className:"h-3 w-3 mr-1"})," Registered Organ Donor"]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(m.Z,{className:"h-3 w-3 mr-1"})," Not an Organ Donor"]})})}),t.is_organ_donor&&(0,s.jsxs)("div",{className:"space-y-3",children:[t.organ_donor_country&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Registered Country:"}),(0,s.jsxs)("p",{className:"font-medium",children:["USA"===t.organ_donor_country&&"United States","CAN"===t.organ_donor_country&&"Canada","GBR"===t.organ_donor_country&&"United Kingdom","AUS"===t.organ_donor_country&&"Australia","NZL"===t.organ_donor_country&&"New Zealand","DEU"===t.organ_donor_country&&"Germany","FRA"===t.organ_donor_country&&"France","ESP"===t.organ_donor_country&&"Spain","ITA"===t.organ_donor_country&&"Italy","JPN"===t.organ_donor_country&&"Japan","OTHER"===t.organ_donor_country&&"Other"]})]}),"USA"===t.organ_donor_country&&t.organ_donor_state&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Registered State:"}),s.jsx("p",{className:"font-medium",children:t.organ_donor_state})]})]})]})]}),t.has_pets&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[s.jsx(h.Z,{className:"h-5 w-5 text-amber-600 mr-2"}),"Pet Care Instructions"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:t.pet_care_instructions?s.jsx("p",{className:"whitespace-pre-wrap",children:t.pet_care_instructions}):s.jsx("p",{className:"text-gray-500 italic",children:"No specific pet care instructions provided."})})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[s.jsx(p.Z,{className:"h-5 w-5 text-blue-600 mr-2"}),"Trustee Responsibilities"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx("div",{className:"bg-blue-50 p-4 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx(x.Z,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0"}),s.jsx("div",{children:(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:["As a trustee, you are responsible for carrying out ",g,"'s last wishes to the best of your ability. Please respect their preferences and instructions."]})})]})})})]})]})]}):(0,s.jsxs)("div",{className:"text-center py-16 bg-gray-50 rounded-lg border border-gray-200",children:[s.jsx(d.Z,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Last Wishes Found"}),(0,s.jsxs)("p",{className:"text-gray-500 mb-6 max-w-md mx-auto",children:[g," did not document any last wishes."]})]})]})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),t(85999)},91664:(e,r,t)=>{"use strict";t.d(r,{z:()=>d});var s=t(10326),n=t(17577),o=t(34214),a=t(79360),i=t(51223);let c=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...a},d)=>{let l=n?o.g7:"button";return s.jsx(l,{className:(0,i.cn)(c({variant:r,size:t,className:e})),ref:d,...a})});d.displayName="Button"},2777:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>i});var s=t(56292);let n="https://ccwvtcudztphwwzzgwvg.supabase.co",o=null,a=null,i=(0,s.eI)(n,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(a)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";a=(0,s.eI)(n,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var s=t(41135),n=t(31009);function o(...e){return(0,n.m6)((0,s.W)(e))}},27113:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/last-wishes/page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,8987,9168,5981,6292,8002],()=>t(67562));module.exports=s})();