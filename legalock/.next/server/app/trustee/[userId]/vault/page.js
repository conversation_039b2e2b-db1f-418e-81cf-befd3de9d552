(()=>{var e={};e.id=3292,e.ids=[3292,2777],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},12325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(12919),r(56752),r(35866);var a=r(23191),n=r(88716),s=r(37922),o=r.n(s),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["trustee",{children:["[userId]",{children:["vault",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12919)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/vault/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/vault/page.tsx"],u="/trustee/[userId]/vault/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/trustee/[userId]/vault/page",pathname:"/trustee/[userId]/vault",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3482:(e,t,r)=>{Promise.resolve().then(r.bind(r,97236))},31540:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},12714:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},36283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9015:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},88307:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},58038:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},97236:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(10326),n=r(17577),s=r(35047),o=r(2777),i=r(68136);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var l=r(91664),c=r(41190);!function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();var d=r(88307),u=r(58038),p=r(36283),m=r(9015),h=r(12714),y=r(31540);!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var f=r(39318),x=r(85999);function g(){let{userId:e}=(0,s.useParams)(),t=(0,s.useRouter)(),{user:r}=(0,i.a)(),[g,v]=(0,n.useState)([]),[w,b]=(0,n.useState)(""),[j,N]=(0,n.useState)("all"),[O,_]=(0,n.useState)(!0),[E,U]=(0,n.useState)(null),[k,M]=(0,n.useState)(!1),[I,C]=(0,n.useState)(""),D=async e=>{try{U(e.id);let{data:t,error:r}=await o.supabase.storage.from("documents").download(e.file_path);if(r)throw console.error("Error downloading file:",r),Error(`Failed to download file: ${r.message}`);if(!t)throw Error("No data returned from storage");let a=(0,f.RG)(e.encryption_key),n=e.file_path.split("/").pop()||"document",s=await (0,f.np)(t,a,n,e.file_type),i=URL.createObjectURL(s);window.open(i,"_blank")}catch(e){console.error("Error viewing document:",e),x.Am.error(`Failed to decrypt document: ${e.message}`)}finally{U(null)}},F=async e=>{try{U(e.id);let{data:t,error:r}=await o.supabase.storage.from("documents").download(e.file_path);if(r)throw r;let a=(0,f.RG)(e.encryption_key),n=(e.file_path.split("/").pop()||"").split("_").slice(1).join("_"),s=e.name,i=n.split(".").pop(),l=`${s}${i?"."+i:""}`,c=await (0,f.np)(t,a,l,e.file_type),d=URL.createObjectURL(c),u=window.document.createElement("a");u.href=d,u.download=l,window.document.body.appendChild(u),u.click(),window.document.body.removeChild(u),setTimeout(()=>{URL.revokeObjectURL(d)},100)}catch(e){console.error("Error downloading document:",e),x.Am.error(`Failed to decrypt document: ${e.message}`)}finally{U(null)}};return k?(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[a.jsx(l.z,{variant:"outline",className:"mb-6",onClick:()=>t.push("/trustee/dashboard"),children:"Back to Dashboard"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:`${I}'s Digital Vault`,description:"Access important documents that have been shared with you as a trustee."}),(0,a.jsxs)("div",{className:"mb-6 flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-1/2",children:[a.jsx(c.I,{type:"text",placeholder:"Search documents...",value:w,onChange:e=>b(e.target.value),className:"pr-10"}),a.jsx(d.Z,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:j,onValueChange:N,children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full md:w-[180px]",children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Filter by category"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[{value:"all",label:"All Documents"},{value:"will",label:"Will"},{value:"trust",label:"Trust"},{value:"financial",label:"Financial"},{value:"medical",label:"Medical"},{value:"insurance",label:"Insurance"},{value:"property",label:"Property"},{value:"digital",label:"Digital"},{value:"other",label:"Other"}].map(e=>a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]})]}),O?(0,a.jsxs)("div",{className:"text-center py-10",children:[a.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Loading documents..."})]}):0===g.length?(0,a.jsxs)("div",{className:"text-center py-16 bg-gray-50 rounded-lg border border-gray-200",children:[a.jsx(u.Z,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No documents found"}),a.jsx("p",{className:"text-gray-500 mb-6 max-w-md mx-auto",children:"There are no documents in this vault that have been shared with you."})]}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden hover:shadow-md transition-shadow",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center text-lg",children:[a.jsx(p.Z,{className:"mr-2 h-5 w-5 text-primary"}),a.jsx("span",{className:"truncate",children:e.name})]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"text-sm text-gray-500 mr-2",children:"Category:"}),a.jsx("span",{className:"text-sm font-medium bg-gray-100 px-2 py-0.5 rounded",children:e.category})]}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mb-4",children:[a.jsx(m.Z,{className:"h-3 w-3 mr-1 text-green-500"}),a.jsx("span",{children:"End-to-end encrypted"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>D(e),disabled:E===e.id,children:E===e.id?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-primary border-t-transparent rounded-full"}),"Decrypting..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"View"]})}),a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>F(e),disabled:E===e.id,children:E===e.id?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-primary border-t-transparent rounded-full"}),"Decrypting..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Download"]})})]})]})]},e.id))})]}):a.jsx("div",{className:"container mx-auto py-8",children:(0,a.jsxs)("div",{className:"text-center py-16",children:[a.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Verifying access..."})]})})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var a=r(10326),n=r(17577),s=r(34214),o=r(79360),i=r(51223);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...o},c)=>{let d=n?s.g7:"button";return a.jsx(d,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:c,...o})});c.displayName="Button"},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>o});var a=r(10326),n=r(17577),s=r(51223);let o=n.forwardRef(({className:e,type:t,...r},n)=>a.jsx("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));o.displayName="Input"},2777:(e,t,r)=>{"use strict";r.d(t,{supabase:()=>i});var a=r(56292);let n="https://ccwvtcudztphwwzzgwvg.supabase.co",s=null,o=null,i=(0,a.eI)(n,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(o)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";o=(0,a.eI)(n,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(41135),n=r(31009);function s(...e){return(0,n.m6)((0,a.W)(e))}},39318:(e,t,r)=>{"use strict";r.d(t,{Po:()=>s,RG:()=>n,np:()=>o,sM:()=>a});let a=e=>{let t=new Uint8Array(e),r="";for(let e=0;e<t.byteLength;e++)r+=String.fromCharCode(t[e]);return btoa(r)},n=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},s=async e=>{try{let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.generateKey({name:"AES-GCM",length:256},!0,["encrypt","decrypt"]),n=await crypto.subtle.exportKey("raw",r);sessionStorage.setItem("lastEncryptionKey",a(n));let s=e.size,o=Math.ceil(s/10485760);if(1===o){let a=await e.arrayBuffer(),n=await crypto.subtle.encrypt({name:"AES-GCM",iv:t},r,a),s=new Uint8Array(1+t.length+n.byteLength);return s[0]=t.length,s.set(t,1),s.set(new Uint8Array(n),1+t.length),new File([s],e.name,{type:"application/encrypted",lastModified:e.lastModified})}{let a=new Uint8Array(1+t.length);a[0]=t.length,a.set(t,1);let n=[a];for(let a=0;a<o;a++){let o=10485760*a,i=Math.min(o+10485760,s),l=await e.slice(o,i).arrayBuffer(),c=await crypto.subtle.encrypt({name:"AES-GCM",iv:t},r,l);n.push(new Uint8Array(c))}return new File(n,e.name,{type:"application/encrypted",lastModified:e.lastModified})}}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt file")}},o=async(e,t,r,a)=>{try{let s="string"==typeof t?n(t):t,o=await crypto.subtle.importKey("raw",s,{name:"AES-GCM",length:256},!1,["decrypt"]),i=await e.slice(0,100).arrayBuffer(),l=new Uint8Array(i),c=l[0];if(c<1||c>16)throw console.error("Invalid IV length:",c),Error("Invalid file format or encryption");let d=l.slice(1,1+c),u=e.size,p=1+c;try{if(u-p<10485760){let t=await e.arrayBuffer(),n=new Uint8Array(t).slice(p),s=await crypto.subtle.decrypt({name:"AES-GCM",iv:d},o,n);return new File([s],r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}{let t=Math.ceil((u-p)/10485760),n=[];for(let r=0;r<t;r++){let t=p+10485760*r,a=Math.min(t+10485760,u),s=await e.slice(t,a).arrayBuffer(),i=await crypto.subtle.decrypt({name:"AES-GCM",iv:d},o,s);n.push(new Uint8Array(i))}return new File(n,r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}}catch(t){console.error("Crypto operation error:",t);try{let t=await e.arrayBuffer(),n=new Uint8Array(12).fill(1),s=await crypto.subtle.decrypt({name:"AES-GCM",iv:n},o,t);return new File([s],r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}catch(e){throw console.error("Fallback decryption failed:",e),Error("Failed to decrypt file: "+e.message)}}}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt file: "+(e.message||"Unknown error"))}}},12919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/[userId]/vault/page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,8987,5981,6292,8002],()=>r(12325));module.exports=a})();