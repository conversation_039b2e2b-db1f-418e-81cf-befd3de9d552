(()=>{var e={};e.id=83,e.ids=[83,2777],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},64449:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>c}),r(89990),r(56752),r(35866);var s=r(23191),a=r(88716),n=r(37922),o=r.n(n),i=r(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let c=["",{children:["trustee",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,89990)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/dashboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/dashboard/page.tsx"],u="/trustee/dashboard/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/trustee/dashboard/page",pathname:"/trustee/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48222:(e,t,r)=>{Promise.resolve().then(r.bind(r,62451))},24230:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},36283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},67427:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},42887:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},85962:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},50949:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},24061:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(79404),a=r.n(s)},62451:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(10326),a=r(17577),n=r(90434),o=r(35047),i=r(68136),d=r(2777);let c=(0,r(62881).Z)("HeartPulse",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}],["path",{d:"M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27",key:"1uw2ng"}]]);var l=r(50949),u=r(67427),m=r(24061),h=r(48705),p=r(24230),f=r(36283),x=r(42887),O=r(85962),N=r(91664),b=r(85999);function j(){(0,o.useRouter)();let{user:e,loading:t}=(0,i.a)(),[r,j]=(0,a.useState)([]),[g,v]=(0,a.useState)(!0),[_,w]=(0,a.useState)(null),[y,D]=(0,a.useState)(!1),U=async()=>{try{v(!0);let{data:t,error:r}=await d.supabase.from("trustees").select("*, user_id").eq("trustee_user_id",e?.id).eq("status","active");if(r)throw r;if(!t||0===t.length){j([]);return}let s=t.map(e=>e.user_id),{data:a,error:n}=await d.supabase.from("profiles").select("id, first_name, last_name, email").in("id",s);if(n)throw n;let o=t.map(e=>{let t=a?.find(t=>t.id===e.user_id);return{id:e.user_id,first_name:t?.first_name||"",last_name:t?.last_name||"",email:t?.email||"",relationship:e.relationship,permissions:e.permissions,status:"alive"}}),{data:i,error:c}=await d.supabase.from("death_notifications").select("user_id, status").in("user_id",s).in("status",["pending","verified"]);if(c)throw c;i&&i.length>0&&i.forEach(e=>{let t=o.findIndex(t=>t.id===e.user_id);t>=0&&(o[t].status="verified"===e.status?"deceased":"pending")}),j(o)}catch(e){console.error("Error fetching grantors:",e),b.Am.error("Failed to load your trustee relationships")}finally{v(!1)}},E=async()=>{try{if(!_)return;D(!0);let{data:t,error:r}=await d.supabase.from("death_notifications").select("id, status").eq("user_id",_.id).single();if(t){if("verified"===t.status){b.Am.error("Death has already been verified");return}let{error:r}=await d.supabase.from("death_notifications").update({reported_by:e?.id,reported_at:new Date().toISOString()}).eq("id",t.id);if(r)throw r}else{let{error:t}=await d.supabase.from("death_notifications").insert({user_id:_.id,reported_by:e?.id,status:"pending",reported_at:new Date().toISOString()});if(t)throw t}b.Am.success("Death reported. Our team will verify this information."),w(null),U()}catch(e){console.error("Error reporting death:",e),b.Am.error(e.message||"Failed to report death")}finally{D(!1)}},M=e=>{switch(e){case"alive":return(0,s.jsxs)("span",{className:"flex items-center text-green-600 text-sm font-medium",children:[s.jsx(c,{className:"h-4 w-4 mr-1"})," Active"]});case"pending":return(0,s.jsxs)("span",{className:"flex items-center text-amber-600 text-sm font-medium",children:[s.jsx(l.Z,{className:"h-4 w-4 mr-1"})," Death Reported (Pending Verification)"]});case"deceased":return(0,s.jsxs)("span",{className:"flex items-center text-gray-600 text-sm font-medium",children:[s.jsx(u.Z,{className:"h-4 w-4 mr-1"})," Deceased"]});default:return s.jsx("span",{children:e})}};return t?(0,s.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-500",children:"Loading trustee dashboard..."})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Trustee Dashboard",description:"Manage your responsibilities as a trustee",icon:s.jsx(m.Z,{className:"h-6 w-6"})}),g?(0,s.jsxs)("div",{className:"text-center py-10",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"Loading your trustee relationships..."})]}):0===r.length?(0,s.jsxs)("div",{className:"text-center py-16 bg-gray-50 rounded-lg border border-gray-200",children:[s.jsx(m.Z,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No trustee relationships found"}),s.jsx("p",{className:"text-gray-500 mb-6 max-w-md mx-auto",children:"You are not currently a trustee for anyone. If someone has invited you to be their trustee, please check your email for an invitation."})]}):s.jsx("div",{className:"space-y-8 mt-8",children:r.map(e=>(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:[e.first_name," ",e.last_name]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[e.relationship," • ",e.email]})]}),M(e.status)]})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["alive"===e.status?s.jsx("div",{className:"bg-blue-50 p-4 rounded-md mb-6",children:(0,s.jsxs)("p",{className:"text-blue-800 text-sm",children:["You are a trustee for ",e.first_name,". You will be able to access their digital legacy after their passing."]})}):"pending"===e.status?s.jsx("div",{className:"bg-amber-50 p-4 rounded-md mb-6",children:(0,s.jsxs)("p",{className:"text-amber-800 text-sm",children:["You have reported ",e.first_name,"'s passing. Our team is verifying this information. You will be notified when verification is complete."]})}):s.jsx("div",{className:"bg-gray-100 p-4 rounded-md mb-6",children:(0,s.jsxs)("p",{className:"text-gray-800 text-sm",children:[e.first_name,"'s passing has been verified. You now have access to their digital legacy as specified below."]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.permissions.includes("assets")&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`border ${"deceased"===e.status?"border-primary":"border-gray-200"}`,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium flex items-center",children:[s.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Assets"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-2",children:s.jsx(N.z,{asChild:!0,variant:"deceased"===e.status?"default":"outline",disabled:"deceased"!==e.status,className:"w-full",children:(0,s.jsxs)(n.default,{href:`/trustee/${e.id}/assets`,children:["View Assets",s.jsx(p.Z,{className:"ml-2 h-4 w-4"})]})})})]}),e.permissions.includes("vault")&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`border ${"deceased"===e.status?"border-primary":"border-gray-200"}`,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium flex items-center",children:[s.jsx(f.Z,{className:"h-4 w-4 mr-2"}),"Digital Vault"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-2",children:s.jsx(N.z,{asChild:!0,variant:"deceased"===e.status?"default":"outline",disabled:"deceased"!==e.status,className:"w-full",children:(0,s.jsxs)(n.default,{href:`/trustee/${e.id}/vault`,children:["View Documents",s.jsx(p.Z,{className:"ml-2 h-4 w-4"})]})})})]}),e.permissions.includes("contacts")&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`border ${"deceased"===e.status?"border-primary":"border-gray-200"}`,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium flex items-center",children:[s.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Contacts"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-2",children:s.jsx(N.z,{asChild:!0,variant:"deceased"===e.status?"default":"outline",disabled:"deceased"!==e.status,className:"w-full",children:(0,s.jsxs)(n.default,{href:`/trustee/${e.id}/contacts`,children:["View Contacts",s.jsx(p.Z,{className:"ml-2 h-4 w-4"})]})})})]}),e.permissions.includes("services")&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`border ${"deceased"===e.status?"border-primary":"border-gray-200"}`,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium flex items-center",children:[s.jsx(O.Z,{className:"h-4 w-4 mr-2"}),"Service Sunset"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-2",children:s.jsx(N.z,{asChild:!0,variant:"deceased"===e.status?"default":"outline",disabled:"deceased"!==e.status,className:"w-full",children:(0,s.jsxs)(n.default,{href:`/trustee/${e.id}/services`,children:["View Services",s.jsx(p.Z,{className:"ml-2 h-4 w-4"})]})})})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`border ${"deceased"===e.status?"border-primary":"border-gray-200"}`,children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium flex items-center",children:[s.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Last Wishes"]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-2",children:s.jsx(N.z,{asChild:!0,variant:"deceased"===e.status?"default":"outline",disabled:"deceased"!==e.status,className:"w-full",children:(0,s.jsxs)(n.default,{href:`/trustee/${e.id}/last-wishes`,children:["View Last Wishes",s.jsx(p.Z,{className:"ml-2 h-4 w-4"})]})})})]})]})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"border-t pt-4",children:"alive"===e.status&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,s.jsxs)(N.z,{variant:"outline",className:"text-red-600 border-red-200",children:[s.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Report Death"]})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Report Death"}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Are you sure you want to report the death of ",e.first_name," ",e.last_name,"? This will initiate the process of granting you access to their digital legacy."]})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cancel"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>{w(e),E()},className:"bg-red-600 hover:bg-red-700",children:y?"Processing...":"Report Death"})]})]})]})})]},e.id))})]})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var s=r(10326),a=r(17577),n=r(34214),o=r(79360),i=r(51223);let d=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},c)=>{let l=a?n.g7:"button";return s.jsx(l,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:c,...o})});c.displayName="Button"},2777:(e,t,r)=>{"use strict";r.d(t,{supabase:()=>i});var s=r(56292);let a="https://ccwvtcudztphwwzzgwvg.supabase.co",n=null,o=null,i=(0,s.eI)(a,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(o)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";o=(0,s.eI)(a,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(41135),a=r(31009);function n(...e){return(0,a.m6)((0,s.W)(e))}},89990:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/trustee/dashboard/page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,8987,9168,5981,6292,8002],()=>r(64449));module.exports=s})();