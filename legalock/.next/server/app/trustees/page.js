(()=>{var e={};e.id=4456,e.ids=[4456],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},98020:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),t(20885),t(56752),t(35866);var s=t(23191),n=t(88716),a=t(37922),o=t.n(a),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["trustees",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20885)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/trustees/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/trustees/page.tsx"],u="/trustees/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/trustees/page",pathname:"/trustees",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},65103:(e,r,t)=>{Promise.resolve().then(t.bind(t,13637))},86333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},69669:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48998:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5932:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},44389:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},21405:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},98091:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74975:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},24061:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},13637:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>D});var s=t(10326),n=t(17577),a=t(35047),o=t(68136),i=t(48998),l=t(30361),d=t(69669),c=t(24061),u=t(74975),m=t(5932),h=t(44389),p=t(21405);let x=(0,t(62881).Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var f=t(98091),g=t(91664),j=t(85999),v=t(88270),N=t(74723),b=t(74064),O=t(27256),y=t(41190),_=t(9280),w=t(9969);!function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}();let E=O.Ry({firstName:O.Z_().min(2,{message:"First name must be at least 2 characters"}),lastName:O.Z_().min(2,{message:"Last name must be at least 2 characters"}),email:O.Z_().email({message:"Please enter a valid email address"}),permissions:O.IX(O.Z_()).min(1,{message:"Please select at least one permission"}),message:O.Z_().optional()});function U({onSuccess:e,onCancel:r,isEditing:t=!1,defaultValues:i,onUpdate:l}){let d=(0,a.useRouter)(),{user:c}=(0,o.a)(),[u,m]=(0,n.useState)(!1),h=[{id:"assets",label:"Assets",description:"Digital and physical assets inventory"},{id:"vault",label:"Digital Vault",description:"Important documents and files"},{id:"contacts",label:"Emergency Contacts",description:"People to notify after your passing"},{id:"services",label:"Service Sunset",description:"Services to cancel after your passing"}],p=(0,N.cI)({resolver:(0,b.F)(E),defaultValues:i||{firstName:"",lastName:"",email:"",permissions:["assets","vault","contacts","services"],message:""}}),x=async r=>{try{if(!c){j.Am.error("You must be logged in to manage trustees");return}if(m(!0),t&&l){await l(r),m(!1);return}let s=await fetch("/api/trustees",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r.email,first_name:r.firstName.charAt(0).toUpperCase()+r.firstName.slice(1),last_name:r.lastName.charAt(0).toUpperCase()+r.lastName.slice(1),relationship:"",permissions:r.permissions})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to add trustee")}await s.json(),j.Am.success("Trustee added successfully"),e&&e(),p.reset({firstName:"",lastName:"",email:"",permissions:["assets","vault","contacts","services"],message:""})}catch(e){console.error("Error managing trustee:",e),e.message.includes("duplicate")?j.Am.error("This person is already added as your trustee"):j.Am.error(e.message||"Error managing trustee")}finally{m(!1)}};return s.jsx(w.l0,{...p,children:(0,s.jsxs)("form",{onSubmit:p.handleSubmit(x),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(w.Wi,{control:p.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"First Name*"}),s.jsx(w.NI,{children:s.jsx(y.I,{placeholder:"First name",...e})}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:p.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Last Name*"}),s.jsx(w.NI,{children:s.jsx(y.I,{placeholder:"Last name",...e})}),s.jsx(w.zG,{})]})})]}),s.jsx(w.Wi,{control:p.control,name:"email",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Email Address*"}),s.jsx(w.NI,{children:s.jsx(y.I,{type:"email",placeholder:"<EMAIL>",...e,disabled:t})}),s.jsx(w.pf,{children:t?"Email cannot be changed. To change the email, delete this trustee and add a new one.":"Your trustee will receive an invitation at this email address"}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:p.control,name:"permissions",render:()=>(0,s.jsxs)(w.xJ,{children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx(w.lX,{children:"Permissions*"}),s.jsx(w.pf,{children:"Select what information this trustee will have access to after your passing"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h.map(e=>s.jsx(w.Wi,{control:p.control,name:"permissions",render:({field:r})=>(0,s.jsxs)(w.xJ,{className:"flex flex-row items-start space-x-3 space-y-0",children:[s.jsx(w.NI,{children:s.jsx(_.X,{checked:r.value?.includes(e.id),onCheckedChange:t=>{let s=t?[...r.value,e.id]:r.value.filter(r=>r!==e.id);r.onChange(s)}})}),(0,s.jsxs)("div",{className:"space-y-1 leading-none",children:[s.jsx(w.lX,{children:e.label}),s.jsx(w.pf,{children:e.description})]})]})},e.id))}),s.jsx(w.zG,{})]})}),s.jsx(w.Wi,{control:p.control,name:"message",render:({field:e})=>(0,s.jsxs)(w.xJ,{children:[s.jsx(w.lX,{children:"Personal Message (Optional)"}),s.jsx(w.NI,{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Add a personal message to your invitation",className:"resize-none h-24",...e})}),s.jsx(w.pf,{children:"This message will be included in the invitation email"}),s.jsx(w.zG,{})]})}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[r?s.jsx(g.z,{type:"button",variant:"outline",onClick:r,children:"Cancel"}):s.jsx(g.z,{type:"button",variant:"outline",onClick:()=>d.push("/trustees"),children:"Cancel"}),s.jsx(g.z,{type:"submit",disabled:u,children:u?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"}),t?"Updating Trustee...":"Adding Trustee..."]}):t?"Update Trustee":"Add Trustee"})]})]})})}function D(){(0,a.useRouter)();let{user:e,loading:r}=(0,o.a)(),[t,N]=(0,n.useState)([]),[b,O]=(0,n.useState)(!0),[y,_]=(0,n.useState)(null),[w,E]=(0,n.useState)(null),[D,k]=(0,n.useState)(null),C=async()=>{try{O(!0);let e=await fetch("/api/trustees");if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to load trustees")}let r=await e.json();N(r||[])}catch(e){console.error("Error fetching trustees:",e),j.Am.error(e.message||"Failed to load trustees")}finally{O(!1)}},T=async r=>{try{_(r.id);let t=await fetch("/api/send-trustee-invitation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({inviterName:e?.firstName&&e?.lastName?`${e.firstName} ${e.lastName}`:e?.email,inviterEmail:e?.email,trusteeName:`${r.first_name} ${r.last_name}`,trusteeEmail:r.trustee_email,permissions:r.permissions,inviteId:r.id})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to send invitation")}let s=null===r.invitation_sent_at;j.Am.success(s?`Invitation sent to ${r.trustee_email}`:`Invitation resent to ${r.trustee_email}`),C()}catch(e){console.error("Error sending invitation:",e),j.Am.error(e.message||"Failed to send invitation")}finally{_(null)}},M=async e=>{try{E(e);let r=await fetch("/api/trustees",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,status:"revoked"})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to revoke trustee")}j.Am.success("Trustee access revoked"),C()}catch(e){console.error("Error revoking trustee:",e),j.Am.error(e.message||"Failed to revoke trustee")}finally{E(null)}},F=e=>{k(e)},L=async e=>{try{let r=await fetch("/api/trustees",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:D?.id,first_name:e.firstName.charAt(0).toUpperCase()+e.firstName.slice(1),last_name:e.lastName.charAt(0).toUpperCase()+e.lastName.slice(1),permissions:e.permissions})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to update trustee")}j.Am.success("Trustee updated successfully"),k(null),C()}catch(e){console.error("Error updating trustee:",e),j.Am.error(e.message||"Failed to update trustee")}},Z=async e=>{try{let r=await fetch(`/api/trustees?id=${e}`,{method:"DELETE"});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to delete trustee")}j.Am.success("Trustee deleted"),C()}catch(e){console.error("Error deleting trustee:",e),j.Am.error(e.message||"Failed to delete trustee")}},A=e=>{switch(e){case"pending":return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-yellow-50 text-yellow-700 border-yellow-200",children:[s.jsx(i.Z,{className:"h-3 w-3 mr-1"})," Pending"]});case"active":return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:[s.jsx(l.Z,{className:"h-3 w-3 mr-1"})," Active"]});case"revoked":return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-red-50 text-red-700 border-red-200",children:[s.jsx(d.Z,{className:"h-3 w-3 mr-1"})," Revoked"]});default:return s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e})}};return r?(0,s.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),s.jsx("p",{className:"mt-4 text-gray-500",children:"Loading trustees..."})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[s.jsx(v.Z,{}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Trustees",description:"Manage the people you trust to handle your digital legacy",icon:s.jsx(c.Z,{className:"h-6 w-6"}),actions:null}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"feature-card",children:[(0,s.jsxs)("div",{className:"flex items-center mb-5",children:[s.jsx("div",{className:"feature-card-icon bg-purple-100",children:s.jsx(u.Z,{className:"h-5 w-5 text-purple-600"})}),s.jsx("h3",{className:"feature-card-title",children:"Add New Trustee"})]}),s.jsx(U,{onSuccess:()=>{C()},onCancel:()=>{}})]}),s.jsx("div",{className:"lg:col-span-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"feature-card",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"feature-card-icon bg-purple-100 mr-3",children:s.jsx(c.Z,{className:"h-5 w-5 text-purple-600"})}),(0,s.jsxs)("div",{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:"Your Trustees"}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.length," ",1===t.length?"trustee":"trustees"," added"]})]})]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:b?s.jsx("div",{className:"flex justify-center py-8",children:s.jsx("div",{className:"animate-spin h-8 w-8 border-4 border-purple-500 border-t-transparent rounded-full"})}):0===t.length?(0,s.jsxs)("div",{className:"feature-card-empty bg-purple-50",children:[s.jsx(c.Z,{className:"feature-card-empty-icon text-purple-500"}),s.jsx("h4",{className:"feature-card-empty-title text-purple-900",children:"No Trustees Yet"}),s.jsx("p",{className:"feature-card-empty-description text-purple-700",children:"Add people you trust to manage your digital legacy after your passing. They will be able to access your assets, documents, and other information."})]}):s.jsx("div",{className:"grid grid-cols-1 gap-4",children:t.map(e=>{let r=`${e.first_name.charAt(0)}${e.last_name.charAt(0)}`.toUpperCase(),t=null!==e.invitation_sent_at;return(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden hover:shadow-md transition-shadow border border-gray-100",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center text-lg",children:[s.jsx("div",{className:"w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 text-sm font-medium text-purple-700",children:r}),(0,s.jsxs)("span",{className:"truncate",children:[e.first_name," ",e.last_name]}),s.jsx("div",{className:"ml-auto",children:A(e.status)})]})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[s.jsx(m.Z,{className:"h-4 w-4 mr-2 text-gray-500 flex-shrink-0"}),s.jsx("span",{className:"text-gray-700 truncate",children:e.trustee_email})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-500 mb-1",children:"Permissions:"}),s.jsx("div",{className:"flex flex-wrap gap-1",children:e.permissions.map(e=>s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",className:"text-xs",children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 items-center pt-2 border-t",children:[(0,s.jsxs)(g.z,{variant:"outline",size:"sm",className:"text-xs",onClick:()=>F(e),children:[s.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"Edit"]}),"pending"===e.status&&s.jsx(g.z,{variant:"outline",size:"sm",className:"text-xs",onClick:()=>T(e),disabled:y===e.id,children:y===e.id?(0,s.jsxs)(s.Fragment,{children:[s.jsx(p.Z,{className:"h-3 w-3 mr-1 animate-spin"}),"Sending..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(x,{className:"h-3 w-3 mr-1"}),t?"Resend Invitation":"Send Invitation"]})}),"active"===e.status&&(0,s.jsxs)(g.z,{variant:"outline",size:"sm",className:"text-xs text-red-600 border-red-200 hover:bg-red-50",onClick:()=>E(e.id),disabled:w===e.id,children:[s.jsx(d.Z,{className:"h-3 w-3 mr-1"}),"Revoke Access"]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,s.jsxs)(g.z,{variant:"outline",size:"sm",className:"text-xs text-red-600 border-red-200 hover:bg-red-50",children:[s.jsx(f.Z,{className:"h-3 w-3 mr-1"}),"Delete"]})}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Delete Trustee"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Are you sure you want to delete this trustee? This action cannot be undone."})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cancel"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>Z(e.id),className:"bg-red-600 hover:bg-red-700",children:"Delete"})]})]})]})]})]})]},e.id)})})})]})})]}),w&&s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{open:!!w,onOpenChange:()=>E(null),children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Revoke Trustee Access"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Are you sure you want to revoke this trustee's access? They will no longer be able to access your digital legacy."})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cancel"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>M(w),className:"bg-red-600 hover:bg-red-700",children:"Revoke Access"})]})]})}),D&&s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{open:!!D,onOpenChange:e=>!e&&k(null),children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Edit Trustee"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Update the information for this trustee."})]}),s.jsx(U,{isEditing:!0,defaultValues:{firstName:D.first_name,lastName:D.last_name,email:D.trustee_email,permissions:D.permissions,message:""},onSuccess:()=>{k(null),C()},onCancel:()=>k(null),onUpdate:L})]})})]})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()},88270:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(10326);t(17577);var n=t(90434),a=t(86333);let o=()=>s.jsx("div",{className:"mb-6",children:(0,s.jsxs)(n.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[s.jsx(a.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},91664:(e,r,t)=>{"use strict";t.d(r,{z:()=>d});var s=t(10326),n=t(17577),a=t(34214),o=t(79360),i=t(51223);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...o},d)=>{let c=n?a.g7:"button";return s.jsx(c,{className:(0,i.cn)(l({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"},9280:(e,r,t)=>{"use strict";t.d(r,{X:()=>l});var s=t(10326),n=t(17577),a=t(46074),o=t(32933),i=t(51223);let l=n.forwardRef(({className:e,...r},t)=>s.jsx(a.fC,{ref:t,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...r,children:s.jsx(a.z$,{className:(0,i.cn)("flex items-center justify-center text-current"),children:s.jsx(o.Z,{className:"h-4 w-4"})})}));l.displayName=a.fC.displayName},9969:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},41190:(e,r,t)=>{"use strict";t.d(r,{I:()=>o});var s=t(10326),n=t(17577),a=t(51223);let o=n.forwardRef(({className:e,type:r,...t},n)=>s.jsx("input",{type:r,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t}));o.displayName="Input"},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(41135),n=t(31009);function a(...e){return(0,n.m6)((0,s.W)(e))}},20885:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/trustees/page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,8987,9168,5981,6686,4272,8002],()=>t(98020));module.exports=s})();