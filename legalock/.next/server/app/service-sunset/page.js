(()=>{var e={};e.id=4197,e.ids=[4197],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},58603:(e,r,n)=>{"use strict";n.r(r),n.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>l}),n(74212),n(56752),n(35866);var t=n(23191),o=n(88716),s=n(37922),a=n.n(s),i=n(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);n.d(r,c);let l=["",{children:["service-sunset",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,74212)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/service-sunset/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/service-sunset/page.tsx"],u="/service-sunset/page",m={require:n,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/service-sunset/page",pathname:"/service-sunset",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74819:(e,r,n)=>{Promise.resolve().then(n.bind(n,90631))},86333:(e,r,n)=>{"use strict";n.d(r,{Z:()=>t});let t=(0,n(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},30361:(e,r,n)=>{"use strict";n.d(r,{Z:()=>t});let t=(0,n(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},13961:(e,r,n)=>{"use strict";n.d(r,{Z:()=>t});let t=(0,n(62881).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},85962:(e,r,n)=>{"use strict";n.d(r,{Z:()=>t});let t=(0,n(62881).Z)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},90434:(e,r,n)=>{"use strict";n.d(r,{default:()=>o.a});var t=n(79404),o=n.n(t)},90631:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>N});var t=n(10326),o=n(17577),s=n(68136),a=n(78388),i=n(85962),c=n(13961),l=n(30361),d=n(88270),u=n(74723),m=n(74064),h=n(27256),x=n(91664),f=n(41190),p=n(9969);(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();let v=h.Ry({name:h.Z_().min(2,{message:"Service name must be at least 2 characters"}),category:h.Z_().min(1,{message:"Please select a category"}),description:h.Z_().optional(),account_number:h.Z_().optional(),cancellation_instructions:h.Z_().optional()}),b=[{value:"subscription",label:"Subscription"},{value:"utility",label:"Utility"},{value:"financial",label:"Financial"},{value:"entertainment",label:"Entertainment"},{value:"other",label:"Other"}];function g({onSubmit:e,defaultValues:r}){let n=(0,u.cI)({resolver:(0,m.F)(v),defaultValues:{name:r?.name||"",category:r?.category||"",description:r?.description||"",account_number:r?.account_number||"",cancellation_instructions:r?.cancellation_instructions||""}});return t.jsx(p.l0,{...n,children:(0,t.jsxs)("form",{onSubmit:n.handleSubmit(r=>{e(r)}),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsx(p.Wi,{control:n.control,name:"name",render:({field:e})=>(0,t.jsxs)(p.xJ,{children:[t.jsx(p.lX,{children:"Service Name*"}),t.jsx(p.NI,{children:t.jsx(f.I,{placeholder:"Enter service name",...e})}),t.jsx(p.zG,{})]})}),t.jsx(p.Wi,{control:n.control,name:"category",render:({field:e})=>(0,t.jsxs)(p.xJ,{children:[t.jsx(p.lX,{children:"Category*"}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{onValueChange:e.onChange,defaultValue:e.value,children:[t.jsx(p.NI,{children:t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select a category"})})}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:b.map(e=>t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]}),t.jsx(p.zG,{})]})}),t.jsx(p.Wi,{control:n.control,name:"account_number",render:({field:e})=>(0,t.jsxs)(p.xJ,{children:[t.jsx(p.lX,{children:"Account Number"}),t.jsx(p.NI,{children:t.jsx(f.I,{placeholder:"Account number or ID",...e})}),t.jsx(p.zG,{})]})}),t.jsx(p.Wi,{control:n.control,name:"cancellation_instructions",render:({field:e})=>(0,t.jsxs)(p.xJ,{className:"md:col-span-2",children:[t.jsx(p.lX,{children:"Cancellation Instructions"}),t.jsx(p.NI,{children:t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Detailed instructions for cancellation",className:"resize-none h-20",...e})}),t.jsx(p.zG,{})]})})]}),t.jsx("div",{className:"flex justify-end space-x-2 pt-4",children:t.jsx(x.z,{type:"submit",className:"w-full",children:"Save Service"})})]})})}function j({services:e,onEdit:r,onDelete:n}){return t.jsx("div",{className:"rounded-md border",children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Name"}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Category"}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Actions"})]})}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[e.map(e=>(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"font-medium",children:e.name}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e.category}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex gap-2",children:[t.jsx(x.z,{variant:"outline",size:"sm",onClick:()=>r(e),children:"Edit"}),t.jsx(x.z,{variant:"destructive",size:"sm",onClick:()=>n(e.id),children:"Delete"})]})]},e.id)),0===e.length&&t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{colSpan:3,className:"text-center py-8 text-muted-foreground",children:"No services added yet"})})]})]})})}!function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}();var O=n(85999);function N(){let{user:e}=(0,s.a)(),[r,n]=(0,o.useState)([]),[u,m]=(0,o.useState)("all"),[h,x]=(0,o.useState)(!0),[f,p]=(0,o.useState)(null),[v,b]=(0,o.useState)(!1),N=e=>e.charAt(0).toUpperCase()+e.slice(1),_=async t=>{try{if(!e){O.Am.error("You must be logged in to add services");return}let o=N(t.name),s=await fetch("/api/service-sunset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...t,name:o})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to add service")}let a=await s.json();O.Am.success("Service added successfully"),n([...r,a])}catch(e){console.error("Error adding service:",e),O.Am.error(`Failed to add service: ${e.message}`)}},E=async e=>{try{if(!f)return;let t=N(e.name),o=await fetch("/api/service-sunset",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:f.id,...e,name:t})});if(!o.ok){let e=await o.json();throw Error(e.error||"Failed to update service")}let s=await o.json();O.Am.success("Service updated successfully"),n(r.map(e=>e.id===f.id?s:e)),b(!1),p(null)}catch(e){console.error("Error updating service:",e),O.Am.error(`Failed to update service: ${e.message}`)}},w=async e=>{try{let t=await fetch(`/api/service-sunset?id=${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete service")}O.Am.success("Service deleted successfully"),n(r.filter(r=>r.id!==e))}catch(e){console.error("Error deleting service:",e),O.Am.error(`Failed to delete service: ${e.message}`)}};return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[t.jsx(d.Z,{}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Service Sunset",description:"Manage services that should be canceled after your passing.",icon:t.jsx(i.Z,{className:"h-6 w-6"}),actions:null}),t.jsx("div",{className:"mb-6",children:t.jsx("div",{className:"flex flex-col md:flex-row gap-4",children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:u,onValueChange:m,children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full md:w-[180px]",children:t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"All Categories"})}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",children:"All Categories"}),a.ND.map(e=>t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))]})]})})}),h?(0,t.jsxs)("div",{className:"text-center py-10",children:[t.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Loading services..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"feature-card",children:[(0,t.jsxs)("div",{className:"flex items-center mb-5",children:[t.jsx("div",{className:"feature-card-icon bg-orange-100",children:t.jsx(c.Z,{className:"h-5 w-5 text-orange-600"})}),t.jsx("h3",{className:"feature-card-title",children:"Add New Service"})]}),t.jsx(g,{onSubmit:_})]}),t.jsx("div",{className:"md:col-span-2",children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"feature-card",children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-orange-100 mr-3",children:t.jsx(i.Z,{className:"h-5 w-5 text-orange-600"})}),(0,t.jsxs)("div",{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:"Your Services"}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.length," ",1===r.length?"service":"services"," added"]})]})]})}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:0===r.length?(0,t.jsxs)("div",{className:"feature-card-empty bg-orange-50",children:[t.jsx(i.Z,{className:"feature-card-empty-icon text-orange-500"}),t.jsx("h4",{className:"feature-card-empty-title text-orange-900",children:"No Services Yet"}),t.jsx("p",{className:"feature-card-empty-description text-orange-700",children:"Add a service using the form on the left. These will be shared with your trustees after your passing."})]}):t.jsx(j,{services:r,onEdit:e=>{p(e),b(!0)},onDelete:w})})]})}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{open:v,onOpenChange:e=>{b(e),e||p(null)},children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"sm:max-w-[800px]",children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Edit Service"}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Update information for ",f?.name,"."]})]}),f&&t.jsx(g,{onSubmit:E,defaultValues:{name:f.name,category:f.category,description:f.description||"",account_number:f.account_number||"",cancellation_instructions:f.cancellation_instructions||""}})]})})]}),(0,t.jsxs)("div",{className:"mt-12 bg-orange-50 rounded-lg p-6 border border-orange-100",children:[t.jsx("h3",{className:"text-lg font-semibold mb-4 text-orange-800",children:"How Service Sunset Works"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-2 text-orange-700",children:"For You"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-orange-700",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[t.jsx(l.Z,{className:"h-4 w-4 text-orange-500 mr-2 mt-0.5"}),t.jsx("span",{children:"Add services that should be canceled after your passing"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[t.jsx(l.Z,{className:"h-4 w-4 text-orange-500 mr-2 mt-0.5"}),t.jsx("span",{children:"Provide detailed cancellation instructions for each service"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[t.jsx(l.Z,{className:"h-4 w-4 text-orange-500 mr-2 mt-0.5"}),t.jsx("span",{children:"Prevent unwanted charges after your passing"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-2 text-orange-700",children:"For Your Trustees"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-orange-700",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[t.jsx(l.Z,{className:"h-4 w-4 text-orange-500 mr-2 mt-0.5"}),t.jsx("span",{children:"Receive a complete list of services to cancel"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[t.jsx(l.Z,{className:"h-4 w-4 text-orange-500 mr-2 mt-0.5"}),t.jsx("span",{children:"Follow your detailed instructions for each service"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[t.jsx(l.Z,{className:"h-4 w-4 text-orange-500 mr-2 mt-0.5"}),t.jsx("span",{children:"Track progress as services are canceled"})]})]})]})]})]})]})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()},88270:(e,r,n)=>{"use strict";n.d(r,{Z:()=>a});var t=n(10326);n(17577);var o=n(90434),s=n(86333);let a=()=>t.jsx("div",{className:"mb-6",children:(0,t.jsxs)(o.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[t.jsx(s.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},91664:(e,r,n)=>{"use strict";n.d(r,{z:()=>l});var t=n(10326),o=n(17577),s=n(34214),a=n(79360),i=n(51223);let c=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef(({className:e,variant:r,size:n,asChild:o=!1,...a},l)=>{let d=o?s.g7:"button";return t.jsx(d,{className:(0,i.cn)(c({variant:r,size:n,className:e})),ref:l,...a})});l.displayName="Button"},9969:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},41190:(e,r,n)=>{"use strict";n.d(r,{I:()=>a});var t=n(10326),o=n(17577),s=n(51223);let a=o.forwardRef(({className:e,type:r,...n},o)=>t.jsx("input",{type:r,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...n}));a.displayName="Input"},51223:(e,r,n)=>{"use strict";n.d(r,{cn:()=>s});var t=n(41135),o=n(31009);function s(...e){return(0,o.m6)((0,t.W)(e))}},78388:(e,r,n)=>{"use strict";n.d(r,{BG:()=>s,ND:()=>t,ww:()=>o});let t=[{value:"subscription",label:"Subscription Service"},{value:"utility",label:"Utility"},{value:"financial",label:"Financial Service"},{value:"entertainment",label:"Entertainment"},{value:"other",label:"Other"}],o=[{value:"online",label:"Online"},{value:"phone",label:"Phone"},{value:"email",label:"Email"},{value:"mail",label:"Mail"},{value:"in_person",label:"In Person"},{value:"automatic",label:"Automatic"}],s=[{value:"high",label:"High",description:"Services that should be canceled immediately (e.g., financial services with recurring charges)"},{value:"medium",label:"Medium",description:"Services that should be canceled within a month (e.g., utilities, subscriptions)"},{value:"low",label:"Low",description:"Services that can be canceled eventually (e.g., free accounts, email lists)"}]},74212:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>t});let t=(0,n(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/service-sunset/page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),t=r.X(0,[9276,8987,9168,5981,6686,8002],()=>n(58603));module.exports=t})();