(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},75553:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>c.a,__next_app__:()=>o,originalPathname:()=>m,pages:()=>u,routeModule:()=>x,tree:()=>n}),a(38256),a(56752),a(35866);var t=a(23191),r=a(88716),l=a(37922),c=a.n(l),i=a(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let n=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,38256)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],u=["/Users/<USER>/Desktop/Legalock/legalock/src/app/dashboard/page.tsx"],m="/dashboard/page",o={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},95567:(e,s,a)=>{Promise.resolve().then(a.bind(a,2627))},13961:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},48998:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},36283:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},67427:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},48705:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},42887:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},85962:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});let t=(0,a(62881).Z)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},2627:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var t=a(10326);a(17577);var r=a(48705),l=a(13961),c=a(90434);let i=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(r.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Assets"}),t.jsx("p",{className:"feature-card-subtitle",children:"Track your valuable assets"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(r.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Manage Your Assets"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Track your physical and digital assets for your legacy plan."}),(0,t.jsxs)(c.default,{href:"/assets",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Manage Assets"]})]})]});var d=a(36283);let n=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(d.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Digital Vault"}),t.jsx("p",{className:"feature-card-subtitle",children:"Store important documents"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(d.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Document Storage"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Securely store important documents in your digital vault."}),(0,t.jsxs)(c.default,{href:"/vault",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Upload Document"]})]})]});var u=a(24061);let m=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(u.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Trustees"}),t.jsx("p",{className:"feature-card-subtitle",children:"Manage your trusted contacts"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(u.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Manage Trustees"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Designate trusted individuals to handle your digital legacy."}),(0,t.jsxs)(c.default,{href:"/trustees",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Manage Trustees"]})]})]});var o=a(42887);let x=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(o.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Contacts"}),t.jsx("p",{className:"feature-card-subtitle",children:"Important contact information"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(o.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Manage Contacts"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Store important contact information for your trustees."}),(0,t.jsxs)(c.default,{href:"/contacts",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Manage Contacts"]})]})]});var h=a(85962);let p=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(h.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Service Sunset"}),t.jsx("p",{className:"feature-card-subtitle",children:"Manage online services"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(h.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Manage Services"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Track online services that should be closed after your passing."}),(0,t.jsxs)(c.default,{href:"/service-sunset",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Manage Services"]})]})]});var b=a(67427);let j=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(b.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Last Wishes"}),t.jsx("p",{className:"feature-card-subtitle",children:"Your final wishes and instructions"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(b.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Document Your Wishes"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Record your final wishes to share with your trustees."}),(0,t.jsxs)(c.default,{href:"/last-wishes",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Manage Last Wishes"]})]})]});var f=a(48998);let v=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(f.Z,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Time Capsule"}),t.jsx("p",{className:"feature-card-subtitle",children:"Future messages for loved ones"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(f.Z,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Create Time Capsules"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Create messages to be delivered to your loved ones in the future."}),(0,t.jsxs)(c.default,{href:"/time-capsule",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:[t.jsx(f.Z,{className:"mr-2 h-4 w-4"}),"Manage Time Capsules"]})]})]}),N=(0,a(62881).Z)("FilePenLine",[["path",{d:"m18 5-2.414-2.414A2 2 0 0 0 14.172 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2",key:"142zxg"}],["path",{d:"M21.378 12.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"2t3380"}],["path",{d:"M8 18h1",key:"13wk12"}]]),y=()=>(0,t.jsxs)("div",{children:[t.jsx("div",{className:"flex items-center justify-between mb-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"feature-card-icon bg-blue-100",children:t.jsx(N,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"feature-card-title",children:"Will Advisor"}),t.jsx("p",{className:"feature-card-subtitle",children:"Not started"})]})]})}),(0,t.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[t.jsx(N,{className:"feature-card-empty-icon text-blue-500"}),t.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"Will Planning Advice"}),t.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Get expert guidance and recommendations for creating your legal will."}),t.jsx(c.default,{href:"/will-advisor",className:"feature-card-empty-button bg-blue-600 hover:bg-blue-700",children:"Get Advice"})]})]});var g=a(35797);function k(){return t.jsx(g.Z,{children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Dashboard",description:"Manage your digital legacy in one secure place."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[t.jsx("div",{className:"feature-card",children:t.jsx(i,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(n,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(m,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(x,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(p,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(j,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(v,{})}),t.jsx("div",{className:"feature-card",children:t.jsx(y,{})})]})]})})}!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()},38256:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/dashboard/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[9276,8987,9168,5981,6292,285,8002,5797],()=>a(75553));module.exports=t})();