(()=>{var e={};e.id=3569,e.ids=[3569],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94910:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(84859),r(56752),r(35866);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["help",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84859)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/help/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/help/page.tsx"],u="/help/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/help/page",pathname:"/help",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57233:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79404,23)),Promise.resolve().then(r.bind(r,71986))},62881:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var s=r(17577);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:o="",children:l,iconNode:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",o),...c},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},l)=>(0,s.createElement)(o,{ref:l,iconNode:t,className:n(`lucide-${a(e)}`,r),...i}));return r.displayName=`${e}`,r}},86333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(79404),a=r.n(s)},71986:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(10326);r(17577);var a=r(90434),n=r(86333);function i({title:e,children:t}){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("header",{className:"bg-white border-b border-gray-200 py-6",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex items-center",children:(0,s.jsxs)(a.default,{href:"/",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm",children:[s.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]})})})}),s.jsx("div",{className:"py-12",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:e}),t]})})}),s.jsx("footer",{className:"bg-white border-t border-gray-200 py-8",children:s.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"text-center text-gray-500 text-sm",children:(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," Legalock. All rights reserved."]})})})})]})}},1733:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(27162).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},83113:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(27162).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11242:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(27162).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},84859:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,metadata:()=>g});var s=r(19510);r(71159);var a=r(57371),n=r(27162);let i=(0,n.Z)("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]),o=(0,n.Z)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var l=r(83113),d=r(11242);let c=(0,n.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var u=r(1733),m=r(82355),h=r(27039),p=r(49922);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();let g={title:"Help Center | Legalock",description:"Find answers to common questions about using Legalock for your digital legacy planning."},x=[{id:1,title:"Getting Started",description:"Learn the basics of setting up your Legalock account and navigating the platform.",icon:s.jsx(i,{className:"h-6 w-6"}),slug:"getting-started"},{id:2,title:"Account & Security",description:"Understand how to manage your account settings and keep your information secure.",icon:s.jsx(o,{className:"h-6 w-6"}),slug:"account-security"},{id:3,title:"Digital Assets",description:"Learn how to document and manage your digital assets effectively.",icon:s.jsx(l.Z,{className:"h-6 w-6"}),slug:"digital-assets"},{id:4,title:"Trustees & Contacts",description:"Understand how to designate trustees and manage emergency contacts.",icon:s.jsx(d.Z,{className:"h-6 w-6"}),slug:"trustees-contacts"}],f=[{id:1,question:"How do I add a trustee to my account?",slug:"add-trustee"},{id:2,question:"What happens to my data after I pass away?",slug:"data-after-passing"},{id:3,question:"How secure is the Digital Vault?",slug:"vault-security"},{id:4,question:"Can I change my subscription plan?",slug:"change-subscription"},{id:5,question:"How do trustees verify my passing?",slug:"trustee-verification"}];function y(){return(0,s.jsxs)(m.Z,{title:"Help Center",children:[(0,s.jsxs)("div",{className:"mb-12",children:[s.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Find answers to common questions about using Legalock for your digital legacy planning."}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(c,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),s.jsx(p.I,{type:"text",placeholder:"Search for answers...",className:"pl-10 py-6 text-lg"})]})]}),(0,s.jsxs)("div",{className:"mb-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Browse by Category"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:x.map(e=>(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hover:shadow-md transition-shadow",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:e.icon}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e.title})]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-base",children:e.description})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:s.jsx(h.z,{variant:"ghost",className:"text-primary",asChild:!0,children:(0,s.jsxs)(a.default,{href:`/help/category/${e.slug}`,children:["View articles",s.jsx(u.Z,{className:"ml-2 h-4 w-4"})]})})})]},e.id))})]}),(0,s.jsxs)("div",{className:"mb-12",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Popular Questions"}),s.jsx("div",{className:"space-y-4",children:f.map(e=>s.jsx("div",{className:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors",children:(0,s.jsxs)(a.default,{href:`/help/question/${e.slug}`,className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(o,{className:"h-5 w-5 text-primary mr-3"}),s.jsx("span",{className:"text-gray-800",children:e.question})]}),s.jsx(u.Z,{className:"h-4 w-4 text-gray-400"})]})},e.id))})]}),(0,s.jsxs)("div",{className:"bg-gray-100 rounded-lg p-8 text-center",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Still Need Help?"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Can't find what you're looking for? Our support team is here to help."}),s.jsx(h.z,{asChild:!0,children:s.jsx(a.default,{href:"/contact",children:"Contact Support"})})]})]})}},82355:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/components/Layout/SimplePageLayout.tsx#default`)},27039:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(19510),a=r(71159),n=r(43025),i=r(46145),o=r(40644);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},d)=>{let c=a?n.g7:"button";return s.jsx(c,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:d,...i})});d.displayName="Button"},49922:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var s=r(19510),a=r(71159),n=r(40644);let i=a.forwardRef(({className:e,type:t,...r},a)=>s.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));i.displayName="Input"},40644:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(55761),a=r(62386);function n(...e){return(0,a.m6)((0,s.W)(e))}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,8987,9168,5,8002],()=>r(94910));module.exports=s})();