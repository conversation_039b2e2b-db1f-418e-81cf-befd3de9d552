(()=>{var e={};e.id=2626,e.ids=[2626],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},60779:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c}),t(80974),t(56752),t(35866);var r=t(23191),a=t(88716),l=t(37922),i=t.n(l),n=t(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80974)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/login/page.tsx"],x="/login/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},36951:(e,s,t)=>{Promise.resolve().then(t.bind(t,73396))},91216:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},77506:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},73396:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(10326),a=t(17577),l=t(90434),i=t(35047),n=t(74723),o=t(74064),c=t(27256),d=t(58038),x=t(91216),m=t(12714),u=t(77506),h=t(67327),p=t(91664),g=t(9969),f=t(41190),b=t(68136);function j({onClick:e,isLoading:s,label:t="Sign in with Google"}){return(0,r.jsxs)(p.z,{type:"button",variant:"outline",className:"w-full flex items-center justify-center gap-2",onClick:e,disabled:s,children:[s?r.jsx(u.Z,{className:"h-4 w-4 animate-spin"}):(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"h-5 w-5","aria-hidden":"true",children:[r.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),r.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),r.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),r.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),r.jsx("span",{children:s?"Signing in...":t})]})}t(85999);let y=c.Ry({email:c.Z_().email({message:"Please enter a valid email address"}),password:c.Z_().min(8,{message:"Password must be at least 8 characters"})});function v(){let{signIn:e,signInWithGoogle:s}=(0,b.a)();(0,i.useRouter)();let t=(0,i.useSearchParams)(),[c,v]=(0,a.useState)(!1),[w,N]=(0,a.useState)(!1),[k,S]=(0,a.useState)(!1);t.get("error");let Z=(0,n.cI)({resolver:(0,o.F)(y),defaultValues:{email:"",password:""}}),_=async s=>{try{v(!0),await e(s.email,s.password)}catch(e){console.error("Login error:",e)}finally{v(!1)}},C=async()=>{try{N(!0),await s()}catch(e){console.error("Google login error:",e)}finally{N(!1)}};return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[r.jsx(h.Z,{}),(0,r.jsxs)("div",{className:"flex flex-1 flex-row",children:[r.jsx("div",{className:"w-full lg:w-1/2 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-12",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto w-full",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(d.Z,{className:"h-10 w-10 text-primary"}),r.jsx("h1",{className:"text-2xl font-bold",children:"Legalock"})]}),r.jsx("h2",{className:"mt-6 text-3xl font-bold tracking-tight text-gray-900",children:"Sign in to your account"}),(0,r.jsxs)("p",{className:"mt-2 text-sm text-gray-600",children:["Or"," ",r.jsx(l.default,{href:"/register",className:"font-medium text-primary hover:text-primary/90",children:"create a new account"})]})]}),r.jsx(g.l0,{...Z,children:(0,r.jsxs)("form",{onSubmit:Z.handleSubmit(_),className:"space-y-6",children:[r.jsx(g.Wi,{control:Z.control,name:"email",render:({field:e})=>(0,r.jsxs)(g.xJ,{children:[r.jsx(g.lX,{children:"Email address"}),r.jsx(g.NI,{children:r.jsx(f.I,{placeholder:"<EMAIL>",type:"email",autoComplete:"email",disabled:c,...e})}),r.jsx(g.zG,{})]})}),r.jsx(g.Wi,{control:Z.control,name:"password",render:({field:e})=>(0,r.jsxs)(g.xJ,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx(g.lX,{children:"Password"}),r.jsx(l.default,{href:"/reset-password",className:"text-sm font-medium text-primary hover:text-primary/90",children:"Forgot password?"})]}),r.jsx(g.NI,{children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(f.I,{placeholder:"••••••••",type:k?"text":"password",autoComplete:"current-password",disabled:c,...e}),(0,r.jsxs)(p.z,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3",onClick:()=>S(!k),tabIndex:-1,children:[k?r.jsx(x.Z,{className:"h-4 w-4 text-gray-500","aria-hidden":"true"}):r.jsx(m.Z,{className:"h-4 w-4 text-gray-500","aria-hidden":"true"}),r.jsx("span",{className:"sr-only",children:k?"Hide password":"Show password"})]})]})}),r.jsx(g.zG,{})]})}),r.jsx(p.z,{type:"submit",className:"w-full",disabled:c||w,children:c?(0,r.jsxs)(r.Fragment,{children:[r.jsx(u.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign in with Email"}),(0,r.jsxs)("div",{className:"relative my-4",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("div",{className:"w-full border-t border-gray-300"})}),r.jsx("div",{className:"relative flex justify-center text-sm",children:r.jsx("span",{className:"bg-gray-50 px-2 text-gray-500",children:"Or continue with"})})]}),r.jsx(j,{onClick:C,isLoading:w})]})})]})}),r.jsx("div",{className:"hidden lg:block lg:w-1/2 bg-gradient-to-r from-blue-600 to-indigo-600",children:r.jsx("div",{className:"flex h-full items-center justify-center p-12",children:(0,r.jsxs)("div",{className:"max-w-lg text-white",children:[r.jsx("h2",{className:"text-3xl font-bold mb-6",children:"Welcome Back to Legalock"}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Your Digital Legacy Matters"}),r.jsx("p",{className:"text-white/90 mb-4",children:"Continue managing your digital legacy and ensuring your wishes are documented and secure. Your loved ones will thank you for your foresight and planning."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-semibold mb-3",children:"What You Can Do Today"}),(0,r.jsxs)("ul",{className:"space-y-4",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(d.Z,{className:"h-6 w-6 mr-3 flex-shrink-0 text-blue-300"}),r.jsx("span",{children:"Update your asset inventory with recent acquisitions"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(d.Z,{className:"h-6 w-6 mr-3 flex-shrink-0 text-blue-300"}),r.jsx("span",{children:"Upload important documents to your secure Digital Vault"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(d.Z,{className:"h-6 w-6 mr-3 flex-shrink-0 text-blue-300"}),r.jsx("span",{children:"Review and update your last wishes and final instructions"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(d.Z,{className:"h-6 w-6 mr-3 flex-shrink-0 text-blue-300"}),r.jsx("span",{children:"Manage your trustees and ensure they have proper access"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white/10 p-6 rounded-lg",children:[r.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Did You Know?"}),r.jsx("p",{className:"text-white/90",children:"The average person has over 90 online accounts. Without proper documentation, loved ones may struggle to locate and manage these accounts after your passing."})]})]})]})})})]})]})}},67327:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var r=t(10326);t(17577);var a=t(90434),l=t(46226),i=t(91664);let n=()=>r.jsx("header",{className:"w-full bg-white border-b border-gray-200",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-20",children:[r.jsx("div",{className:"flex items-center",children:r.jsx(a.default,{href:"/",className:"flex items-center",children:r.jsx(l.default,{src:"/images/Legalock-logo.svg",alt:"Legalock Logo",width:160,height:50,priority:!0})})}),r.jsx("div",{className:"flex items-center gap-4",children:r.jsx(i.z,{variant:"outline",asChild:!0,children:r.jsx(a.default,{href:"/",children:"Back to Home"})})})]})})})},91664:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var r=t(10326),a=t(17577),l=t(34214),i=t(79360),n=t(51223);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...i},c)=>{let d=a?l.g7:"button";return r.jsx(d,{className:(0,n.cn)(o({variant:s,size:t,className:e})),ref:c,...i})});c.displayName="Button"},9969:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},41190:(e,s,t)=>{"use strict";t.d(s,{I:()=>i});var r=t(10326),a=t(17577),l=t(51223);let i=a.forwardRef(({className:e,type:s,...t},a)=>r.jsx("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));i.displayName="Input"},51223:(e,s,t)=>{"use strict";t.d(s,{cn:()=>l});var r=t(41135),a=t(31009);function l(...e){return(0,a.m6)((0,r.W)(e))}},80974:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/login/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,8987,9168,5981,285,6686,8002],()=>t(60779));module.exports=r})();