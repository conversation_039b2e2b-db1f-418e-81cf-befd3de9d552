(()=>{var e={};e.id=630,e.ids=[630],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},81353:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(14433),r(56752),r(35866);var a=r(23191),n=r(88716),o=r(37922),l=r.n(o),s=r(95231),i={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>s[e]);r.d(t,i);let c=["",{children:["vault",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,14433)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/vault/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/vault/page.tsx"],u="/vault/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/vault/page",pathname:"/vault",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},84877:(e,t,r)=>{Promise.resolve().then(r.bind(r,34341))},86333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},41291:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},31540:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},12714:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},36283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},39572:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},9015:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},58038:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},98091:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63685:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},94019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},34341:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var a=r(10326),n=r(17577),o=r(68136),l=r(91664),s=r(63685),i=r(36283),c=r(58038),d=r(9015),u=r(12714),m=r(31540),p=r(98091),h=r(88270),f=r(39318),x=r(85999),y=r(44794),g=r(41190),w=r(41291),v=r(39572),b=r(94019),j=r(51223);let N=({onFileSelected:e,maxSizeMB:t=5120,acceptedFileTypes:r=[]})=>{let[o,i]=(0,n.useState)(null),[c,d]=(0,n.useState)(null),u=(0,n.useRef)(null),m=1048576*t,p=e=>{if(e.size>m)return d(`File size exceeds the maximum limit of ${t}MB`),!1;if(r.length>0){let t=e.type,a=e.name.split(".").pop()?.toLowerCase();if(!r.some(e=>!!(t===e||e.startsWith(".")&&a===e.substring(1))))return d("File type not accepted. Please upload a document, image, video, audio, or archive file."),!1}return d(null),!0};return a.jsx("div",{className:"w-full",children:o?a.jsx("div",{className:"border rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(v.Z,{className:"h-5 w-5 text-primary mr-2"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium truncate max-w-[200px] md:max-w-xs",children:o.name}),a.jsx("p",{className:"text-xs text-gray-500",children:(0,j.formatFileSize)(o.size)})]})]}),(0,a.jsxs)(l.z,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700",onClick:()=>{i(null),d(null),u.current&&(u.current.value="")},children:[a.jsx(b.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Remove file"})]})]})}):(0,a.jsxs)("div",{className:`border-2 border-dashed ${c?"border-red-300 bg-red-50":"border-gray-300 hover:bg-gray-50"} rounded-lg p-6 text-center cursor-pointer transition-colors`,onClick:()=>u.current?.click(),onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:t=>{t.preventDefault(),t.stopPropagation();let r=t.dataTransfer.files;if(r&&r.length>0){let t=r[0];p(t)&&(i(t),e(t))}},children:[c?a.jsx(w.Z,{className:"mx-auto h-12 w-12 text-red-400"}):a.jsx(s.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),a.jsx("p",{className:`mt-2 text-sm ${c?"text-red-600 font-medium":"text-gray-600"}`,children:c||"Drag and drop a file here, or click to select a file"}),r.length>0&&!c&&a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Accepted file types: PDF, Word, Excel, PowerPoint, Images, Videos, Audio, and more"}),a.jsx("input",{type:"file",className:"hidden",onChange:t=>{let r=t.target.files;if(r&&r.length>0){let t=r[0];p(t)?(i(t),e(t)):u.current&&(u.current.value="")}},ref:u,accept:r.join(",")})]})})};var O=r(77506);!function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();let D=[{value:"will",label:"Will"},{value:"trust",label:"Trust"},{value:"financial",label:"Financial"},{value:"medical",label:"Medical"},{value:"insurance",label:"Insurance"},{value:"property",label:"Property"},{value:"digital",label:"Digital"},{value:"other",label:"Other"}];function E({onSuccess:e}){let[t,r]=(0,n.useState)(""),[s,i]=(0,n.useState)(""),[c,u]=(0,n.useState)(null),[m,p]=(0,n.useState)(!1),{user:h}=(0,o.a)(),w=()=>{r(""),i(""),u(null)},v=async()=>{if(!c||!t||!s||!h){x.Am.error("Please fill in all fields and select a file");return}try{p(!0);let r=crypto.getRandomValues(new Uint8Array(32)),a=(0,f.sM)(r),n=await (0,f.Po)(c),o=new FileReader,l=new Promise(e=>{o.onloadend=()=>{let t=o.result.split(",")[1];e(t)}});o.readAsDataURL(n);let i=await l,d=await fetch("/api/documents/upload",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t,category:s,encryptedFile:{name:c.name,data:i},encryptionKey:a,fileType:c.type,fileSize:c.size})});if(!d.ok){let e=await d.json();throw Error(e.error||`Upload failed with status ${d.status}`)}await d.json(),x.Am.success("Document uploaded successfully and securely encrypted"),w(),e()}catch(e){console.error("Upload error:",e),x.Am.error(`Upload failed: ${e.message}`)}finally{p(!1)}};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(y._,{htmlFor:"documentName",children:"Document Name"}),a.jsx(g.I,{type:"text",id:"documentName",placeholder:"Document name",value:t,onChange:e=>{let t=e.target.value;t.length>0?r(t.charAt(0).toUpperCase()+t.slice(1)):r(t)}})]}),(0,a.jsxs)("div",{children:[a.jsx(y._,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{onValueChange:i,value:s,children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select a category"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:D.map(e=>a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{children:[a.jsx(y._,{children:"File"}),a.jsx(N,{onFileSelected:e=>u(e),maxSizeMB:5120,acceptedFileTypes:["application/pdf",".pdf","application/msword",".doc","application/vnd.openxmlformats-officedocument.wordprocessingml.document",".docx","application/vnd.ms-excel",".xls","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",".xlsx","application/vnd.ms-powerpoint",".ppt","application/vnd.openxmlformats-officedocument.presentationml.presentation",".pptx","image/jpeg",".jpg",".jpeg","image/png",".png","image/gif",".gif","image/tiff",".tif",".tiff","application/zip",".zip","application/x-rar-compressed",".rar","text/plain",".txt","text/csv",".csv","application/json",".json","application/xml",".xml","video/mp4",".mp4","video/quicktime",".mov","audio/mpeg",".mp3","audio/wav",".wav"]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md border border-blue-100",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium text-blue-800 mb-2 flex items-center",children:[a.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Secure Encryption"]}),a.jsx("p",{className:"text-xs text-blue-700 mb-2",children:"Your document will be encrypted before upload using AES-256 encryption. Only you and your designated trustees will be able to access the decrypted content."}),(0,a.jsxs)("p",{className:"text-xs text-blue-700",children:[a.jsx("strong",{children:"Maximum file size:"})," 5GB",a.jsx("br",{}),a.jsx("strong",{children:"Supported formats:"})," PDF, Word, Excel, PowerPoint, images, videos, audio, text files, and more."]})]}),a.jsx(l.z,{onClick:v,disabled:m,className:"w-full",children:m?(0,a.jsxs)(a.Fragment,{children:[a.jsx(O.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Encrypting & Uploading..."]}):"Encrypt & Upload Document"})]})}var U=r(35797);function _(){let{user:e}=(0,o.a)(),[t,r]=(0,n.useState)([]),[y,g]=(0,n.useState)("all"),[w,v]=(0,n.useState)(!0),[b,j]=(0,n.useState)(null),[N,O]=(0,n.useState)(null),[D,_]=(0,n.useState)(null),M=async()=>{try{v(!0);let e=new URLSearchParams;y&&"all"!==y&&e.append("category",y);let t=await fetch(`/api/documents?${e.toString()}`);if(!t.ok){let e=await t.json();throw Error(e.error||`Failed to fetch documents: ${t.status}`)}let a=await t.json();r(a||[])}catch(e){console.error("Error fetching documents:",e),x.Am.error(`Failed to load documents: ${e.message}`),r([])}finally{v(!1)}},k=async e=>{try{j(e.id);let t=await fetch(`/api/documents/${e.id}/download`);if(!t.ok){let e=await t.json();throw Error(e.error||`Failed to download file: ${t.status}`)}let r=t.headers.get("X-Encryption-Key");if(!r)throw Error("Encryption key not found in response");t.headers.get("X-Document-Name");let a=t.headers.get("X-Document-Path")||"";t.headers.get("X-Document-Type");let n=await t.blob();try{let e=(0,f.RG)(r);a.split("/").pop(),await crypto.subtle.importKey("raw",e,{name:"AES-GCM",length:256},!1,["decrypt"]);let t=URL.createObjectURL(n);window.open(t,"_blank")}catch(t){console.error("Error with crypto operations:",t);let e=URL.createObjectURL(n);window.open(e,"_blank")}}catch(e){console.error("Error viewing document:",e),x.Am.error(`Failed to view document: ${e.message}`)}finally{j(null)}},F=async e=>{try{j(e.id);let t=await fetch(`/api/documents/${e.id}/download`);if(!t.ok){let e=await t.json();throw Error(e.error||`Failed to download file: ${t.status}`)}let r=t.headers.get("X-Document-Name")||"document",a=t.headers.get("X-Document-Path")||"",n=await t.blob(),o=(a.split("/").pop()||"").split("_").slice(1).join("_").split(".").pop(),l=`${r}${o?"."+o:""}`,s=URL.createObjectURL(n),i=window.document.createElement("a");i.href=s,i.download=l,window.document.body.appendChild(i),i.click(),window.document.body.removeChild(i),setTimeout(()=>{URL.revokeObjectURL(s)},100),x.Am.success("Document downloaded successfully")}catch(e){console.error("Error downloading document:",e),x.Am.error(`Failed to download document: ${e.message}`)}finally{j(null)}},C=async e=>{try{O(e.id),_(null);let a=await fetch(`/api/documents/${e.id}`,{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||`Failed to delete document: ${a.status}`)}x.Am.success("Document deleted successfully"),r(t.filter(t=>t.id!==e.id))}catch(e){console.error("Error deleting document:",e),x.Am.error(`Failed to delete document: ${e.message}`)}finally{O(null)}};return a.jsx(U.Z,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-8",children:[a.jsx(h.Z,{}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Digital Vault",description:"Securely store and manage your important documents with end-to-end encryption."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-5",children:[a.jsx("div",{className:"feature-card-icon bg-blue-100",children:a.jsx(s.Z,{className:"h-5 w-5 text-blue-600"})}),a.jsx("h3",{className:"feature-card-title",children:"Upload Document"})]}),a.jsx(E,{onSuccess:M})]}),a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"feature-card",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"feature-card-icon bg-blue-100 mr-3",children:a.jsx(i.Z,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:"Your Documents"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.length," ",1===t.length?"document":"documents"," stored"]})]})]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx("div",{className:"mb-6",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:y,onValueChange:g,children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full md:w-[180px]",children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Filter by category"})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[{value:"all",label:"All Documents"},{value:"will",label:"Will"},{value:"trust",label:"Trust"},{value:"financial",label:"Financial"},{value:"medical",label:"Medical"},{value:"insurance",label:"Insurance"},{value:"property",label:"Property"},{value:"digital",label:"Digital"},{value:"other",label:"Other"}].map(e=>a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]})}),w?(0,a.jsxs)("div",{className:"text-center py-10",children:[a.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Loading documents..."})]}):0===t.length?(0,a.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[a.jsx(c.Z,{className:"feature-card-empty-icon text-blue-500"}),a.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"No Documents Yet"}),a.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Your digital vault is empty. Upload important documents to keep them secure and accessible to your trustees when needed."})]}):a.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:t.map(e=>(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden hover:shadow-md transition-shadow border border-gray-100",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center text-lg",children:[a.jsx(i.Z,{className:"mr-2 h-5 w-5 text-primary"}),a.jsx("span",{className:"truncate",children:e.name})]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"text-sm text-gray-500 mr-2",children:"Category:"}),a.jsx("span",{className:"text-sm font-medium bg-gray-100 px-2 py-0.5 rounded",children:e.category})]}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mb-4",children:[a.jsx(d.Z,{className:"h-3 w-3 mr-1 text-green-500"}),a.jsx("span",{children:"End-to-end encrypted"})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>k(e),disabled:b===e.id,children:b===e.id?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-primary border-t-transparent rounded-full"}),"Decrypting..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"View"]})}),a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>F(e),disabled:b===e.id,children:b===e.id?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-primary border-t-transparent rounded-full"}),"Decrypting..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"mr-2 h-4 w-4"}),"Download"]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:a.jsx(l.z,{variant:"ghost",size:"sm",className:"text-red-500 hover:text-red-700 hover:bg-red-50",disabled:b===e.id||N===e.id,children:N===e.id?a.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-red-500 border-t-transparent rounded-full"}):a.jsx(p.Z,{className:"h-4 w-4"})})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Delete Document"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:['Are you sure you want to delete "',e.name,'" from your vault? This action cannot be undone.']})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cancel"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>C(e),className:"bg-red-500 hover:bg-red-600",children:"Delete"})]})]})]})]})]})]},e.id))})]})]})})]})]})})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()},88270:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(10326);r(17577);var n=r(90434),o=r(86333);let l=()=>a.jsx("div",{className:"mb-6",children:(0,a.jsxs)(n.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[a.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>l});var a=r(10326),n=r(17577),o=r(51223);let l=n.forwardRef(({className:e,type:t,...r},n)=>a.jsx("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...r}));l.displayName="Input"},44794:(e,t,r)=>{"use strict";r.d(t,{_:()=>c});var a=r(10326),n=r(17577),o=r(34478),l=r(79360),s=r(51223);let i=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...t},r)=>a.jsx(o.Root,{ref:r,className:(0,s.cn)(i(),e),...t}));c.displayName=o.Root.displayName},39318:(e,t,r)=>{"use strict";r.d(t,{Po:()=>o,RG:()=>n,np:()=>l,sM:()=>a});let a=e=>{let t=new Uint8Array(e),r="";for(let e=0;e<t.byteLength;e++)r+=String.fromCharCode(t[e]);return btoa(r)},n=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},o=async e=>{try{let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.generateKey({name:"AES-GCM",length:256},!0,["encrypt","decrypt"]),n=await crypto.subtle.exportKey("raw",r);sessionStorage.setItem("lastEncryptionKey",a(n));let o=e.size,l=Math.ceil(o/10485760);if(1===l){let a=await e.arrayBuffer(),n=await crypto.subtle.encrypt({name:"AES-GCM",iv:t},r,a),o=new Uint8Array(1+t.length+n.byteLength);return o[0]=t.length,o.set(t,1),o.set(new Uint8Array(n),1+t.length),new File([o],e.name,{type:"application/encrypted",lastModified:e.lastModified})}{let a=new Uint8Array(1+t.length);a[0]=t.length,a.set(t,1);let n=[a];for(let a=0;a<l;a++){let l=10485760*a,s=Math.min(l+10485760,o),i=await e.slice(l,s).arrayBuffer(),c=await crypto.subtle.encrypt({name:"AES-GCM",iv:t},r,i);n.push(new Uint8Array(c))}return new File(n,e.name,{type:"application/encrypted",lastModified:e.lastModified})}}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt file")}},l=async(e,t,r,a)=>{try{let o="string"==typeof t?n(t):t,l=await crypto.subtle.importKey("raw",o,{name:"AES-GCM",length:256},!1,["decrypt"]),s=await e.slice(0,100).arrayBuffer(),i=new Uint8Array(s),c=i[0];if(c<1||c>16)throw console.error("Invalid IV length:",c),Error("Invalid file format or encryption");let d=i.slice(1,1+c),u=e.size,m=1+c;try{if(u-m<10485760){let t=await e.arrayBuffer(),n=new Uint8Array(t).slice(m),o=await crypto.subtle.decrypt({name:"AES-GCM",iv:d},l,n);return new File([o],r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}{let t=Math.ceil((u-m)/10485760),n=[];for(let r=0;r<t;r++){let t=m+10485760*r,a=Math.min(t+10485760,u),o=await e.slice(t,a).arrayBuffer(),s=await crypto.subtle.decrypt({name:"AES-GCM",iv:d},l,o);n.push(new Uint8Array(s))}return new File(n,r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}}catch(t){console.error("Crypto operation error:",t);try{let t=await e.arrayBuffer(),n=new Uint8Array(12).fill(1),o=await crypto.subtle.decrypt({name:"AES-GCM",iv:n},l,t);return new File([o],r||"decrypted-file",{type:a||"application/octet-stream",lastModified:Date.now()})}catch(e){throw console.error("Fallback decryption failed:",e),Error("Failed to decrypt file: "+e.message)}}}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt file: "+(e.message||"Unknown error"))}}},14433:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/vault/page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{Root:()=>s});var a=r(17577),n=r(45226),o=r(10326),l=a.forwardRef((e,t)=>(0,o.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=l},45226:(e,t,r)=>{"use strict";r.d(t,{WV:()=>l});var a=r(17577);r(60962);var n=r(34214),o=r(10326),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=(0,n.Z8)(`Primitive.${t}`),l=a.forwardRef((e,a)=>{let{asChild:n,...l}=e,s=n?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s,{...l,ref:a})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,8987,9168,5981,6292,285,8002,5797],()=>r(81353));module.exports=a})();