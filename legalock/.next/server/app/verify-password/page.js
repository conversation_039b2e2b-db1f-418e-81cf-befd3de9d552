(()=>{var e={};e.id=3141,e.ids=[3141],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},52145:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),t(98699),t(56752),t(35866);var r=t(23191),a=t(88716),i=t(37922),l=t.n(i),o=t(95231),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let c=["",{children:["verify-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98699)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/verify-password/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/verify-password/page.tsx"],u="/verify-password/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/verify-password/page",pathname:"/verify-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93069:(e,s,t)=>{Promise.resolve().then(t.bind(t,77963))},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9015:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},77963:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(10326),a=t(17577),i=t(90434),l=t(35047),o=t(58038),n=t(9015),c=t(21405),d=t(86333),u=t(91664),m=t(41190),x=t(44794),f=t(68136),p=t(85999),h=t(67327);function g(){let[e,s]=(0,a.useState)(""),[g,v]=(0,a.useState)(""),[y,b]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),{verifyOtp:k}=(0,f.a)(),N=(0,l.useRouter)();(0,l.useSearchParams)();let P=async s=>{if(s.preventDefault(),!g){p.Am.error("Email address is missing. Please go back and try again.");return}if(!e||6!==e.length){p.Am.error("Please enter a valid 6-digit verification code.");return}try{b(!0),await q(g,e),p.Am.success("Password updated successfully!"),N.push("/settings/security")}catch(e){console.error("Verification error:",e),p.Am.error(e.message||"Error verifying code"),b(!1)}},q=async(e,s)=>{try{let{supabase:r}=await Promise.all([t.e(6292),t.e(2777)]).then(t.bind(t,2777)),{error:a}=await r.auth.verifyOtp({email:e,token:s,type:"recovery"});if(a)throw a}catch(e){throw e}},_=async()=>{if(!g){p.Am.error("Email address is missing. Please go back and try again.");return}try{j(!0);let{supabase:e}=await Promise.all([t.e(6292),t.e(2777)]).then(t.bind(t,2777)),{error:s}=await e.auth.resetPasswordForEmail(g,{redirectTo:void 0});if(s)throw Error(s.message);p.Am.success("Verification code resent. Please check your email.")}catch(e){p.Am.error(e.message||"Failed to resend verification code")}finally{j(!1)}};return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[r.jsx(h.Z,{}),r.jsx("div",{className:"flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24",children:r.jsx("div",{className:"mx-auto w-full max-w-sm lg:w-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(o.Z,{className:"h-10 w-10 text-primary"}),r.jsx("h1",{className:"text-2xl font-bold",children:"Legalock"})]}),r.jsx("h2",{className:"mt-6 text-3xl font-bold tracking-tight text-gray-900",children:"Verify Password Change"}),(0,r.jsxs)("div",{className:"mt-8 flex flex-col items-center",children:[r.jsx("div",{className:"bg-blue-50 p-6 rounded-full",children:r.jsx(n.Z,{className:"h-12 w-12 text-primary"})}),(0,r.jsxs)("p",{className:"mt-6 text-center text-gray-600",children:["We've sent a 6-digit verification code to ",g||"your email address",". Please enter the code below to confirm your password change."]}),r.jsx("p",{className:"mt-4 text-center text-gray-500 text-sm",children:"If you don't see the email, check your spam folder or click the resend button below."})]}),r.jsx("form",{onSubmit:P,className:"mt-8 w-full",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(x._,{htmlFor:"verification-code",children:"Verification Code"}),r.jsx(m.I,{id:"verification-code",type:"text",inputMode:"numeric",pattern:"[0-9]*",maxLength:6,placeholder:"Enter 6-digit code",value:e,onChange:e=>s(e.target.value.replace(/[^0-9]/g,"")),className:"mt-1 block w-full text-center text-lg tracking-widest",required:!0})]}),r.jsx(u.z,{type:"submit",className:"w-full",disabled:y||6!==e.length,children:y?"Verifying...":"Confirm Password Change"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx(u.z,{type:"button",variant:"ghost",size:"sm",onClick:_,disabled:w,className:"text-sm",children:w?(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.Z,{className:"mr-2 h-3 w-3 animate-spin"}),"Resending..."]}):"Resend code"}),r.jsx(u.z,{variant:"ghost",size:"sm",asChild:!0,className:"text-sm",children:(0,r.jsxs)(i.default,{href:"/settings/security",children:[r.jsx(d.Z,{className:"mr-1 h-3 w-3"}),"Back to Security"]})})]})]})})]})})}),r.jsx("div",{className:"relative hidden w-0 flex-1 lg:block",children:r.jsx("div",{className:"absolute inset-0 h-full w-full bg-gradient-to-r from-blue-600 to-indigo-600",children:r.jsx("div",{className:"flex h-full items-center justify-center p-12",children:(0,r.jsxs)("div",{className:"max-w-xl text-white",children:[r.jsx("h2",{className:"text-3xl font-bold mb-6",children:"Secure Your Digital Legacy"}),r.jsx("p",{className:"text-xl mb-8",children:"Legalock helps you organize, protect, and pass on your digital assets and final wishes to your loved ones."}),(0,r.jsxs)("ul",{className:"space-y-4",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(o.Z,{className:"h-6 w-6 mr-2 flex-shrink-0"}),r.jsx("span",{children:"Create a comprehensive inventory of your digital assets"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(o.Z,{className:"h-6 w-6 mr-2 flex-shrink-0"}),r.jsx("span",{children:"Securely store sensitive documents for your trustees"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx(o.Z,{className:"h-6 w-6 mr-2 flex-shrink-0"}),r.jsx("span",{children:"Schedule future messages to loved ones"})]})]})]})})})})]})}},67327:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var r=t(10326);t(17577);var a=t(90434),i=t(46226),l=t(91664);let o=()=>r.jsx("header",{className:"w-full bg-white border-b border-gray-200",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-20",children:[r.jsx("div",{className:"flex items-center",children:r.jsx(a.default,{href:"/",className:"flex items-center",children:r.jsx(i.default,{src:"/images/Legalock-logo.svg",alt:"Legalock Logo",width:160,height:50,priority:!0})})}),r.jsx("div",{className:"flex items-center gap-4",children:r.jsx(l.z,{variant:"outline",asChild:!0,children:r.jsx(a.default,{href:"/",children:"Back to Home"})})})]})})})},91664:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var r=t(10326),a=t(17577),i=t(34214),l=t(79360),o=t(51223);let n=(0,l.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...l},c)=>{let d=a?i.g7:"button";return r.jsx(d,{className:(0,o.cn)(n({variant:s,size:t,className:e})),ref:c,...l})});c.displayName="Button"},41190:(e,s,t)=>{"use strict";t.d(s,{I:()=>l});var r=t(10326),a=t(17577),i=t(51223);let l=a.forwardRef(({className:e,type:s,...t},a)=>r.jsx("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));l.displayName="Input"},44794:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var r=t(10326),a=t(17577),i=t(34478),l=t(79360),o=t(51223);let n=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...s},t)=>r.jsx(i.Root,{ref:t,className:(0,o.cn)(n(),e),...s}));c.displayName=i.Root.displayName},51223:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(41135),a=t(31009);function i(...e){return(0,a.m6)((0,r.W)(e))}},98699:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/verify-password/page.tsx#default`)},34478:(e,s,t)=>{"use strict";t.d(s,{Root:()=>o});var r=t(17577),a=t(45226),i=t(10326),l=r.forwardRef((e,s)=>(0,i.jsx)(a.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var o=l},45226:(e,s,t)=>{"use strict";t.d(s,{WV:()=>l});var r=t(17577);t(60962);var a=t(34214),i=t(10326),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,s)=>{let t=(0,a.Z8)(`Primitive.${s}`),l=r.forwardRef((e,r)=>{let{asChild:a,...l}=e,o=a?t:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...l,ref:r})});return l.displayName=`Primitive.${s}`,{...e,[s]:l}},{})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,8987,9168,5981,285,8002],()=>t(52145));module.exports=r})();