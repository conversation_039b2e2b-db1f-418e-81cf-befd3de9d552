(()=>{var e={};e.id=8656,e.ids=[8656],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11655:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c}),r(48162),r(56752),r(35866);var a=r(23191),s=r(88716),n=r(37922),i=r.n(n),d=r(95231),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(t,o);let c=["",{children:["time-capsule",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48162)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/time-capsule/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/time-capsule/[id]/page.tsx"],u="/time-capsule/[id]/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/time-capsule/[id]/page",pathname:"/time-capsule/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76054:(e,t,r)=>{Promise.resolve().then(r.bind(r,76921))},31540:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},36283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},39572:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},97622:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Film",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M7 3v18",key:"bbkbws"}],["path",{d:"M3 7.5h4",key:"zfgn84"}],["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 16.5h4",key:"1230mu"}],["path",{d:"M17 3v18",key:"in4fa5"}],["path",{d:"M17 7.5h4",key:"myr1c1"}],["path",{d:"M17 16.5h4",key:"go4c1d"}]])},71709:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},56562:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(79404),s=r.n(a)},76921:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(10326),s=r(17577),n=r(35047),i=r(11394),d=r(65720),o=r(39572),c=r(71709),l=r(97622),u=r(56562),m=r(36283);let p=(0,r(62881).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var h=r(31540),x=r(91664),g=r(85999),y=r(88270),f=r(68136),v=r(90434);function b(){(0,n.useParams)(),(0,n.useRouter)();let{user:e}=(0,f.a)(),[t,r]=(0,s.useState)(null),[b,j]=(0,s.useState)([]),[_,N]=(0,s.useState)(!0),[k,M]=(0,s.useState)(!0),w=async(e,t)=>{try{let r=await fetch(e),a=await r.blob(),s=document.createElement("a");s.href=URL.createObjectURL(a),s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),g.Am.success("Download started")}catch(e){console.error("Error downloading file:",e),g.Am.error("Failed to download file")}},O=e=>{let t=o.Z;return e?.includes("image")?t=c.Z:e?.includes("video")?t=l.Z:e?.includes("audio")?t=u.Z:(e?.includes("pdf")||e?.includes("document"))&&(t=m.Z),a.jsx(t,{className:"h-4 w-4 mr-2 text-gray-500"})},D=e=>{let t=new Date(e),r=(0,i.p)(t),a=0===r?"12 AM":r<12?`${r} AM`:12===r?"12 PM":`${r-12} PM`;return`${(0,d.WU)(t,"MMMM d, yyyy")} at ${a}`};return _?(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[a.jsx(y.Z,{}),a.jsx("div",{className:"flex justify-center py-12",children:a.jsx("div",{className:"animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"})})]}):t?(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[a.jsx(y.Z,{}),a.jsx("div",{className:"mb-6",children:a.jsx(x.z,{variant:"outline",asChild:!0,children:(0,a.jsxs)(v.default,{href:"/time-capsule",children:[a.jsx(p,{className:"mr-2 h-4 w-4"}),"Back to Time Capsules"]})})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"max-w-3xl mx-auto",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:t.title}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["To: ",t.recipient_first_name&&t.recipient_last_name?`${t.recipient_first_name.charAt(0).toUpperCase()+t.recipient_first_name.slice(1)} ${t.recipient_last_name.charAt(0).toUpperCase()+t.recipient_last_name.slice(1)}`:t.recipient_name||t.recipient_email,a.jsx("br",{}),t.recipient_email,a.jsx("br",{}),"scheduled"===t.status?`To be delivered on: ${D(t.delivery_date)}`:`${"delivered"===t.status?"Delivered":"Cancelled"} on: ${D(t.delivery_date)}`]})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:(e=>{switch(e){case"scheduled":return"bg-blue-50 text-blue-700 border-blue-200";case"delivered":return"bg-green-50 text-green-700 border-green-200";default:return"bg-gray-50 text-gray-700 border-gray-200"}})(t.status),children:t.status.charAt(0).toUpperCase()+t.status.slice(1)})]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Message"}),a.jsx("div",{className:"bg-gray-50 p-4 rounded min-h-[100px] whitespace-pre-wrap",children:t.message||"No message content"})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Attachments"}),k?a.jsx("p",{className:"text-gray-500",children:"Loading attachments..."}):0===b.length?a.jsx("p",{className:"text-gray-500",children:"No attachments"}):a.jsx("div",{className:"space-y-2",children:b.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[O(e.file_type),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:e.file_name}),(0,a.jsxs)("p",{className:"text-gray-500 text-xs",children:[(e.file_size/1024/1024).toFixed(2)," MB"]})]})]}),e.signed_url&&(0,a.jsxs)(x.z,{size:"sm",variant:"outline",onClick:()=>w(e.signed_url,e.file_name),children:[a.jsx(h.Z,{className:"h-4 w-4 mr-1"}),"Download"]})]},e.id))})]})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Created on ",(0,d.WU)(new Date(t.created_at),"MMMM d, yyyy"),t.created_at!==t.updated_at&&` • Last updated on ${(0,d.WU)(new Date(t.updated_at),"MMMM d, yyyy")}`]})})]})]}):(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[a.jsx(y.Z,{}),(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Time Capsule Not Found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"The time capsule you're looking for doesn't exist or you don't have permission to view it."}),a.jsx(x.z,{asChild:!0,children:a.jsx(v.default,{href:"/time-capsule",children:"Back to Time Capsules"})})]})]})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()},88270:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(10326);r(17577);var s=r(90434),n=r(86333);let i=()=>a.jsx("div",{className:"mb-6",children:(0,a.jsxs)(s.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[a.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var a=r(10326),s=r(17577),n=r(34214),i=r(79360),d=r(51223);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...i},c)=>{let l=s?n.g7:"button";return a.jsx(l,{className:(0,d.cn)(o({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(41135),s=r(31009);function n(...e){return(0,s.m6)((0,a.W)(e))}},48162:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/time-capsule/[id]/page.tsx#default`)},11394:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(77120);function s(e,t){return(0,a.Q)(e,t?.in).getHours()}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,8987,9168,5981,7600,8002],()=>r(11655));module.exports=a})();