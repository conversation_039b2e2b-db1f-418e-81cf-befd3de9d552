(()=>{var e={};e.id=8434,e.ids=[8434],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},90515:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c}),r(57554),r(56752),r(35866);var a=r(23191),s=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["time-capsule",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57554)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/time-capsule/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Desktop/Legalock/legalock/src/app/time-capsule/page.tsx"],m="/time-capsule/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/time-capsule/page",pathname:"/time-capsule",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24333:(e,t,r)=>{Promise.resolve().then(r.bind(r,42518))},24230:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},37358:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48998:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},31540:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},36283:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},39572:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},97622:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Film",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M7 3v18",key:"bbkbws"}],["path",{d:"M3 7.5h4",key:"zfgn84"}],["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 16.5h4",key:"1230mu"}],["path",{d:"M17 3v18",key:"in4fa5"}],["path",{d:"M17 7.5h4",key:"myr1c1"}],["path",{d:"M17 16.5h4",key:"go4c1d"}]])},71709:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},77506:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5932:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},56562:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},98091:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63685:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},79635:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},94019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(79404),s=r.n(a)},42518:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var a=r(10326),s=r(17577),n=r(11394),i=r(65720),o=r(39572),l=r(71709),c=r(97622),d=r(56562),m=r(36283),u=r(48998),h=r(5932),p=r(79635),x=r(37358);let f=(0,r(62881).Z)("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);var g=r(24230),j=r(98091),y=r(31540),v=r(91664),_=r(88270),N=r(68136),b=r(85999),O=r(74723),w=r(74064),D=r(27256),E=r(9969),M=r(41190),U=r(63685),C=r(94019),k=r(77506),T=r(77120),F=r(47602);function Z(e,t,r){let a=(0,T.Q)(e,r?.in);return isNaN(t)?(0,F.L)(r?.in||e,NaN):(t&&a.setDate(a.getDate()+t),a)}(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/calendar'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/popover'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();let L=D.Ry({title:D.Z_().min(2,{message:"Title must be at least 2 characters"}).max(100),message:D.Z_().max(5e3,{message:"Message must be less than 5000 characters"}).optional(),recipient_first_name:D.Z_().min(1,{message:"First name is required"}),recipient_last_name:D.Z_().min(1,{message:"Last name is required"}),recipient_email:D.Z_().email({message:"Please enter a valid email address"}),delivery_date:D.hT({required_error:"Please select a delivery date"}).refine(e=>{var t;return t=Z(new Date,1),+(0,T.Q)(e)>+(0,T.Q)(t)},{message:"Delivery date must be at least 1 day in the future"}),delivery_hour:D.Z_().min(1,{message:"Please select a delivery hour"})}),A=({onSuccess:e})=>{let[t,r]=(0,s.useState)(!1),[n,l]=(0,s.useState)([]),[c,d]=(0,s.useState)(0),[m,u]=(0,s.useState)(!1),h=(0,s.useRef)(null),p=(0,O.cI)({resolver:(0,w.F)(L),defaultValues:{title:"",message:"",recipient_first_name:"",recipient_last_name:"",recipient_email:"",delivery_date:Z(new Date,7),delivery_hour:"12"}}),f=e=>{l(n.filter((t,r)=>r!==e))},g=async e=>{if(0===n.length)return[];u(!0);let t=[],r=n.length;try{for(let a=0;a<n.length;a++){let s=n[a],i=s.name.split(".").pop(),o=`${Math.random().toString(36).substring(2)}.${i}`,l=`${e}/${o}`,c=new FormData;c.append("file",s),c.append("path",l);let m=await fetch("/api/time-capsules/upload",{method:"POST",body:c});if(!m.ok){let e=await m.json();throw Error(e.error||"Failed to upload file")}t.push({path:l,name:s.name,type:s.type,size:s.size}),d(Math.round((a+1)/r*100))}return t}catch(e){throw console.error("Error uploading files:",e),b.Am.error("Failed to upload files"),e}finally{u(!1),d(0)}},j=async t=>{r(!0);try{let r=await fetch("/api/time-capsules",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:t.title,message:t.message||"",recipient_name:`${t.recipient_first_name} ${t.recipient_last_name}`,recipient_first_name:t.recipient_first_name,recipient_last_name:t.recipient_last_name,recipient_email:t.recipient_email,delivery_date:(function(e,t,r){let a=(0,T.Q)(e,void 0);return a.setHours(t),a})(t.delivery_date,parseInt(t.delivery_hour,10)).toISOString()})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to create time capsule")}let a=await r.json();if(n.length>0)try{let e=await g(a.id);e.length>0&&!(await fetch("/api/time-capsules/media",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({capsule_id:a.id,files:e})})).ok&&console.error("Failed to save file metadata")}catch(e){console.error("Error uploading files:",e),b.Am.error("Time capsule created but file upload failed")}b.Am.success("Time capsule created successfully"),p.reset({title:"",message:"",recipient_first_name:"",recipient_last_name:"",recipient_email:"",delivery_date:Z(new Date,7),delivery_hour:"12"}),l([]),e()}catch(e){console.error("Error creating time capsule:",e),b.Am.error(e.message||"Failed to create time capsule")}finally{r(!1)}};return a.jsx(E.l0,{...p,children:(0,a.jsxs)("form",{onSubmit:p.handleSubmit(j),className:"space-y-4",children:[a.jsx(E.Wi,{control:p.control,name:"title",render:({field:e})=>(0,a.jsxs)(E.xJ,{children:[a.jsx(E.lX,{children:"Title*"}),a.jsx(E.NI,{children:a.jsx(M.I,{placeholder:"Message for my children",...e})}),a.jsx(E.zG,{})]})}),a.jsx(E.Wi,{control:p.control,name:"message",render:({field:e})=>(0,a.jsxs)(E.xJ,{children:[a.jsx(E.lX,{children:"Message"}),a.jsx(E.NI,{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Write your message here...",className:"h-24",...e})}),a.jsx(E.zG,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(E.Wi,{control:p.control,name:"recipient_first_name",render:({field:e})=>(0,a.jsxs)(E.xJ,{children:[a.jsx(E.lX,{children:"First Name*"}),a.jsx(E.NI,{children:a.jsx(M.I,{placeholder:"John",...e})}),a.jsx(E.zG,{})]})}),a.jsx(E.Wi,{control:p.control,name:"recipient_last_name",render:({field:e})=>(0,a.jsxs)(E.xJ,{children:[a.jsx(E.lX,{children:"Last Name*"}),a.jsx(E.NI,{children:a.jsx(M.I,{placeholder:"Doe",...e})}),a.jsx(E.zG,{})]})})]}),a.jsx(E.Wi,{control:p.control,name:"recipient_email",render:({field:e})=>(0,a.jsxs)(E.xJ,{children:[a.jsx(E.lX,{children:"Recipient Email*"}),a.jsx(E.NI,{children:a.jsx(M.I,{type:"email",placeholder:"<EMAIL>",...e})}),a.jsx(E.zG,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(E.Wi,{control:p.control,name:"delivery_date",render:({field:e})=>(0,a.jsxs)(E.xJ,{className:"flex flex-col",children:[a.jsx(E.lX,{children:"Delivery Date*"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/popover'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/popover'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:a.jsx(E.NI,{children:(0,a.jsxs)(v.z,{variant:"outline",className:"w-full justify-start text-left font-normal",children:[a.jsx(x.Z,{className:"mr-2 h-4 w-4"}),e.value?(0,i.WU)(e.value,"MMMM d, yyyy"):a.jsx("span",{children:"Select a date"})]})})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/popover'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-auto p-0",children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/calendar'");throw e.code="MODULE_NOT_FOUND",e}()),{mode:"single",selected:e.value,onSelect:t=>{e.onChange(t),document.body.click()},disabled:e=>{var t;return t=Z(new Date,1),+(0,T.Q)(e)<+(0,T.Q)(t)},initialFocus:!0})})]}),a.jsx(E.pf,{children:"Date of delivery"}),a.jsx(E.zG,{})]})}),a.jsx(E.Wi,{control:p.control,name:"delivery_hour",render:({field:e})=>(0,a.jsxs)(E.xJ,{children:[a.jsx(E.lX,{children:"Delivery Hour*"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{onValueChange:e.onChange,defaultValue:e.value,children:[a.jsx(E.NI,{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select hour"})})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:Array.from({length:24},(e,t)=>t).map(e=>a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.toString(),children:0===e?"12 AM (Midnight)":e<12?`${e} AM`:12===e?"12 PM (Noon)":`${e-12} PM`},e))})]}),a.jsx(E.pf,{children:"Hour of delivery (in 24-hour format)"}),a.jsx(E.zG,{})]})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(E.lX,{children:"Attachment"}),(0,a.jsxs)("div",{className:"relative group",children:[a.jsx("span",{className:"cursor-help text-gray-500 bg-gray-100 rounded-full w-5 h-5 inline-flex items-center justify-center text-xs font-medium",children:"i"}),a.jsx("div",{className:"absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 invisible group-hover:visible bg-gray-800 text-white text-xs rounded py-1 px-2 w-64",children:"Allowed formats: PDF, DOC, DOCX, JPG, PNG, MP3, MP4 (max 250MB)"})]})]}),(0,a.jsxs)("div",{className:"border border-dashed border-gray-300 rounded-md p-6 text-center",children:[a.jsx("input",{type:"file",onChange:e=>{if(e.target.files){let t=Array.from(e.target.files);if(n.length+t.length>1){b.Am.error("Only 1 file can be attached to a time capsule");return}if(t.filter(e=>e.size>262144e3).length>0){b.Am.error("File exceeds the 250MB size limit");return}l([...n,...t])}},className:"hidden",ref:h}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(v.z,{type:"button",variant:"outline",onClick:()=>h.current?.click(),disabled:n.length>=1||t||m,children:[a.jsx(U.Z,{className:"mr-2 h-4 w-4"}),"Add File"]}),a.jsx("p",{className:"text-sm text-gray-500",children:"Add 1 file (max 250MB)"})]}),n.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"text-sm font-medium mb-2",children:"Selected Files:"}),a.jsx("ul",{className:"space-y-2",children:n.map((e,r)=>(0,a.jsxs)("li",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 mr-2 text-gray-500"}),(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("p",{className:"font-medium truncate max-w-[200px]",children:e.name}),(0,a.jsxs)("p",{className:"text-gray-500 text-xs",children:[(e.size/1024/1024).toFixed(2)," MB"]})]})]}),a.jsx(v.z,{type:"button",variant:"ghost",size:"sm",onClick:()=>f(r),disabled:t||m,children:a.jsx(C.Z,{className:"h-4 w-4"})})]},r))})]})]})]}),a.jsx(v.z,{type:"submit",className:"w-full",disabled:t||m,children:t||m?(0,a.jsxs)(a.Fragment,{children:[a.jsx(k.Z,{className:"mr-2 h-4 w-4 animate-spin"}),m?`Uploading (${c}%)`:"Creating..."]}):"Create Time Capsule"})]})]})})};function S(){let{user:e}=(0,N.a)(),[t,r]=(0,s.useState)([]),[O,w]=(0,s.useState)(!0),[D,E]=(0,s.useState)(null),[M,U]=(0,s.useState)(!1),[C,k]=(0,s.useState)([]),[T,F]=(0,s.useState)(!1),[Z,L]=(0,s.useState)(null),[S,z]=(0,s.useState)({}),P=async()=>{try{w(!0);let e=await fetch("/api/time-capsules");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to load time capsules")}let t=await e.json();if(r(t||[]),t&&t.length>0){let e=t.map(async e=>{try{let t=await fetch(`/api/time-capsules/media?capsule_id=${e.id}`);if(t.ok){let r=await t.json();return{capsuleId:e.id,media:r}}return{capsuleId:e.id,media:[]}}catch(t){return console.error(`Error fetching media for capsule ${e.id}:`,t),{capsuleId:e.id,media:[]}}}),r=await Promise.all(e),a={};r.forEach(e=>{a[e.capsuleId]=e.media}),z(a)}}catch(e){console.error("Error fetching time capsules:",e),b.Am.error(e.message||"Failed to load time capsules")}finally{w(!1)}},$=async e=>{E(e),U(!0);try{F(!0);let t=await fetch(`/api/time-capsules/media?capsule_id=${e.id}`);if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to load media")}let r=await t.json();k(r)}catch(e){console.error("Error fetching media:",e),k([])}finally{F(!1)}},I=async e=>{try{let t=await fetch(`/api/time-capsules?id=${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete time capsule")}b.Am.success("Time capsule deleted"),P()}catch(e){console.error("Error deleting time capsule:",e),b.Am.error(e.message||"Failed to delete time capsule")}finally{L(null)}},q=async(e,t)=>{try{let r=await fetch(e),a=await r.blob(),s=document.createElement("a");s.href=URL.createObjectURL(a),s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),b.Am.success("Download started")}catch(e){console.error("Error downloading file:",e),b.Am.error("Failed to download file")}},W=e=>{let t=o.Z;return e?.includes("image")?t=l.Z:e?.includes("video")?t=c.Z:e?.includes("audio")?t=d.Z:(e?.includes("pdf")||e?.includes("document"))&&(t=m.Z),a.jsx(t,{className:"h-4 w-4 mr-2 text-gray-500"})},H=e=>{let t=new Date(e),r=(0,n.p)(t),a=0===r?"12 AM":r<12?`${r} AM`:12===r?"12 PM":`${r-12} PM`;return`${(0,i.WU)(t,"MMMM d, yyyy")} at ${a}`};return(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[a.jsx(_.Z,{}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Time Capsule",description:"Create messages to be delivered to your loved ones in the future.",icon:a.jsx(u.Z,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8",children:[(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-5",children:[a.jsx("div",{className:"feature-card-icon bg-blue-100",children:a.jsx(u.Z,{className:"h-5 w-5 text-blue-600"})}),a.jsx("h3",{className:"feature-card-title",children:"Create Time Capsule"})]}),a.jsx(A,{onSuccess:P})]}),a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"feature-card",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"feature-card-icon bg-blue-100 mr-3",children:a.jsx(h.Z,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:"Your Time Capsules"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.length," ",1===t.length?"capsule":"capsules"," created"]})]})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:O?a.jsx("div",{className:"flex justify-center py-8",children:a.jsx("div",{className:"animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"})}):0===t.length?(0,a.jsxs)("div",{className:"feature-card-empty bg-blue-50",children:[a.jsx(u.Z,{className:"feature-card-empty-icon text-blue-500"}),a.jsx("h4",{className:"feature-card-empty-title text-blue-900",children:"No Time Capsules Yet"}),a.jsx("p",{className:"feature-card-empty-description text-blue-700",children:"Create messages to be delivered to your loved ones in the future."})]}):a.jsx("div",{className:"grid grid-cols-1 gap-4",children:t.map(e=>(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden hover:shadow-md transition-shadow border border-gray-100",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg",children:e.title.charAt(0).toUpperCase()+e.title.slice(1)}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"scheduled"===e.status?"default":"delivered"===e.status?"secondary":"outline",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Created on ",new Date(e.created_at).toLocaleDateString()]})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm",children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2 text-gray-500"}),a.jsx("span",{className:"text-gray-700",children:e.recipient_first_name&&e.recipient_last_name?`${e.recipient_first_name.charAt(0).toUpperCase()+e.recipient_first_name.slice(1)} ${e.recipient_last_name.charAt(0).toUpperCase()+e.recipient_last_name.slice(1)}`:e.recipient_name||e.recipient_email.split("@")[0]||"No recipient name"}),a.jsx("span",{className:"mx-2 text-gray-400",children:"•"}),a.jsx("span",{className:"text-gray-700",children:e.recipient_email})]}),(0,a.jsxs)("div",{className:"flex items-center text-sm",children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2 text-gray-500"}),(0,a.jsxs)("span",{className:"text-gray-700",children:["Delivery: ",H(e.delivery_date)]})]})]}),e.message&&a.jsx("div",{className:"mt-3 text-sm text-gray-600 line-clamp-1",children:(e.message.charAt(0).toUpperCase()+e.message.slice(1)).substring(0,60)+(e.message.length>60?"...":"")}),S[e.id]&&S[e.id].length>0&&(0,a.jsxs)("div",{className:"mt-3 pt-2 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-1",children:[a.jsx(f,{className:"h-3 w-3 mr-1"}),(0,a.jsxs)("span",{children:[S[e.id].length," attachment",1!==S[e.id].length?"s":""]})]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:S[e.id].map(e=>{let t=o.Z;return e.file_type.startsWith("image/")?t=l.Z:e.file_type.startsWith("video/")?t=c.Z:e.file_type.startsWith("audio/")?t=d.Z:(e.file_type.includes("pdf")||e.file_type.includes("document"))&&(t=m.Z),(0,a.jsxs)("div",{className:"flex items-center bg-gray-50 rounded px-2 py-1 text-xs",title:e.file_name,children:[a.jsx(t,{className:"h-3 w-3 mr-1 text-gray-500"}),a.jsx("span",{className:"truncate max-w-[100px]",children:e.file_name})]},e.id)})})]})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex justify-between pt-2 border-t",children:[(0,a.jsxs)(v.z,{variant:"outline",size:"sm",onClick:()=>$(e),children:["View Details",a.jsx(g.Z,{className:"ml-2 h-3 w-3"})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,a.jsxs)(v.z,{variant:"outline",size:"sm",className:"text-red-600 border-red-200 hover:bg-red-50",children:[a.jsx(j.Z,{className:"h-3 w-3 mr-1"}),"Delete"]})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Delete Time Capsule"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Are you sure you want to delete this time capsule? This action cannot be undone."})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cancel"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>I(e.id),className:"bg-red-600 hover:bg-red-700",children:"Delete"})]})]})]})]})]},e.id))})})]})})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{open:M,onOpenChange:U,children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:D&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:D.title.charAt(0).toUpperCase()+D.title.slice(1)}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["To: ",D.recipient_first_name&&D.recipient_last_name?`${D.recipient_first_name.charAt(0).toUpperCase()+D.recipient_first_name.slice(1)} ${D.recipient_last_name.charAt(0).toUpperCase()+D.recipient_last_name.slice(1)}`:D.recipient_name||D.recipient_email,a.jsx("br",{}),D.recipient_email,a.jsx("br",{}),"scheduled"===D.status?`To be delivered on: ${H(D.delivery_date)}`:`${"delivered"===D.status?"Delivered":"Cancelled"} on: ${H(D.delivery_date)}`]})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"scheduled"===D.status?"default":"delivered"===D.status?"secondary":"outline",children:D.status.charAt(0).toUpperCase()+D.status.slice(1)})]})}),(0,a.jsxs)("div",{className:"space-y-6 my-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Message"}),a.jsx("div",{className:"bg-gray-50 p-4 rounded min-h-[100px] whitespace-pre-wrap",children:D.message?D.message.charAt(0).toUpperCase()+D.message.slice(1):"No message content"})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Attachments"}),T?a.jsx("p",{className:"text-gray-500",children:"Loading attachments..."}):0===C.length?a.jsx("p",{className:"text-gray-500",children:"No attachments"}):a.jsx("div",{className:"space-y-2",children:C.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[W(e.file_type),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-sm",children:e.file_name}),(0,a.jsxs)("p",{className:"text-gray-500 text-xs",children:[(e.file_size/1024/1024).toFixed(2)," MB"]})]})]}),e.signed_url&&(0,a.jsxs)(v.z,{size:"sm",variant:"outline",onClick:()=>q(e.signed_url,e.file_name),children:[a.jsx(y.Z,{className:"h-4 w-4 mr-1"}),"Download"]})]},e.id))})]})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Created on ",(0,i.WU)(new Date(D.created_at),"MMMM d, yyyy"),D.created_at!==D.updated_at&&` • Last updated on ${(0,i.WU)(new Date(D.updated_at),"MMMM d, yyyy")}`]})})]})})})]})}(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()},88270:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(10326);r(17577);var s=r(90434),n=r(86333);let i=()=>a.jsx("div",{className:"mb-6",children:(0,a.jsxs)(s.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[a.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var a=r(10326),s=r(17577),n=r(34214),i=r(79360),o=r(51223);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...i},c)=>{let d=s?n.g7:"button";return a.jsx(d,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:c,...i})});c.displayName="Button"},9969:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var a=r(10326),s=r(17577),n=r(51223);let i=s.forwardRef(({className:e,type:t,...r},s)=>a.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...r}));i.displayName="Input"},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(41135),s=r(31009);function n(...e){return(0,s.m6)((0,a.W)(e))}},57554:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/time-capsule/page.tsx#default`)},11394:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(77120);function s(e,t){return(0,a.Q)(e,t?.in).getHours()}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,8987,9168,5981,6686,7600,8002],()=>r(90515));module.exports=a})();