(()=>{var e={};e.id=6938,e.ids=[6938],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},80171:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>d}),t(95964),t(56752),t(35866);var n=t(23191),s=t(88716),a=t(37922),o=t.n(a),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(r,c);let d=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95964)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/page.tsx"],u="/settings/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25742:(e,r,t)=>{Promise.resolve().then(t.bind(t,60140))},86333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},24230:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},9015:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},88378:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},79635:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},90434:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var n=t(79404),s=t.n(n)},60140:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var n=t(10326);t(17577);var s=t(90434),a=t(35047),o=t(88378),i=t(79635),c=t(24230),d=t(9015);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var l=t(91664);!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var u=t(88270);function m(){return(0,a.useRouter)(),(0,n.jsxs)("div",{className:"container mx-auto py-8",children:[n.jsx(u.Z,{}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Account Settings",description:"Manage your account settings and preferences",icon:n.jsx(o.Z,{className:"h-6 w-6"})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-8",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hover:shadow-md transition-shadow",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[n.jsx(i.Z,{className:"h-5 w-5 text-primary mr-2"}),"Profile Information"]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Update your personal information"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("p",{className:"text-sm text-gray-600",children:"Manage your name, email address, and other personal details."})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(l.z,{asChild:!0,variant:"outline",className:"w-full",children:(0,n.jsxs)(s.default,{href:"/settings/profile",className:"flex items-center justify-center",children:["Edit Profile",n.jsx(c.Z,{className:"ml-2 h-4 w-4"})]})})})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hover:shadow-md transition-shadow",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[n.jsx(d.Z,{className:"h-5 w-5 text-primary mr-2"}),"Security"]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Manage your password and security settings"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("p",{className:"text-sm text-gray-600",children:"Update your password and manage other security settings to keep your account secure."})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(l.z,{asChild:!0,variant:"outline",className:"w-full",children:(0,n.jsxs)(s.default,{href:"/settings/security",className:"flex items-center justify-center",children:["Security Settings",n.jsx(c.Z,{className:"ml-2 h-4 w-4"})]})})})]})]})]})}},88270:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var n=t(10326);t(17577);var s=t(90434),a=t(86333);let o=()=>n.jsx("div",{className:"mb-6",children:(0,n.jsxs)(s.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[n.jsx(a.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},91664:(e,r,t)=>{"use strict";t.d(r,{z:()=>d});var n=t(10326),s=t(17577),a=t(34214),o=t(79360),i=t(51223);let c=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...o},d)=>{let l=s?a.g7:"button";return n.jsx(l,{className:(0,i.cn)(c({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(41135),s=t(31009);function a(...e){return(0,s.m6)((0,n.W)(e))}},95964:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[9276,8987,9168,5981,8002],()=>t(80171));module.exports=n})();