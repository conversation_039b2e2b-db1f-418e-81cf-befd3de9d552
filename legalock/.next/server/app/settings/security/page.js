(()=>{var e={};e.id=7724,e.ids=[7724,2777],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},56666:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>c}),s(63013),s(56752),s(35866);var t=s(23191),a=s(88716),n=s(37922),o=s.n(n),i=s(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let c=["",{children:["settings",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,63013)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/security/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/security/page.tsx"],u="/settings/security/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/settings/security/page",pathname:"/settings/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},40049:(e,r,s)=>{Promise.resolve().then(s.bind(s,40357))},86333:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},91216:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(62881).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12714:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(62881).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9015:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(62881).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},90434:(e,r,s)=>{"use strict";s.d(r,{default:()=>a.a});var t=s(79404),a=s.n(t)},40357:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g});var t=s(10326),a=s(17577),n=s(35047),o=s(90434),i=s(68136),d=s(2777),c=s(86333),l=s(9015),u=s(91216),p=s(12714),m=s(91664),h=s(41190),x=s(44794);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var f=s(85999);function g(){let e=(0,n.useRouter)(),{user:r,loading:s,updatePassword:g}=(0,i.a)(),[w,v]=(0,a.useState)(""),[y,b]=(0,a.useState)(""),[N,j]=(0,a.useState)(""),[O,I]=(0,a.useState)(!1),[_,k]=(0,a.useState)(!1),[C,U]=(0,a.useState)(!1),[E,P]=(0,a.useState)(!1),D=async s=>{if(s.preventDefault(),r){if(y.length<8){f.Am.error("New password must be at least 8 characters long");return}if(y!==N){f.Am.error("New passwords do not match");return}try{I(!0);let{error:s}=await d.supabase.auth.signInWithPassword({email:r.email,password:w});if(s){f.Am.error("Current password is incorrect");return}await g(y),v(""),b(""),j(""),e.push(`/verify-password?email=${encodeURIComponent(r.email)}`)}catch(e){console.error("Error updating password:",e)}finally{I(!1)}}};return s?(0,t.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[t.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),t.jsx("p",{className:"mt-4 text-gray-500",children:"Loading security settings..."})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[t.jsx(m.z,{variant:"outline",asChild:!0,className:"mb-6",children:(0,t.jsxs)(o.default,{href:"/settings",children:[t.jsx(c.Z,{className:"mr-2 h-4 w-4"}),"Back to Settings"]})}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Security Settings",description:"Manage your password and security preferences",icon:t.jsx(l.Z,{className:"h-6 w-6"})}),t.jsx("div",{className:"max-w-2xl mx-auto mt-8",children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Change Password"}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Update your password to keep your account secure"})]}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(x._,{htmlFor:"current-password",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(h.I,{id:"current-password",type:_?"text":"password",value:w,onChange:e=>v(e.target.value),placeholder:"Enter your current password",required:!0}),(0,t.jsxs)(m.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>k(!_),children:[_?t.jsx(u.Z,{className:"h-4 w-4 text-gray-500"}):t.jsx(p.Z,{className:"h-4 w-4 text-gray-500"}),t.jsx("span",{className:"sr-only",children:_?"Hide password":"Show password"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(x._,{htmlFor:"new-password",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(h.I,{id:"new-password",type:C?"text":"password",value:y,onChange:e=>b(e.target.value),placeholder:"Enter your new password",required:!0}),(0,t.jsxs)(m.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>U(!C),children:[C?t.jsx(u.Z,{className:"h-4 w-4 text-gray-500"}):t.jsx(p.Z,{className:"h-4 w-4 text-gray-500"}),t.jsx("span",{className:"sr-only",children:C?"Hide password":"Show password"})]})]}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/password-strength-indicator'");throw e.code="MODULE_NOT_FOUND",e}()),{password:y,className:"mt-2"}),t.jsx("p",{className:"text-xs text-gray-500",children:"Password must be at least 8 characters long"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(x._,{htmlFor:"confirm-password",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(h.I,{id:"confirm-password",type:E?"text":"password",value:N,onChange:e=>j(e.target.value),placeholder:"Confirm your new password",required:!0}),(0,t.jsxs)(m.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>P(!E),children:[E?t.jsx(u.Z,{className:"h-4 w-4 text-gray-500"}):t.jsx(p.Z,{className:"h-4 w-4 text-gray-500"}),t.jsx("span",{className:"sr-only",children:E?"Hide password":"Show password"})]})]})]}),(0,t.jsxs)("div",{className:"pt-4",children:[t.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Changing your password will require verification. We'll send a verification code to your email address."}),t.jsx("div",{className:"flex justify-end",children:t.jsx(m.z,{type:"submit",disabled:O,children:O?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"}),"Processing..."]}):"Update Password"})})]})]})})]})})]})}(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/password-strength-indicator'");throw e.code="MODULE_NOT_FOUND",e}()},91664:(e,r,s)=>{"use strict";s.d(r,{z:()=>c});var t=s(10326),a=s(17577),n=s(34214),o=s(79360),i=s(51223);let d=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:s,asChild:a=!1,...o},c)=>{let l=a?n.g7:"button";return t.jsx(l,{className:(0,i.cn)(d({variant:r,size:s,className:e})),ref:c,...o})});c.displayName="Button"},41190:(e,r,s)=>{"use strict";s.d(r,{I:()=>o});var t=s(10326),a=s(17577),n=s(51223);let o=a.forwardRef(({className:e,type:r,...s},a)=>t.jsx("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));o.displayName="Input"},44794:(e,r,s)=>{"use strict";s.d(r,{_:()=>c});var t=s(10326),a=s(17577),n=s(34478),o=s(79360),i=s(51223);let d=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...r},s)=>t.jsx(n.Root,{ref:s,className:(0,i.cn)(d(),e),...r}));c.displayName=n.Root.displayName},2777:(e,r,s)=>{"use strict";s.d(r,{supabase:()=>i});var t=s(56292);let a="https://ccwvtcudztphwwzzgwvg.supabase.co",n=null,o=null,i=(0,t.eI)(a,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(o)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";o=(0,t.eI)(a,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(41135),a=s(31009);function n(...e){return(0,a.m6)((0,t.W)(e))}},63013:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/security/page.tsx#default`)},34478:(e,r,s)=>{"use strict";s.d(r,{Root:()=>i});var t=s(17577),a=s(45226),n=s(10326),o=t.forwardRef((e,r)=>(0,n.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var i=o},45226:(e,r,s)=>{"use strict";s.d(r,{WV:()=>o});var t=s(17577);s(60962);var a=s(34214),n=s(10326),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let s=(0,a.Z8)(`Primitive.${r}`),o=t.forwardRef((e,t)=>{let{asChild:a,...o}=e,i=a?s:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(i,{...o,ref:t})});return o.displayName=`Primitive.${r}`,{...e,[r]:o}},{})}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9276,8987,9168,5981,6292,8002],()=>s(56666));module.exports=t})();