(()=>{var e={};e.id=9671,e.ids=[9671],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8153:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l}),t(42931),t(56752),t(35866);var a=t(23191),n=t(88716),s=t(37922),i=t.n(s),o=t(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l=["",{children:["settings",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42931)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/profile/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/profile/page.tsx"],u="/settings/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/settings/profile/page",pathname:"/settings/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},58968:(e,r,t)=>{Promise.resolve().then(t.bind(t,73516))},86333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5932:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},31215:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},79635:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},90434:(e,r,t)=>{"use strict";t.d(r,{default:()=>n.a});var a=t(79404),n=t.n(a)},73516:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var a=t(10326),n=t(17577),s=t(35047),i=t(90434),o=t(68136),d=t(86333),l=t(79635),c=t(31215),u=t(5932),m=t(91664),p=t(41190),f=t(44794);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}();var h=t(85999);function g(){(0,s.useRouter)();let{user:e,loading:r,updateProfile:t,updateEmail:g}=(0,o.a)(),[x,v]=(0,n.useState)(""),[b,j]=(0,n.useState)(""),[y,N]=(0,n.useState)(""),[O,w]=(0,n.useState)(!1),[_,U]=(0,n.useState)(!1),D=async r=>{if(r.preventDefault(),e)try{w(!0),await t(x,b)}catch(e){}finally{w(!1)}},k=async r=>{if(r.preventDefault(),e){if(!y||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(y)){h.Am.error("Please enter a valid email address");return}if(y===e.email){h.Am.info("Email address is unchanged");return}try{U(!0),await g(y)}catch(r){N(e.email||"")}finally{U(!1)}}};return r?(0,a.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[a.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-500",children:"Loading profile..."})]}):(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[a.jsx(m.z,{variant:"outline",asChild:!0,className:"mb-6",children:(0,a.jsxs)(i.default,{href:"/settings",children:[a.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Back to Settings"]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Profile Settings",description:"Update your personal information",icon:a.jsx(l.Z,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Personal Information"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Update your name and personal details"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"first-name",children:"First Name"}),a.jsx(p.I,{id:"first-name",value:x,onChange:e=>v(e.target.value),placeholder:"First Name",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"last-name",children:"Last Name"}),a.jsx(p.I,{id:"last-name",value:b,onChange:e=>j(e.target.value),placeholder:"Last Name",required:!0})]})]}),a.jsx("div",{className:"flex justify-end",children:a.jsx(m.z,{type:"submit",disabled:O,children:O?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.Z,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})})]})})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[a.jsx(u.Z,{className:"h-5 w-5 text-primary mr-2"}),"Email Address"]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Update your email address"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(f._,{htmlFor:"email",children:"Email Address"}),a.jsx(p.I,{id:"email",type:"email",value:y,onChange:e=>N(e.target.value),placeholder:"<EMAIL>",required:!0}),a.jsx("p",{className:"text-xs text-gray-500",children:"Changing your email will require verification. We'll send a confirmation link to your new email address."})]}),a.jsx("div",{className:"flex justify-end",children:a.jsx(m.z,{type:"submit",disabled:_,children:_?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"}),"Sending Verification..."]}):"Update Email"})})]})})]})]})]})}!function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()},91664:(e,r,t)=>{"use strict";t.d(r,{z:()=>l});var a=t(10326),n=t(17577),s=t(34214),i=t(79360),o=t(51223);let d=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...i},l)=>{let c=n?s.g7:"button";return a.jsx(c,{className:(0,o.cn)(d({variant:r,size:t,className:e})),ref:l,...i})});l.displayName="Button"},41190:(e,r,t)=>{"use strict";t.d(r,{I:()=>i});var a=t(10326),n=t(17577),s=t(51223);let i=n.forwardRef(({className:e,type:r,...t},n)=>a.jsx("input",{type:r,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t}));i.displayName="Input"},44794:(e,r,t)=>{"use strict";t.d(r,{_:()=>l});var a=t(10326),n=t(17577),s=t(34478),i=t(79360),o=t(51223);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=n.forwardRef(({className:e,...r},t)=>a.jsx(s.Root,{ref:t,className:(0,o.cn)(d(),e),...r}));l.displayName=s.Root.displayName},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var a=t(41135),n=t(31009);function s(...e){return(0,n.m6)((0,a.W)(e))}},42931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/settings/profile/page.tsx#default`)},34478:(e,r,t)=>{"use strict";t.d(r,{Root:()=>o});var a=t(17577),n=t(45226),s=t(10326),i=a.forwardRef((e,r)=>(0,s.jsx)(n.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i},45226:(e,r,t)=>{"use strict";t.d(r,{WV:()=>i});var a=t(17577);t(60962);var n=t(34214),s=t(10326),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,n.Z8)(`Primitive.${r}`),i=a.forwardRef((e,a)=>{let{asChild:n,...i}=e,o=n?t:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o,{...i,ref:a})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9276,8987,9168,5981,8002],()=>t(8153));module.exports=a})();