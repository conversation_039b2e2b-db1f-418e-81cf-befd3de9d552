(()=>{var e={};e.id=3670,e.ids=[3670,2777],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},76415:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l}),r(86891),r(56752),r(35866);var t=r(23191),i=r(88716),a=r(37922),n=r.n(a),o=r(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let l=["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86891)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/debug/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/debug/page.tsx"],u="/debug/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},30825:(e,s,r)=>{Promise.resolve().then(r.bind(r,20148))},86333:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},21405:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},90434:(e,s,r)=>{"use strict";r.d(s,{default:()=>i.a});var t=r(79404),i=r.n(t)},20148:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(10326),i=r(17577),a=r(68136);r(2777);var n=r(91664),o=r(90434),d=r(35047),l=r(86333),c=r(21405);function u(){let{user:e,session:s}=(0,a.a)();(0,d.useRouter)();let[r,u]=(0,i.useState)(null),[h,x]=(0,i.useState)(null),[p,m]=(0,i.useState)(!0),[g,b]=(0,i.useState)(null),[f,v]=(0,i.useState)(!1);return t.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-8",children:(0,t.jsxs)(o.default,{href:"/",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[t.jsx(l.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),t.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Authentication Debug Page"}),p?t.jsx("div",{className:"bg-white p-8 rounded-xl shadow-md",children:t.jsx("p",{className:"text-gray-500",children:"Loading authentication data..."})}):g?(0,t.jsxs)("div",{className:"bg-red-50 p-8 rounded-xl shadow-md border border-red-200",children:[t.jsx("h2",{className:"text-xl font-bold text-red-700 mb-4",children:"Error"}),t.jsx("p",{className:"text-red-600",children:g})]}):(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-md",children:[t.jsx("h2",{className:"text-xl font-bold mb-4",children:"Auth Context"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold",children:"User:"}),t.jsx("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm",children:e?JSON.stringify(e,null,2):"No user in context"})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold",children:"Session:"}),t.jsx("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm",children:s?JSON.stringify(s,null,2):"No session in context"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-md",children:[t.jsx("h2",{className:"text-xl font-bold mb-4",children:"Direct Supabase Data"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold",children:"Session:"}),t.jsx("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm",children:r?JSON.stringify(r,null,2):"No session from Supabase"})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold",children:"Profile:"}),t.jsx("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm",children:h?JSON.stringify(h,null,2):"No profile found"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-md",children:[t.jsx("h2",{className:"text-xl font-bold mb-4",children:"Actions"}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{children:t.jsx(o.default,{href:"/dashboard",children:t.jsx(n.z,{children:"Go to Dashboard"})})}),t.jsx("div",{children:t.jsx(o.default,{href:"/login",children:t.jsx(n.z,{variant:"outline",children:"Go to Login"})})}),(0,t.jsxs)("div",{className:"pt-4 border-t border-gray-200 mt-4",children:[t.jsx("h3",{className:"font-semibold mb-2",children:"Manual Redirect"}),t.jsx(n.z,{variant:"secondary",onClick:()=>{v(!0),setTimeout(()=>{window.location.href="/dashboard"},500)},disabled:f,children:f?(0,t.jsxs)(t.Fragment,{children:[t.jsx(c.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Redirecting..."]}):"Force Redirect to Dashboard"})]}),t.jsx("div",{children:t.jsx(n.z,{variant:"outline",onClick:()=>{window.location.reload()},children:"Refresh Page"})})]})]})]})]})})}},91664:(e,s,r)=>{"use strict";r.d(s,{z:()=>l});var t=r(10326),i=r(17577),a=r(34214),n=r(79360),o=r(51223);let d=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef(({className:e,variant:s,size:r,asChild:i=!1,...n},l)=>{let c=i?a.g7:"button";return t.jsx(c,{className:(0,o.cn)(d({variant:s,size:r,className:e})),ref:l,...n})});l.displayName="Button"},2777:(e,s,r)=>{"use strict";r.d(s,{supabase:()=>o});var t=r(56292);let i="https://ccwvtcudztphwwzzgwvg.supabase.co",a=null,n=null,o=(0,t.eI)(i,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(n)return;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";n=(0,t.eI)(i,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>a});var t=r(41135),i=r(31009);function a(...e){return(0,i.m6)((0,t.W)(e))}},86891:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/debug/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[9276,8987,9168,5981,6292,8002],()=>r(76415));module.exports=t})();