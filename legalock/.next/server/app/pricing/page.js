(()=>{var e={};e.id=9979,e.ids=[9979],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},21764:e=>{"use strict";e.exports=require("util")},75900:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u}),r(59594),r(56752),r(35866);var o=r(23191),n=r(88716),i=r(37922),a=r.n(i),s=r(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u=["",{children:["pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59594)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/pricing/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/pricing/page.tsx"],d="/pricing/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/pricing/page",pathname:"/pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},93104:(e,t,r)=>{Promise.resolve().then(r.bind(r,1696))},44432:(e,t,r)=>{"use strict";var o=r(49759),n=r(66802),i=r(77336),a=r(61596);e.exports=a||o.call(i,n)},66802:e=>{"use strict";e.exports=Function.prototype.apply},77336:e=>{"use strict";e.exports=Function.prototype.call},36892:(e,t,r)=>{"use strict";var o=r(49759),n=r(12823),i=r(77336),a=r(44432);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},61596:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},1996:(e,t,r)=>{"use strict";var o=r(88197),n=r(36892),i=n([o("%String.prototype.indexOf%")]);e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n([r]):r}},37186:(e,t,r)=>{"use strict";var o,n=r(36892),i=r(22924);try{o=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!o&&i&&i(Object.prototype,"__proto__"),s=Object,l=s.getPrototypeOf;e.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof l&&function(e){return l(null==e?e:s(e))}},5702:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},71073:e=>{"use strict";e.exports=EvalError},88170:e=>{"use strict";e.exports=Error},45553:e=>{"use strict";e.exports=RangeError},66861:e=>{"use strict";e.exports=ReferenceError},37450:e=>{"use strict";e.exports=SyntaxError},12823:e=>{"use strict";e.exports=TypeError},64606:e=>{"use strict";e.exports=URIError},87713:e=>{"use strict";e.exports=Object},14930:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],d=0;d<u;d++)c[d]="$"+d;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var h=function(){};h.prototype=s.prototype,a.prototype=new h,h.prototype=null}return a}},49759:(e,t,r)=>{"use strict";var o=r(14930);e.exports=Function.prototype.bind||o},88197:(e,t,r)=>{"use strict";var o,n=r(87713),i=r(88170),a=r(71073),s=r(45553),l=r(66861),u=r(37450),c=r(12823),d=r(64606),h=r(55135),p=r(13084),m=r(63626),f=r(61794),y=r(41887),v=r(99953),P=r(99944),g=Function,T=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},_=r(22924),E=r(5702),b=function(){throw new c},S=_?function(){try{return arguments.callee,b}catch(e){try{return _(arguments,"callee").get}catch(e){return b}}}():b,x=r(86514)(),O=r(58644),w=r(32230),A=r(21475),C=r(66802),R=r(77336),j={},G="undefined"!=typeof Uint8Array&&O?O(Uint8Array):o,k={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":x&&O?O([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":j,"%AsyncGenerator%":j,"%AsyncGeneratorFunction%":j,"%AsyncIteratorPrototype%":j,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":i,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":j,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":x&&O?O(O([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&x&&O?O(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":_,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":s,"%ReferenceError%":l,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&x&&O?O(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":x&&O?O(""[Symbol.iterator]()):o,"%Symbol%":x?Symbol:o,"%SyntaxError%":u,"%ThrowTypeError%":S,"%TypedArray%":G,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":d,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":R,"%Function.prototype.apply%":C,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":w,"%Math.abs%":h,"%Math.floor%":p,"%Math.max%":m,"%Math.min%":f,"%Math.pow%":y,"%Math.round%":v,"%Math.sign%":P,"%Reflect.getPrototypeOf%":A};if(O)try{null.error}catch(e){var I=O(O(e));k["%Error.prototype%"]=I}var N=function e(t){var r;if("%AsyncFunction%"===t)r=T("async function () {}");else if("%GeneratorFunction%"===t)r=T("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=T("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&O&&(r=O(n.prototype))}return k[t]=r,r},D={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=r(49759),U=r(7238),F=M.call(R,Array.prototype.concat),q=M.call(C,Array.prototype.splice),L=M.call(R,String.prototype.replace),H=M.call(R,String.prototype.slice),z=M.call(R,RegExp.prototype.exec),$=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,B=/\\(\\)?/g,W=function(e){var t=H(e,0,1),r=H(e,-1);if("%"===t&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new u("invalid intrinsic syntax, expected opening `%`");var o=[];return L(e,$,function(e,t,r,n){o[o.length]=r?L(n,B,"$1"):t||e}),o},K=function(e,t){var r,o=e;if(U(D,o)&&(o="%"+(r=D[o])[0]+"%"),U(k,o)){var n=k[o];if(n===j&&(n=N(o)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new u("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===z(/^%?[^%]*%?$/,e))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=W(e),o=r.length>0?r[0]:"",n=K("%"+o+"%",t),i=n.name,a=n.value,s=!1,l=n.alias;l&&(o=l[0],q(r,F([0,1],l)));for(var d=1,h=!0;d<r.length;d+=1){var p=r[d],m=H(p,0,1),f=H(p,-1);if(('"'===m||"'"===m||"`"===m||'"'===f||"'"===f||"`"===f)&&m!==f)throw new u("property names with quotes must have matching quotes");if("constructor"!==p&&h||(s=!0),o+="."+p,U(k,i="%"+o+"%"))a=k[i];else if(null!=a){if(!(p in a)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(_&&d+1>=r.length){var y=_(a,p);a=(h=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[p]}else h=U(a,p),a=a[p];h&&!s&&(k[i]=a)}}return a}},32230:(e,t,r)=>{"use strict";var o=r(87713);e.exports=o.getPrototypeOf||null},21475:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},58644:(e,t,r)=>{"use strict";var o=r(21475),n=r(32230),i=r(37186);e.exports=o?function(e){return o(e)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return n(e)}:i?function(e){return i(e)}:null},53534:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},22924:(e,t,r)=>{"use strict";var o=r(53534);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},86514:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(26799);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},26799:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},7238:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,i=r(49759);e.exports=i.call(o,n)},32933:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});let o=(0,r(62881).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},77506:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});let o=(0,r(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55135:e=>{"use strict";e.exports=Math.abs},13084:e=>{"use strict";e.exports=Math.floor},1049:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},63626:e=>{"use strict";e.exports=Math.max},61794:e=>{"use strict";e.exports=Math.min},41887:e=>{"use strict";e.exports=Math.pow},99953:e=>{"use strict";e.exports=Math.round},99944:(e,t,r)=>{"use strict";var o=r(1049);e.exports=function(e){return o(e)||0===e?e:e<0?-1:1}},50662:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,h="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,m=Boolean.prototype.valueOf,f=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,E=RegExp.prototype.test,b=Array.prototype.concat,S=Array.prototype.join,x=Array.prototype.slice,O=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,R="function"==typeof Symbol&&"object"==typeof Symbol.iterator,j="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===R?"object":"symbol")?Symbol.toStringTag:null,G=Object.prototype.propertyIsEnumerable,k=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function I(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-O(-e):O(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var N=r(63324),D=N.custom,M=$(D)?D:null,U={__proto__:null,double:'"',single:"'"},F={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function q(e,t,r){var o=U[r.quoteStyle||t];return o+e+o}function L(e){return!j||!("object"==typeof e&&(j in e||void 0!==e[j]))}function H(e){return"[object Array]"===K(e)&&L(e)}function z(e){return"[object RegExp]"===K(e)&&L(e)}function $(e){if(R)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!C)return!1;try{return C.call(e),!0}catch(e){}return!1}e.exports=function e(t,r,o,n){var s=r||{};if(W(s,"quoteStyle")&&!W(U,s.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(W(s,"maxStringLength")&&("number"==typeof s.maxStringLength?s.maxStringLength<0&&s.maxStringLength!==1/0:null!==s.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=!W(s,"customInspect")||s.customInspect;if("boolean"!=typeof l&&"symbol"!==l)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(W(s,"indent")&&null!==s.indent&&"	"!==s.indent&&!(parseInt(s.indent,10)===s.indent&&s.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(W(s,"numericSeparator")&&"boolean"!=typeof s.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var f=s.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+"... "+o+" more character"+(o>1?"s":"")}var n=F[r.quoteStyle||"single"];return n.lastIndex=0,q(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,J),"single",r)}(t,s);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var T=String(t);return f?I(t,T):T}if("bigint"==typeof t){var E=String(t)+"n";return f?I(t,E):E}var O=void 0===s.depth?5:s.depth;if(void 0===o&&(o=0),o>=O&&O>0&&"object"==typeof t)return H(t)?"[Array]":"[Object]";var A=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=S.call(Array(e.indent+1)," ")}return{base:r,prev:S.call(Array(t+1),r)}}(s,o);if(void 0===n)n=[];else if(V(n,t)>=0)return"[Circular]";function D(t,r,i){if(r&&(n=x.call(n)).push(r),i){var a={depth:s.depth};return W(s,"quoteStyle")&&(a.quoteStyle=s.quoteStyle),e(t,a,o+1,n)}return e(t,s,o+1,n)}if("function"==typeof t&&!z(t)){var B=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),et=ee(t,D);return"[Function"+(B?": "+B:" (anonymous)")+"]"+(et.length>0?" { "+S.call(et,", ")+" }":"")}if($(t)){var er=R?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):C.call(t);return"object"!=typeof t||R?er:Q(er)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var eo,en="<"+_.call(String(t.nodeName)),ei=t.attributes||[],ea=0;ea<ei.length;ea++)en+=" "+ei[ea].name+"="+q((eo=ei[ea].value,g.call(String(eo),/"/g,"&quot;")),"double",s);return en+=">",t.childNodes&&t.childNodes.length&&(en+="..."),en+="</"+_.call(String(t.nodeName))+">"}if(H(t)){if(0===t.length)return"[]";var es=ee(t,D);return A&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(es)?"["+Z(es,A)+"]":"[ "+S.call(es,", ")+" ]"}if("[object Error]"===K(t)&&L(t)){var el=ee(t,D);return"cause"in Error.prototype||!("cause"in t)||G.call(t,"cause")?0===el.length?"["+String(t)+"]":"{ ["+String(t)+"] "+S.call(el,", ")+" }":"{ ["+String(t)+"] "+S.call(b.call("[cause]: "+D(t.cause),el),", ")+" }"}if("object"==typeof t&&l){if(M&&"function"==typeof t[M]&&N)return N(t,{depth:O-o});if("symbol"!==l&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var eu=[];return a&&a.call(t,function(e,r){eu.push(D(r,t,!0)+" => "+D(e,t))}),Y("Map",i.call(t),eu,A)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ec=[];return c&&c.call(t,function(e){ec.push(D(e,t))}),Y("Set",u.call(t),ec,A)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{h.call(e,h)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return X("WeakMap");if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{h.call(e,h);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return X("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return X("WeakRef");if("[object Number]"===K(t)&&L(t))return Q(D(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return Q(D(w.call(t)));if("[object Boolean]"===K(t)&&L(t))return Q(m.call(t));if("[object String]"===K(t)&&L(t))return Q(D(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||"undefined"!=typeof global&&t===global)return"{ [object globalThis] }";if(!("[object Date]"===K(t)&&L(t))&&!z(t)){var ed=ee(t,D),eh=k?k(t)===Object.prototype:t instanceof Object||t.constructor===Object,ep=t instanceof Object?"":"null prototype",em=!eh&&j&&Object(t)===t&&j in t?P.call(K(t),8,-1):ep?"Object":"",ef=(eh||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(em||ep?"["+S.call(b.call([],em||[],ep||[]),": ")+"] ":"");return 0===ed.length?ef+"{}":A?ef+"{"+Z(ed,A)+"}":ef+"{ "+S.call(ed,", ")+" }"}return String(t)};var B=Object.prototype.hasOwnProperty||function(e){return e in this};function W(e,t){return B.call(e,t)}function K(e){return f.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function J(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+T.call(t.toString(16))}function Q(e){return"Object("+e+")"}function X(e){return e+" { ? }"}function Y(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):S.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+S.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r,o=H(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=W(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(R){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)W(e,l)&&(!o||String(Number(l))!==l||!(l<e.length))&&(R&&r["$"+l]instanceof Symbol||(E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof A)for(var u=0;u<a.length;u++)G.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},63324:(e,t,r)=>{e.exports=r(21764).inspect},62399:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:o.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},12594:(e,t,r)=>{"use strict";var o=r(78950),n=r(49292),i=r(62399);e.exports={formats:i,parse:n,stringify:o}},49292:(e,t,r)=>{"use strict";var o=r(87251),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},s=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,t.throwOnLimitExceeded?u+1:u);if(t.throwOnLimitExceeded&&c.length>u)throw RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var d=-1,h=t.charset;if(t.charsetSentinel)for(p=0;p<c.length;++p)0===c[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[p]?h="utf-8":"utf8=%26%2310003%3B"===c[p]&&(h="iso-8859-1"),d=p,p=c.length);for(p=0;p<c.length;++p)if(p!==d){var p,m,f,y=c[p],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(m=t.decoder(y,a.decoder,h,"key"),f=t.strictNullHandling?null:""):(m=t.decoder(y.slice(0,P),a.decoder,h,"key"),f=o.maybeMap(s(y.slice(P+1),t,i(r[m])?r[m].length:0),function(e){return t.decoder(e,a.decoder,h,"value")})),f&&t.interpretNumericEntities&&"iso-8859-1"===h&&(f=String(f).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(f=i(f)?[f]:f);var g=n.call(r,m);g&&"combine"===t.duplicates?r[m]=o.combine(r[m],f):g&&"last"!==t.duplicates||(r[m]=f)}return r},u=function(e,t,r,n){var i=0;if(e.length>0&&"[]"===e[e.length-1]){var a=e.slice(0,-1).join("");i=Array.isArray(t)&&t[a]?t[a].length:0}for(var l=n?t:s(t,r,i),u=e.length-1;u>=0;--u){var c,d=e[u];if("[]"===d&&r.parseArrays)c=r.allowEmptyArrays&&(""===l||r.strictNullHandling&&null===l)?[]:o.combine([],l);else{c=r.plainObjects?{__proto__:null}:{};var h="["===d.charAt(0)&&"]"===d.charAt(d.length-1)?d.slice(1,-1):d,p=r.decodeDotInKeys?h.replace(/%2E/g,"."):h,m=parseInt(p,10);r.parseArrays||""!==p?!isNaN(m)&&d!==p&&String(m)===p&&m>=0&&r.parseArrays&&m<=r.arrayLimit?(c=[])[m]=l:"__proto__"!==p&&(c[p]=l):c={0:l}}l=c}return l},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var d=0;r.depth>0&&null!==(s=a.exec(i))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},d=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}};e.exports=function(e,t){var r=d(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],h=c(u,n[u],r,"string"==typeof e);i=o.merge(i,h,r)}return!0===r.allowSparse?i:o.compact(i)}},78950:(e,t,r)=>{"use strict";var o=r(49989),n=r(87251),i=r(62399),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},d=Date.prototype.toISOString,h=i.default,p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:h,formatter:i.formatters[h],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},m={},f=function e(t,r,i,a,s,u,d,h,f,y,v,P,g,T,_,E,b,S){for(var x,O,w=t,A=S,C=0,R=!1;void 0!==(A=A.get(m))&&!R;){var j=A.get(t);if(C+=1,void 0!==j){if(j===C)throw RangeError("Cyclic object value");R=!0}void 0===A.get(m)&&(C=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return f&&!E?f(r,p.encoder,b,"key",T):r;w=""}if("string"==typeof(x=w)||"number"==typeof x||"boolean"==typeof x||"symbol"==typeof x||"bigint"==typeof x||n.isBuffer(w))return f?[_(E?r:f(r,p.encoder,b,"key",T))+"="+_(f(w,p.encoder,b,"value",T))]:[_(r)+"="+_(String(w))];var G=[];if(void 0===w)return G;if("comma"===i&&l(w))E&&f&&(w=n.maybeMap(w,f)),O=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))O=y;else{var k=Object.keys(w);O=v?k.sort(v):k}var I=h?String(r).replace(/\./g,"%2E"):String(r),N=a&&l(w)&&1===w.length?I+"[]":I;if(s&&l(w)&&0===w.length)return N+"[]";for(var D=0;D<O.length;++D){var M=O[D],U="object"==typeof M&&M&&void 0!==M.value?M.value:w[M];if(!d||null!==U){var F=P&&h?String(M).replace(/\./g,"%2E"):String(M),q=l(w)?"function"==typeof i?i(N,F):N:N+(P?"."+F:"["+F+"]");S.set(t,C);var L=o();L.set(m,S),c(G,e(U,q,i,a,s,u,d,h,"comma"===i&&E&&l(w)?null:f,y,v,P,g,T,_,E,b,L))}}return G},y=function(e){if(!e)return p;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=p.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||p.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}};e.exports=function(e,t){var r,n=e,i=y(t);"function"==typeof i.filter?n=(0,i.filter)("",n):l(i.filter)&&(r=i.filter);var a=[];if("object"!=typeof n||null===n)return"";var u=s[i.arrayFormat],d="comma"===u&&i.commaRoundTrip;r||(r=Object.keys(n)),i.sort&&r.sort(i.sort);for(var h=o(),p=0;p<r.length;++p){var m=r[p],v=n[m];i.skipNulls&&null===v||c(a,f(v,m,u,d,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,h))}var P=a.join(i.delimiter),g=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),P.length>0?g+P:""}},87251:(e,t,r)=>{"use strict";var o=r(62399),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,d=[],h=0;h<c.length;++h){var p=c.charCodeAt(h);if(45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||i===o.RFC1738&&(40===p||41===p)){d[d.length]=c.charAt(h);continue}if(p<128){d[d.length]=a[p];continue}if(p<2048){d[d.length]=a[192|p>>6]+a[128|63&p];continue}if(p<55296||p>=57344){d[d.length]=a[224|p>>12]+a[128|p>>6&63]+a[128|63&p];continue}h+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(h)),d[d.length]=a[240|p>>18]+a[128|p>>12&63]+a[128|p>>6&63]+a[128|63&p]}l+=d.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},4337:(e,t,r)=>{"use strict";var o=r(50662),n=r(12823),i=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o},a=function(e,t){if(e){var r=i(e,t);return r&&r.value}},s=function(e,t,r){var o=i(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}},l=function(e,t){if(e)return i(e,t,!0)};e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new n("Side channel does not contain "+o(e))},delete:function(t){var r=e&&e.next,o=l(e,t);return o&&r&&r===o&&(e=void 0),!!o},get:function(t){return a(e,t)},has:function(t){var r;return!!(r=e)&&!!i(r,t)},set:function(t,r){e||(e={next:void 0}),s(e,t,r)}};return t}},58722:(e,t,r)=>{"use strict";var o=r(88197),n=r(1996),i=r(50662),a=r(12823),s=o("%Map%",!0),l=n("Map.prototype.get",!0),u=n("Map.prototype.set",!0),c=n("Map.prototype.has",!0),d=n("Map.prototype.delete",!0),h=n("Map.prototype.size",!0);e.exports=!!s&&function(){var e,t={assert:function(e){if(!t.has(e))throw new a("Side channel does not contain "+i(e))},delete:function(t){if(e){var r=d(e,t);return 0===h(e)&&(e=void 0),r}return!1},get:function(t){if(e)return l(e,t)},has:function(t){return!!e&&c(e,t)},set:function(t,r){e||(e=new s),u(e,t,r)}};return t}},87452:(e,t,r)=>{"use strict";var o=r(88197),n=r(1996),i=r(50662),a=r(58722),s=r(12823),l=o("%WeakMap%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),h=n("WeakMap.prototype.delete",!0);e.exports=l?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new s("Side channel does not contain "+i(e))},delete:function(r){if(l&&r&&("object"==typeof r||"function"==typeof r)){if(e)return h(e,r)}else if(a&&t)return t.delete(r);return!1},get:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?u(e,r):t&&t.get(r)},has:function(r){return l&&r&&("object"==typeof r||"function"==typeof r)&&e?d(e,r):!!t&&t.has(r)},set:function(r,o){l&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new l),c(e,r,o)):a&&(t||(t=a()),t.set(r,o))}};return r}:a},49989:(e,t,r)=>{"use strict";var o=r(12823),n=r(50662),i=r(4337),a=r(58722),s=r(87452)||a||i;e.exports=function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+n(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=s()),e.set(t,r)}};return t}},1696:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>nY});var o={};r.r(o),r.d(o,{StripeAPIError:()=>q,StripeAuthenticationError:()=>L,StripeCardError:()=>U,StripeConnectionError:()=>$,StripeError:()=>M,StripeIdempotencyError:()=>W,StripeInvalidGrantError:()=>K,StripeInvalidRequestError:()=>F,StripePermissionError:()=>H,StripeRateLimitError:()=>z,StripeSignatureVerificationError:()=>B,StripeUnknownError:()=>V,TemporarySessionExpiredError:()=>J,generateV1Error:()=>N,generateV2Error:()=>D});var n={};r.r(n),r.d(n,{Account:()=>rB,AccountLinks:()=>rK,AccountSessions:()=>rJ,Accounts:()=>rB,ApplePayDomains:()=>rX,ApplicationFees:()=>rZ,Apps:()=>nS,Balance:()=>r0,BalanceTransactions:()=>r8,Billing:()=>nx,BillingPortal:()=>nO,Charges:()=>r3,Checkout:()=>nw,Climate:()=>nA,ConfirmationTokens:()=>r5,CountrySpecs:()=>r7,Coupons:()=>ot,CreditNotes:()=>oo,CustomerSessions:()=>oi,Customers:()=>os,Disputes:()=>ou,Entitlements:()=>nC,EphemeralKeys:()=>od,Events:()=>op,ExchangeRates:()=>of,FileLinks:()=>ov,Files:()=>oT,FinancialConnections:()=>nR,Forwarding:()=>nj,Identity:()=>nG,InvoiceItems:()=>oE,InvoicePayments:()=>oS,InvoiceRenderingTemplates:()=>oO,Invoices:()=>oA,Issuing:()=>nk,Mandates:()=>oR,OAuth:()=>ok,PaymentIntents:()=>oN,PaymentLinks:()=>oM,PaymentMethodConfigurations:()=>oF,PaymentMethodDomains:()=>oL,PaymentMethods:()=>oz,Payouts:()=>oB,Plans:()=>oK,Prices:()=>oJ,Products:()=>oX,PromotionCodes:()=>oZ,Quotes:()=>o0,Radar:()=>nI,Refunds:()=>o8,Reporting:()=>nN,Reviews:()=>o3,SetupAttempts:()=>o5,SetupIntents:()=>o7,ShippingRates:()=>nt,Sigma:()=>nD,Sources:()=>no,SubscriptionItems:()=>ni,SubscriptionSchedules:()=>ns,Subscriptions:()=>nu,Tax:()=>nM,TaxCodes:()=>nd,TaxIds:()=>np,TaxRates:()=>nf,Terminal:()=>nU,TestHelpers:()=>nF,Tokens:()=>nv,Topups:()=>ng,Transfers:()=>n_,Treasury:()=>nq,V2:()=>nL,WebhookEndpoints:()=>nb});var i=r(10326),a=r(17577),s=r(35047),l=r(68136),u=r(87423),c=r(32933),d=r(77506),h=r(91664),p=r(84770),m=r(17702);class f{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}computeSHA256Async(e){throw Error("computeSHA256 not implemented.")}}class y extends Error{}class v extends f{computeHMACSignature(e,t){return p.createHmac("sha256",t).update(e,"utf8").digest("hex")}async computeHMACSignatureAsync(e,t){return await this.computeHMACSignature(e,t)}async computeSHA256Async(e){return new Uint8Array(await p.createHash("sha256").update(e).digest())}}var P=r(32615),g=r.t(P,2),T=r(35240),_=r.t(T,2);class E{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(E.TIMEOUT_ERROR_CODE);return e.code=E.TIMEOUT_ERROR_CODE,e}}E.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],E.TIMEOUT_ERROR_CODE="ETIMEDOUT";class b{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}let S=P||g,x=T||_,O=new S.Agent({keepAlive:!0}),w=new x.Agent({keepAlive:!0});class A extends E{constructor(e){super(),this._agent=e}getClientName(){return"node"}makeRequest(e,t,r,o,n,i,a,s){let l="http"===a,u=this._agent;return u||(u=l?O:w),new Promise((a,c)=>{let d=(l?S:x).request({host:e,port:t,path:r,method:o,agent:u,headers:n,ciphers:"DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5"});d.setTimeout(s,()=>{d.destroy(E.makeTimeoutError())}),d.on("response",e=>{a(new C(e))}),d.on("error",e=>{c(e)}),d.once("socket",e=>{e.connecting?e.once(l?"connect":"secureConnect",()=>{d.write(i),d.end()}):(d.write(i),d.end())})})}}class C extends b{constructor(e){super(e.statusCode,e.headers||{}),this._res=e}getRawResponse(){return this._res}toStream(e){return this._res.once("end",()=>e()),this._res}toJSON(){return new Promise((e,t)=>{let r="";this._res.setEncoding("utf8"),this._res.on("data",e=>{r+=e}),this._res.once("end",()=>{try{e(JSON.parse(r))}catch(e){t(e)}})})}}class R extends E{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=R.makeFetchWithAbortTimeout(e):this._fetchFn=R.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,o)=>{let n;let i=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(E.makeTimeoutError())},o)});return Promise.race([e(t,r),i]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,o)=>{let n=new AbortController,i=setTimeout(()=>{i=null,n.abort(E.makeTimeoutError())},o);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw E.makeTimeoutError();throw e}finally{i&&clearTimeout(i)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,o,n,i,a,s){let l=new URL(r,`${"http"===a?"http":"https"}://${e}`);l.port=t;let u="POST"==o||"PUT"==o||"PATCH"==o;return new j(await this._fetchFn(l.toString(),{method:o,headers:n,body:i||(u?"":void 0)},s))}}class j extends b{constructor(e){super(e.status,j._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class G extends f{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new y("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=k[n[e]];return i.join("")}async computeSHA256Async(e){return new Uint8Array(await this.subtleCrypto.digest("SHA-256",e))}}let k=Array(256);for(let e=0;e<k.length;e++)k[e]=e.toString(16).padStart(2,"0");class I{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new R(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new G(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}let N=e=>{switch(e.type){case"card_error":return new U(e);case"invalid_request_error":return new F(e);case"api_error":return new q(e);case"authentication_error":return new L(e);case"rate_limit_error":return new z(e);case"idempotency_error":return new W(e);case"invalid_grant":return new K(e);default:return new V(e)}},D=e=>"temporary_session_expired"===e.type?new J(e):"invalid_fields"===e.code?new F(e):N(e);class M extends Error{constructor(e={},t=null){super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.userMessage=e.user_message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}M.generate=N;class U extends M{constructor(e={}){super(e,"StripeCardError")}}class F extends M{constructor(e={}){super(e,"StripeInvalidRequestError")}}class q extends M{constructor(e={}){super(e,"StripeAPIError")}}class L extends M{constructor(e={}){super(e,"StripeAuthenticationError")}}class H extends M{constructor(e={}){super(e,"StripePermissionError")}}class z extends M{constructor(e={}){super(e,"StripeRateLimitError")}}class $ extends M{constructor(e={}){super(e,"StripeConnectionError")}}class B extends M{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class W extends M{constructor(e={}){super(e,"StripeIdempotencyError")}}class K extends M{constructor(e={}){super(e,"StripeInvalidGrantError")}}class V extends M{constructor(e={}){super(e,"StripeUnknownError")}}class J extends M{constructor(e={}){super(e,"TemporarySessionExpiredError")}}var Q=r(12594);let X=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host","authenticator","stripeContext","additionalHeaders"];function Y(e){return e&&"object"==typeof e&&X.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function Z(e,t){return Q.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString(),arrayFormat:"v2"==t?"repeat":"indices"}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let ee=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function et(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!Y(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>X.includes(e));return r.length>0&&r.length!==t.length&&ei(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function er(e){let t={host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.authenticator=es(e.pop());else if(Y(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!X.includes(e));if(o.length&&ei(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.authenticator=es(r.apiKey)),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.stripeContext){if(t.headers["Stripe-Account"])throw Error("Can't specify both stripeAccount and stripeContext.");t.headers["Stripe-Context"]=r.stripeContext}if(r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host),r.authenticator){if(r.apiKey)throw Error("Can't specify both apiKey and authenticator.");if("function"!=typeof r.authenticator)throw Error("The authenticator must be a function receiving a request as the first parameter.");t.authenticator=r.authenticator}r.additionalHeaders&&(t.headers=r.additionalHeaders)}}return t}function eo(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function en(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function ei(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function ea(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}function es(e){let t=t=>(t.headers.Authorization="Bearer "+e,Promise.resolve());return t._apiKey=e,t}function el(e,t){return this[e]instanceof Date?Math.floor(this[e].getTime()/1e3).toString():t}function eu(e){return e&&e.startsWith("/v2")?"v2":"v1"}var ec=r(61282);class ed extends M{}class eh extends I{constructor(){super(),this._exec=ec.exec,this._UNAME_CACHE=null}uuid4(){return p.randomUUID?p.randomUUID():super.uuid4()}getUname(){return this._UNAME_CACHE||(this._UNAME_CACHE=new Promise((e,t)=>{try{this._exec("uname -a",(t,r)=>{if(t)return e(null);e(r)})}catch(t){e(null)}})),this._UNAME_CACHE}secureCompare(e,t){if(!e||!t)throw Error("secureCompare must receive two arguments");if(e.length!==t.length)return!1;if(p.timingSafeEqual){let r=new TextEncoder,o=r.encode(e),n=r.encode(t);return p.timingSafeEqual(o,n)}return super.secureCompare(e,t)}createEmitter(){return new m.EventEmitter}tryBufferData(e){if(!(e.file.data instanceof m.EventEmitter))return Promise.resolve(e);let t=[];return new Promise((r,o)=>{e.file.data.on("data",e=>{t.push(e)}).once("end",()=>{let o=Object.assign({},e);o.file.data=function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}(t),r(o)}).on("error",e=>{o(new ed({message:"An error occurred while attempting to process the file for upload.",detail:e}))})})}createNodeHttpClient(e){return new A(e)}createDefaultHttpClient(){return new A}createNodeCryptoProvider(){return new v}createDefaultCryptoProvider(){return this.createNodeCryptoProvider()}}class ep{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return eo({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=o.toStream(()=>{let r=this._makeResponseEvent(e,o.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r,o){return n=>{let i=n.getHeaders(),a=this._getRequestId(i),s=n.getStatusCode(),l=this._makeResponseEvent(e,s,i);this._stripe._emitter.emit("response",l),n.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=i,e.error.statusCode=s,e.error.requestId=a,401===s?new L(e.error):403===s?new H(e.error):429===s?new z(e.error):"v2"===t?D(e.error):N(e.error);return e},e=>{throw new q({message:"Invalid JSON received from the Stripe API",exception:e,requestId:i["request-id"]})}).then(e=>{this._recordRequestMetrics(a,l.elapsed,r);let t=n.getRawResponse();this._addHeadersDirectlyToObject(t,i),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:t}),o(null,e)},e=>o(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&E.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(2,e-1),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t,r){let o=this._getMaxNetworkRetries(t),n=()=>`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`;if("v2"===r){if("POST"===e||"DELETE"===e)return n()}else if("v1"===r&&"POST"===e&&o>0)return n();return null}_makeHeaders({contentType:e,contentLength:t,apiVersion:r,clientUserAgent:o,method:n,userSuppliedHeaders:i,userSuppliedSettings:a,stripeAccount:s,stripeContext:l,apiMode:u}){let c={Accept:"application/json","Content-Type":e,"User-Agent":this._getUserAgentString(u),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":s,"Stripe-Context":l,"Idempotency-Key":this._defaultIdempotencyKey(n,a,u)},d="POST"==n||"PUT"==n||"PATCH"==n;return(d||t)&&(d||ei(`${n} method had non-zero contentLength but no payload is expected for this verb`),c["Content-Length"]=t),Object.assign(eo(c),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(e){let t=this._stripe.getConstant("PACKAGE_VERSION"),r=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/${e} NodeBindings/${t} ${r}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e){if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)ei("Request metrics buffer is full, dropping telemetry message.");else{let o={request_id:e,request_duration_ms:t};r&&r.length>0&&(o.usage=r),this._stripe._prevRequestMetrics.push(o)}}}_rawRequest(e,t,r,o){return new Promise((n,i)=>{let a;try{let n=e.toUpperCase();if("POST"!==n&&r&&0!==Object.keys(r).length)throw Error("rawRequest only supports params on POST requests. Please pass null and add your parameters to path.");let i=[].slice.call([r,o]),s=et(i),l=Object.assign({},s),u=er(i),c=u.headers,d=u.authenticator;a={requestMethod:n,requestPath:t,bodyData:l,queryData:{},authenticator:d,headers:c,host:null,streaming:!1,settings:{},usage:["raw_request"]}}catch(e){i(e);return}let{headers:s,settings:l}=a,u=a.authenticator;this._request(a.requestMethod,a.host,t,a.bodyData,u,{headers:s,settings:l,streaming:a.streaming},a.usage,function(e,t){e?i(e):n(t)})})}_request(e,t,r,o,n,i,a=[],s,l=null){var u;let c;n=null!==(u=null!=n?n:this._stripe._authenticator)&&void 0!==u?u:null;let d=eu(r),h=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),p=(o,l,u)=>{let m=i.settings&&i.settings.timeout&&Number.isInteger(i.settings.timeout)&&i.settings.timeout>=0?i.settings.timeout:this._stripe.getApiField("timeout"),f={host:t||this._stripe.getApiField("host"),port:this._stripe.getApiField("port"),path:r,method:e,headers:Object.assign({},l),body:c,protocol:this._stripe.getApiField("protocol")};n(f).then(()=>{let t=this._stripe.getApiField("httpClient").makeRequest(f.host,f.port,f.path,f.method,f.headers,f.body,f.protocol,m),n=Date.now(),c=eo({api_version:o,account:l["Stripe-Account"],idempotency_key:l["Idempotency-Key"],method:e,path:r,request_start_time:n}),y=u||0,v=this._getMaxNetworkRetries(i.settings||{});this._stripe._emitter.emit("request",c),t.then(e=>ep._shouldRetry(e,y,v)?h(p,o,l,y,e.getHeaders()["retry-after"]):i.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(c,a,s)(e):this._jsonResponseHandler(c,d,a,s)(e)).catch(e=>ep._shouldRetry(null,y,v,e)?h(p,o,l,y,null):s(new $({message:e.code&&e.code===E.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${m}ms)`:ep._generateConnectionErrorMessage(y),detail:e})))}).catch(e=>{throw new M({message:"Unable to authenticate the request",exception:e})})},m=(t,r)=>{if(t)return s(t);c=r,this._stripe.getClientUserAgent(t=>{let r=this._stripe.getApiField("version"),o=this._makeHeaders({contentType:"v2"==d?"application/json":"application/x-www-form-urlencoded",contentLength:c.length,apiVersion:r,clientUserAgent:t,method:e,userSuppliedHeaders:i.headers,userSuppliedSettings:i.settings,stripeAccount:"v2"==d?null:this._stripe.getApiField("stripeAccount"),stripeContext:"v2"==d?this._stripe.getApiField("stripeContext"):null,apiMode:d});p(r,o,0)})};if(l)l(e,o,i.headers,m);else{let e;m(null,"v2"==d?o?JSON.stringify(o,el):"":Z(o||{},d))}}}class em{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=eT(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class ef extends em{getNextPage(e){let t=eT(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class ey extends em{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}class ev{constructor(e,t,r,o){this.currentPageIterator=(async()=>(await e).data[Symbol.iterator]())(),this.nextPageUrl=(async()=>(await e).next_page_url||null)(),this.requestArgs=t,this.spec=r,this.stripeResource=o}async turnPage(){let e=await this.nextPageUrl;if(!e)return null;this.spec.fullPath=e;let t=await this.stripeResource._makeRequest([],this.spec,{});return this.nextPageUrl=Promise.resolve(t.next_page_url),this.currentPageIterator=Promise.resolve(t.data[Symbol.iterator]()),this.currentPageIterator}async next(){{let e=(await this.currentPageIterator).next();if(!e.done)return{done:!1,value:e.value}}let e=await this.turnPage();if(!e)return{done:!0,value:void 0};let t=e.next();return t.done?{done:!0,value:void 0}:{done:!1,value:t.value}}}let eP=(e,t,r,o)=>{let n=eu(r.fullPath||r.path);return"v2"!==n&&"search"===r.methodType?eg(new ey(o,t,r,e)):"v2"!==n&&"list"===r.methodType?eg(new ef(o,t,r,e)):"v2"===n&&"list"===r.methodType?eg(new ev(o,t,r,e)):null},eg=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),o=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return en(new Promise((e,o)=>{t().then(function o(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?o({done:!0,value:void 0}):t().then(o))}).catch(o)}),o)}),o={autoPagingEach:r,autoPagingToArray:function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return en(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>o};return o};function eT(e){return!!et([].slice.call(e)).ending_before}function e_(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=ee(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=ee(this.path),this.initialize(...arguments)}function eE(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function eb(e,t){return function(e){return new eE(e,t)}}e_.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},e_.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=en(this._makeRequest(t,e,{}),r);return Object.assign(o,eP(this,t,e,o)),o}},e_.MAX_BUFFERED_REQUEST_METRICS=100,e_.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){var o;let n=(t.method||"GET").toUpperCase(),i=t.usage||[],a=t.urlParams||[],s=t.encode||(e=>e),l=!!t.fullPath,u=ee(l?t.fullPath:t.path||""),c=l?t.fullPath:this.createResourcePathWithSymbols(t.path),d=[].slice.call(e),h=a.reduce((e,t)=>{let r=d.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${n} ${c}\`)`);return e[t]=r,e},{}),p=s(Object.assign({},et(d),r)),m=er(d),f=m.host||t.host,y=!!t.streaming;if(d.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${d}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${n} \`${c}\`)`);let v=l?u(h):this.createFullPath(u,h),P=Object.assign(m.headers,t.headers);t.validator&&t.validator(p,{headers:P});let g="GET"===t.method||"DELETE"===t.method;return{requestMethod:n,requestPath:v,bodyData:g?null:p,queryData:g?p:{},authenticator:null!==(o=m.authenticator)&&void 0!==o?o:null,headers:P,host:null!=f?f:null,streaming:y,settings:m.settings,usage:i}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",Z(a.queryData,eu(a.requestPath))].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.authenticator,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let eS=e_.method,ex=e_.extend({retrieve:eS({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:eS({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:eS({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:eS({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:eS({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:eS({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:eS({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),eO=e_.method,ew=e_.extend({retrieve:eO({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:eO({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),eA=e_.method,eC=e_.extend({create:eA({method:"POST",fullPath:"/v1/billing/alerts"}),retrieve:eA({method:"GET",fullPath:"/v1/billing/alerts/{id}"}),list:eA({method:"GET",fullPath:"/v1/billing/alerts",methodType:"list"}),activate:eA({method:"POST",fullPath:"/v1/billing/alerts/{id}/activate"}),archive:eA({method:"POST",fullPath:"/v1/billing/alerts/{id}/archive"}),deactivate:eA({method:"POST",fullPath:"/v1/billing/alerts/{id}/deactivate"})}),eR=e_.method,ej=e_.extend({create:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),finalizeAmount:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount"}),increment:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),respond:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond"}),reverse:eR({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),eG=e_.method,ek=e_.extend({retrieve:eG({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:eG({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:eG({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:eG({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:eG({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),eI=e_.method,eN=e_.extend({create:eI({method:"POST",fullPath:"/v1/tax/calculations"}),retrieve:eI({method:"GET",fullPath:"/v1/tax/calculations/{calculation}"}),listLineItems:eI({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),eD=e_.method,eM=e_.extend({create:eD({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:eD({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:eD({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:eD({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eU=e_.method,eF=e_.extend({deliverCard:eU({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eU({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eU({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eU({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"}),submitCard:eU({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/submit"})}),eq=e_.method,eL=e_.extend({create:eq({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:eq({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:eq({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:eq({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),eH=e_.method,ez=e_.extend({create:eH({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:eH({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:eH({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:eH({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),e$=e_.method,eB=e_.extend({create:e$({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:e$({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:e$({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:e$({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:e$({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),eW=e_.method,eK=e_.extend({create:eW({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),eV=e_.method,eJ=e_.extend({create:eV({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),eQ=e_.method,eX=e_.extend({retrieve:eQ({method:"GET",fullPath:"/v1/billing/credit_balance_summary"})}),eY=e_.method,eZ=e_.extend({retrieve:eY({method:"GET",fullPath:"/v1/billing/credit_balance_transactions/{id}"}),list:eY({method:"GET",fullPath:"/v1/billing/credit_balance_transactions",methodType:"list"})}),e1=e_.method,e0=e_.extend({create:e1({method:"POST",fullPath:"/v1/billing/credit_grants"}),retrieve:e1({method:"GET",fullPath:"/v1/billing/credit_grants/{id}"}),update:e1({method:"POST",fullPath:"/v1/billing/credit_grants/{id}"}),list:e1({method:"GET",fullPath:"/v1/billing/credit_grants",methodType:"list"}),expire:e1({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/expire"}),voidGrant:e1({method:"POST",fullPath:"/v1/billing/credit_grants/{id}/void"})}),e2=e_.method,e8=e_.extend({create:e2({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:e2({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:e2({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),e6=e_.method,e3=e_.extend({fundCashBalance:e6({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),e9=e_.method,e5=e_.extend({create:e9({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:e9({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:e9({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),e4=e_.method,e7=e_.extend({create:e4({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:e4({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:e4({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:e4({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:e4({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),te=e_.method,tt=e_.extend({retrieve:te({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:te({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),tr=e_.method,to=e_.extend({create:tr({method:"POST",fullPath:"/v2/core/event_destinations"}),retrieve:tr({method:"GET",fullPath:"/v2/core/event_destinations/{id}"}),update:tr({method:"POST",fullPath:"/v2/core/event_destinations/{id}"}),list:tr({method:"GET",fullPath:"/v2/core/event_destinations",methodType:"list"}),del:tr({method:"DELETE",fullPath:"/v2/core/event_destinations/{id}"}),disable:tr({method:"POST",fullPath:"/v2/core/event_destinations/{id}/disable"}),enable:tr({method:"POST",fullPath:"/v2/core/event_destinations/{id}/enable"}),ping:tr({method:"POST",fullPath:"/v2/core/event_destinations/{id}/ping"})}),tn=e_.method,ti=e_.extend({retrieve(...e){return tn({method:"GET",fullPath:"/v2/core/events/{id}",transformResponseData:e=>this.addFetchRelatedObjectIfNeeded(e)}).apply(this,e)},list(...e){return tn({method:"GET",fullPath:"/v2/core/events",methodType:"list",transformResponseData:e=>Object.assign(Object.assign({},e),{data:e.data.map(this.addFetchRelatedObjectIfNeeded.bind(this))})}).apply(this,e)},addFetchRelatedObjectIfNeeded(e){return e.related_object&&e.related_object.url?Object.assign(Object.assign({},e),{fetchRelatedObject:()=>tn({method:"GET",fullPath:e.related_object.url}).apply(this,[{stripeAccount:e.context}])}):e}}),ta=e_.method,ts=e_.extend({create:ta({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:ta({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:ta({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:ta({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),tl=e_.method,tu=e_.extend({create:tl({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:tl({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:tl({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:tl({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),close:tl({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/close"}),retrieveFeatures:tl({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:tl({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),tc=e_.method,td=e_.extend({fail:tc({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:tc({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:tc({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),th=e_.method,tp=e_.extend({create:th({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:th({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:th({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:th({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),tm=e_.method,tf=e_.extend({create:tm({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:tm({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:tm({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:tm({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:tm({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),ty=e_.method,tv=e_.extend({create:ty({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),tP=e_.method,tg=e_.extend({create:tP({method:"POST",fullPath:"/v2/billing/meter_event_adjustments"})}),tT=e_.method,t_=e_.extend({create:tT({method:"POST",fullPath:"/v2/billing/meter_event_session"})}),tE=e_.method,tb=e_.extend({create:tE({method:"POST",fullPath:"/v2/billing/meter_event_stream",host:"meter-events.stripe.com"})}),tS=e_.method,tx=e_.extend({create:tS({method:"POST",fullPath:"/v1/billing/meter_events"})}),tO=e_.method,tw=e_.extend({create:tO({method:"POST",fullPath:"/v2/billing/meter_events"})}),tA=e_.method,tC=e_.extend({create:tA({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:tA({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:tA({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:tA({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:tA({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:tA({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:tA({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),tR=e_.method,tj=e_.extend({create:tR({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:tR({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:tR({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:tR({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:tR({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),tG=e_.method,tk=e_.extend({update:tG({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}"}),fail:tG({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:tG({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:tG({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),tI=e_.method,tN=e_.extend({create:tI({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:tI({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:tI({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:tI({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),tD=e_.method,tM=e_.extend({update:tD({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}"}),fail:tD({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:tD({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:tD({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),tU=e_.method,tF=e_.extend({create:tU({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:tU({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:tU({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:tU({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),tq=e_.method,tL=e_.extend({activate:tq({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:tq({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:tq({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),tH=e_.method,tz=e_.extend({create:tH({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:tH({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:tH({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:tH({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),t$=e_.method,tB=e_.extend({retrieve:t$({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:t$({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),tW=e_.method,tK=e_.extend({retrieve:tW({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:tW({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),tV=e_.method,tJ=e_.extend({presentPaymentMethod:tV({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),tQ=e_.method,tX=e_.extend({create:tQ({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:tQ({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:tQ({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:tQ({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:tQ({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:tQ({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:tQ({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:tQ({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:tQ({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:tQ({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),tY=e_.method,tZ=e_.extend({create:tY({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),t1=e_.method,t0=e_.extend({retrieve:t1({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:t1({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),t2=e_.method,t8=e_.extend({create:t2({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),t6=e_.method,t3=e_.extend({retrieve:t6({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:t6({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),t9=e_.method,t5=e_.extend({expire:t9({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),t4=e_.method,t7=e_.extend({create:t4({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:t4({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:t4({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:t4({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),re=e_.method,rt=e_.extend({create:re({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:re({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:re({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),rr=e_.method,ro=e_.extend({retrieve:rr({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:rr({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),rn=e_.method,ri=e_.extend({create:rn({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:rn({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:rn({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),ra=e_.method,rs=e_.extend({retrieve:ra({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:ra({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),rl=e_.method,ru=e_.extend({create:rl({method:"POST",fullPath:"/v1/apps/secrets"}),list:rl({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:rl({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:rl({method:"GET",fullPath:"/v1/apps/secrets/find"})}),rc=e_.method,rd=e_.extend({create:rc({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),rh=e_.method,rp=e_.extend({create:rh({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:rh({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),update:rh({method:"POST",fullPath:"/v1/checkout/sessions/{session}"}),list:rh({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:rh({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:rh({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),rm=e_.method,rf=e_.extend({create:rm({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:rm({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),ry=e_.method,rv=e_.extend({retrieve:ry({method:"GET",fullPath:"/v1/tax/settings"}),update:ry({method:"POST",fullPath:"/v1/tax/settings"})}),rP=e_.method,rg=e_.extend({retrieve:rP({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:rP({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),rT=e_.method,r_=e_.extend({create:rT({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:rT({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:rT({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:rT({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:rT({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),rE=e_.method,rb=e_.extend({retrieve:rE({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:rE({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:rE({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),rS=e_.method,rx=e_.extend({retrieve:rS({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:rS({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),rO=e_.method,rw=e_.extend({createForceCapture:rO({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:rO({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:rO({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),rA=e_.method,rC=e_.extend({retrieve:rA({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:rA({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),rR=e_.method,rj=e_.extend({retrieve:rR({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:rR({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:rR({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),rG=e_.method,rk=e_.extend({retrieve:rG({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:rG({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:rG({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:rG({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),rI=e_.method,rN=e_.extend({retrieve:rI({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:rI({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),rD=e_.method,rM=e_.extend({create:rD({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:rD({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:rD({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:rD({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),rU=e_.method,rF=e_.extend({create:rU({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:rU({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:rU({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:rU({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:rU({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),rq=e_.method,rL=e_.extend({retrieve:rq({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:rq({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),rH=e_.method,rz=e_.extend({create:rH({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:rH({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:rH({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:rH({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:rH({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:rH({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),r$=e_.method,rB=e_.extend({create:r$({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?r$({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),r$({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:r$({method:"POST",fullPath:"/v1/accounts/{account}"}),list:r$({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:r$({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:r$({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:r$({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:r$({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:r$({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:r$({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:r$({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:r$({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:r$({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:r$({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:r$({method:"GET",fullPath:"/v1/account"}),retrieveCapability:r$({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:r$({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:r$({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:r$({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:r$({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:r$({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),rW=e_.method,rK=e_.extend({create:rW({method:"POST",fullPath:"/v1/account_links"})}),rV=e_.method,rJ=e_.extend({create:rV({method:"POST",fullPath:"/v1/account_sessions"})}),rQ=e_.method,rX=e_.extend({create:rQ({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:rQ({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:rQ({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:rQ({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),rY=e_.method,rZ=e_.extend({retrieve:rY({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:rY({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:rY({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:rY({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:rY({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:rY({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),r1=e_.method,r0=e_.extend({retrieve:r1({method:"GET",fullPath:"/v1/balance"})}),r2=e_.method,r8=e_.extend({retrieve:r2({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:r2({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),r6=e_.method,r3=e_.extend({create:r6({method:"POST",fullPath:"/v1/charges"}),retrieve:r6({method:"GET",fullPath:"/v1/charges/{charge}"}),update:r6({method:"POST",fullPath:"/v1/charges/{charge}"}),list:r6({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:r6({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:r6({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),r9=e_.method,r5=e_.extend({retrieve:r9({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),r4=e_.method,r7=e_.extend({retrieve:r4({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:r4({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),oe=e_.method,ot=e_.extend({create:oe({method:"POST",fullPath:"/v1/coupons"}),retrieve:oe({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:oe({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:oe({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:oe({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),or=e_.method,oo=e_.extend({create:or({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:or({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:or({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:or({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:or({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:or({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:or({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:or({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),on=e_.method,oi=e_.extend({create:on({method:"POST",fullPath:"/v1/customer_sessions"})}),oa=e_.method,os=e_.extend({create:oa({method:"POST",fullPath:"/v1/customers"}),retrieve:oa({method:"GET",fullPath:"/v1/customers/{customer}"}),update:oa({method:"POST",fullPath:"/v1/customers/{customer}"}),list:oa({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:oa({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:oa({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:oa({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:oa({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:oa({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:oa({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:oa({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:oa({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:oa({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:oa({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:oa({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:oa({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:oa({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:oa({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:oa({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:oa({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:oa({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:oa({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:oa({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:oa({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:oa({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:oa({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:oa({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:oa({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),ol=e_.method,ou=e_.extend({retrieve:ol({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:ol({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:ol({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:ol({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),oc=e_.method,od=e_.extend({create:oc({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:oc({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),oh=e_.method,op=e_.extend({retrieve:oh({method:"GET",fullPath:"/v1/events/{id}"}),list:oh({method:"GET",fullPath:"/v1/events",methodType:"list"})}),om=e_.method,of=e_.extend({retrieve:om({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:om({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),oy=e_.method,ov=e_.extend({create:oy({method:"POST",fullPath:"/v1/file_links"}),retrieve:oy({method:"GET",fullPath:"/v1/file_links/{link}"}),update:oy({method:"POST",fullPath:"/v1/file_links/{link}"}),list:oy({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),oP=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.entries(e).forEach(([e,n])=>{let i=o?`${o}[${e}]`:e;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(n)){if(!(n instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(n,"data"))return r(n,i);t[i]=n}else t[i]=String(n)})};return r(e,null),t}(t);for(let e in u){if(!Object.prototype.hasOwnProperty.call(u,e))continue;let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},og=e_.method,oT=e_.extend({create:og({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:og({method:"GET",fullPath:"/v1/files/{file}"}),list:og({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,Z(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,oP(e,t,r))).catch(e=>o(e,null))}}),o_=e_.method,oE=e_.extend({create:o_({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:o_({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:o_({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:o_({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:o_({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),ob=e_.method,oS=e_.extend({retrieve:ob({method:"GET",fullPath:"/v1/invoice_payments/{invoice_payment}"}),list:ob({method:"GET",fullPath:"/v1/invoice_payments",methodType:"list"})}),ox=e_.method,oO=e_.extend({retrieve:ox({method:"GET",fullPath:"/v1/invoice_rendering_templates/{template}"}),list:ox({method:"GET",fullPath:"/v1/invoice_rendering_templates",methodType:"list"}),archive:ox({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/archive"}),unarchive:ox({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/unarchive"})}),ow=e_.method,oA=e_.extend({create:ow({method:"POST",fullPath:"/v1/invoices"}),retrieve:ow({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:ow({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:ow({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:ow({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),addLines:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/add_lines"}),createPreview:ow({method:"POST",fullPath:"/v1/invoices/create_preview"}),finalizeInvoice:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:ow({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),markUncollectible:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),removeLines:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/remove_lines"}),search:ow({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLines:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/update_lines"}),updateLineItem:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:ow({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),oC=e_.method,oR=e_.extend({retrieve:oC({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),oj=e_.method,oG="connect.stripe.com",ok=e_.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${oG}/${r}?${Z(e)}`},token:oj({method:"POST",path:"oauth/token",host:oG}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),oj({method:"POST",path:"oauth/deauthorize",host:oG}).apply(this,[e,...t])}}),oI=e_.method,oN=e_.extend({create:oI({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:oI({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:oI({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:oI({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:oI({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),oD=e_.method,oM=e_.extend({create:oD({method:"POST",fullPath:"/v1/payment_links"}),retrieve:oD({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:oD({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:oD({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:oD({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),oU=e_.method,oF=e_.extend({create:oU({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:oU({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:oU({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:oU({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),oq=e_.method,oL=e_.extend({create:oq({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:oq({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:oq({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:oq({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:oq({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),oH=e_.method,oz=e_.extend({create:oH({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:oH({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:oH({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:oH({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:oH({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:oH({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),o$=e_.method,oB=e_.extend({create:o$({method:"POST",fullPath:"/v1/payouts"}),retrieve:o$({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:o$({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:o$({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:o$({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:o$({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),oW=e_.method,oK=e_.extend({create:oW({method:"POST",fullPath:"/v1/plans"}),retrieve:oW({method:"GET",fullPath:"/v1/plans/{plan}"}),update:oW({method:"POST",fullPath:"/v1/plans/{plan}"}),list:oW({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:oW({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),oV=e_.method,oJ=e_.extend({create:oV({method:"POST",fullPath:"/v1/prices"}),retrieve:oV({method:"GET",fullPath:"/v1/prices/{price}"}),update:oV({method:"POST",fullPath:"/v1/prices/{price}"}),list:oV({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:oV({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),oQ=e_.method,oX=e_.extend({create:oQ({method:"POST",fullPath:"/v1/products"}),retrieve:oQ({method:"GET",fullPath:"/v1/products/{id}"}),update:oQ({method:"POST",fullPath:"/v1/products/{id}"}),list:oQ({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:oQ({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:oQ({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:oQ({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:oQ({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:oQ({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:oQ({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),oY=e_.method,oZ=e_.extend({create:oY({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:oY({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:oY({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:oY({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),o1=e_.method,o0=e_.extend({create:o1({method:"POST",fullPath:"/v1/quotes"}),retrieve:o1({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:o1({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:o1({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:o1({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:o1({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:o1({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:o1({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:o1({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:o1({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),o2=e_.method,o8=e_.extend({create:o2({method:"POST",fullPath:"/v1/refunds"}),retrieve:o2({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:o2({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:o2({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:o2({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),o6=e_.method,o3=e_.extend({retrieve:o6({method:"GET",fullPath:"/v1/reviews/{review}"}),list:o6({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:o6({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),o9=e_.method,o5=e_.extend({list:o9({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),o4=e_.method,o7=e_.extend({create:o4({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:o4({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:o4({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:o4({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:o4({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:o4({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:o4({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),ne=e_.method,nt=e_.extend({create:ne({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:ne({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:ne({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:ne({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),nr=e_.method,no=e_.extend({create:nr({method:"POST",fullPath:"/v1/sources"}),retrieve:nr({method:"GET",fullPath:"/v1/sources/{source}"}),update:nr({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:nr({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:nr({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),nn=e_.method,ni=e_.extend({create:nn({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:nn({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:nn({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:nn({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:nn({method:"DELETE",fullPath:"/v1/subscription_items/{item}"})}),na=e_.method,ns=e_.extend({create:na({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:na({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:na({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:na({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:na({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:na({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),nl=e_.method,nu=e_.extend({create:nl({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:nl({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:nl({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:nl({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:nl({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:nl({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:nl({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:nl({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),nc=e_.method,nd=e_.extend({retrieve:nc({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:nc({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),nh=e_.method,np=e_.extend({create:nh({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:nh({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:nh({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:nh({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),nm=e_.method,nf=e_.extend({create:nm({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:nm({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:nm({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:nm({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),ny=e_.method,nv=e_.extend({create:ny({method:"POST",fullPath:"/v1/tokens"}),retrieve:ny({method:"GET",fullPath:"/v1/tokens/{token}"})}),nP=e_.method,ng=e_.extend({create:nP({method:"POST",fullPath:"/v1/topups"}),retrieve:nP({method:"GET",fullPath:"/v1/topups/{topup}"}),update:nP({method:"POST",fullPath:"/v1/topups/{topup}"}),list:nP({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:nP({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),nT=e_.method,n_=e_.extend({create:nT({method:"POST",fullPath:"/v1/transfers"}),retrieve:nT({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:nT({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:nT({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:nT({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:nT({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:nT({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:nT({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),nE=e_.method,nb=e_.extend({create:nE({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:nE({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:nE({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:nE({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:nE({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),nS=eb("apps",{Secrets:ru}),nx=eb("billing",{Alerts:eC,CreditBalanceSummary:eX,CreditBalanceTransactions:eZ,CreditGrants:e0,MeterEventAdjustments:tv,MeterEvents:tx,Meters:tC}),nO=eb("billingPortal",{Configurations:ez,Sessions:rd}),nw=eb("checkout",{Sessions:rp}),nA=eb("climate",{Orders:tj,Products:tK,Suppliers:rg}),nC=eb("entitlements",{ActiveEntitlements:ew,Features:ts}),nR=eb("financialConnections",{Accounts:ex,Sessions:rf,Transactions:rC}),nj=eb("forwarding",{Requests:ri}),nG=eb("identity",{VerificationReports:rL,VerificationSessions:rz}),nk=eb("issuing",{Authorizations:ek,Cardholders:eM,Cards:eL,Disputes:e7,PersonalizationDesigns:tz,PhysicalBundles:tB,Tokens:rb,Transactions:rj}),nI=eb("radar",{EarlyFraudWarnings:tt,ValueListItems:rM,ValueLists:rF}),nN=eb("reporting",{ReportRuns:rt,ReportTypes:ro}),nD=eb("sigma",{ScheduledQueryRuns:rs}),nM=eb("tax",{Calculations:eN,Registrations:t7,Settings:rv,Transactions:rk}),nU=eb("terminal",{Configurations:eB,ConnectionTokens:eJ,Locations:tf,Readers:tX}),nF=eb("testHelpers",{ConfirmationTokens:eK,Customers:e3,Refunds:t5,TestClocks:r_,Issuing:eb("issuing",{Authorizations:ej,Cards:eF,PersonalizationDesigns:tL,Transactions:rw}),Terminal:eb("terminal",{Readers:tJ}),Treasury:eb("treasury",{InboundTransfers:td,OutboundPayments:tk,OutboundTransfers:tM,ReceivedCredits:tZ,ReceivedDebits:t8})}),nq=eb("treasury",{CreditReversals:e8,DebitReversals:e5,FinancialAccounts:tu,InboundTransfers:tp,OutboundPayments:tN,OutboundTransfers:tF,ReceivedCredits:t0,ReceivedDebits:t3,TransactionEntries:rx,Transactions:rN}),nL=eb("v2",{Billing:eb("billing",{MeterEventAdjustments:tg,MeterEventSession:t_,MeterEventStream:tb,MeterEvents:tw}),Core:eb("core",{EventDestinations:to,Events:ti})}),nH="api.stripe.com",nz="/v1/",n$="2025-03-31.basil",nB=["name","version","url","partner_id"],nW=["authenticator","apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount","stripeContext"],nK=e=>new ep(e,e_.MAX_BUFFERED_REQUEST_METRICS);new(function(e,t=nK){function r(n,i={}){if(!(this instanceof r))return new r(n,i);let a=this._getPropsFromConfig(i);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=r.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let s=a.httpAgent||null;this._api={host:a.host||nH,port:a.port||"443",protocol:a.protocol||"https",basePath:nz,version:a.apiVersion||n$,timeout:ea("timeout",a.timeout,8e4),maxNetworkRetries:ea("maxNetworkRetries",a.maxNetworkRetries,2),agent:s,httpClient:a.httpClient||(s?this._platformFunctions.createNodeHttpClient(s):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null,stripeContext:a.stripeContext||null};let l=a.typescript||!1;l!==r.USER_AGENT.typescript&&(r.USER_AGENT.typescript=l),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setAuthenticator(n,a.authenticator),this.errors=o,this.webhooks=r.webhooks,this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=r.StripeResource}return r.PACKAGE_VERSION="18.0.0",r.USER_AGENT=Object.assign({bindings_version:r.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),r.StripeResource=e_,r.resources=n,r.HttpClient=E,r.HttpClientResponse=b,r.CryptoProvider=f,r.webhooks=function(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof y&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){let t=l(e),r=t.signature||t.cryptoProvider.computeHMACSignature(t.payloadString,t.secret);return t.generateHeaderString(r)},generateTestHeaderStringAsync:async function(e){let t=l(e),r=t.signature||await t.cryptoProvider.computeHMACSignatureAsync(t.payloadString,t.secret);return t.generateHeaderString(r)}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r),f=(l=l||s()).computeHMACSignature(o(d,h),r);return i(d,c,h,f,a,p,m,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r);l=l||s();let f=await l.computeHMACSignatureAsync(o(d,h),r);return i(d,c,h,f,a,p,m,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new B(t,e,{message:"No webhook payload was provided."});let o="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new B(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,s="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let o=t.split("=");return"t"===o[0]&&(e.timestamp=parseInt(o[1],10)),o[0]===r&&e.signatures.push(o[1]),e},{timestamp:-1,signatures:[]});if(!s||-1===s.timestamp)throw new B(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!s.signatures.length)throw new B(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:s,suspectPayloadType:o}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://docs.stripe.com/webhooks/signature",d=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new B(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+d});throw new B(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+d})}let h=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&h>i)throw new B(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}function l(e){if(!e)throw new M({message:"Options are required"});let t=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),o=e.scheme||r.EXPECTED_SCHEME,n=e.cryptoProvider||s(),i=`${t}.${e.payload}`;return Object.assign(Object.assign({},e),{timestamp:t,scheme:o,cryptoProvider:n,payloadString:i,generateHeaderString:e=>`t=${t},${o}=${e}`})}return t.signature=r,t}(e),r.errors=o,r.createNodeHttpClient=e.createNodeHttpClient,r.createFetchHttpClient=e.createFetchHttpClient,r.createNodeCryptoProvider=e.createNodeCryptoProvider,r.createSubtleCryptoProvider=e.createSubtleCryptoProvider,r.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,rawRequest(e,t,r,o){return this._requestSender._rawRequest(e,t,r,o)},_setAuthenticator(e,t){if(e&&t)throw Error("Can't specify both apiKey and authenticator");if(!e&&!t)throw Error("Neither apiKey nor config.authenticator provided");this._authenticator=e?es(e):t},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=nB.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return nH;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return nz;case"DEFAULT_API_VERSION":return n$;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 5;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return r[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=ea(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>5,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(r.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=encodeURIComponent(null!==(o=e[t])&&void 0!==o?o:"null"));n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this))},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!nW.includes(e)).length>0)throw Error(`Config object may only contain the following: ${nW.join(", ")}`);return e},parseThinEvent(e,t,r,o,n,i){return this.webhooks.constructEvent(e,t,r,o,n,i)}},r}(new eh))(process.env.STRIPE_SECRET_KEY||"",{apiVersion:"2023-10-16"});let nV={PREMIUM:process.env.STRIPE_PREMIUM_PLAN_ID||"price_premium"},nJ=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(e/100),nQ=e=>e===nV.PREMIUM?{name:"Legacy Preserver",price:2999,features:["All Essential Legacy features","Digital Vault for secure document storage","Time Capsule for future messages","Priority customer support"]}:{name:"Essential Legacy",price:0,features:["Will advisor","Asset inventory","Emergency contacts","Last wishes"]};var nX=r(85999);function nY(){let e=(0,s.useRouter)(),{user:t}=(0,l.a)(),{plan:r,isSubscribed:o,createCheckout:n}=(0,u.m)(),[p,m]=(0,a.useState)(!1),f=nQ("free"),y=nQ(nV.PREMIUM),v=async r=>{if(!t){nX.Am.info("Please sign in to subscribe"),e.push("/login?redirect=/pricing");return}m(!0);try{let e=await n("premium");e&&(window.location.href=e)}catch(e){console.error("Error creating checkout session:",e),nX.Am.error("Failed to create checkout session")}finally{m(!1)}};return(0,i.jsxs)("div",{className:"container mx-auto py-16 px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[i.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Simple, Transparent Pricing"}),i.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Choose the plan that works best for you and your family's legacy planning needs."})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-5xl mx-auto",children:[(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"relative overflow-hidden border-2 border-gray-200 transition-all duration-300 hover:shadow-md",children:[(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-8",children:[i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-2xl",children:"Essential Legacy"}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Basic features for everyone"}),(0,i.jsxs)("div",{className:"mt-4",children:[i.jsx("span",{className:"text-4xl font-bold",children:"Free"}),i.jsx("span",{className:"text-gray-500 ml-2",children:"forever"})]})]}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:i.jsx("ul",{className:"space-y-3",children:f.features.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-start",children:[i.jsx(c.Z,{className:"h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5"}),i.jsx("span",{children:e})]},t))})}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:i.jsx(h.z,{variant:"outline",className:"w-full",onClick:()=>e.push("/register"),disabled:!!t,children:t?"Current Plan":"Get Started"})})]}),(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"relative overflow-hidden border-2 border-primary transition-all duration-300 hover:shadow-md",children:[i.jsx("div",{className:"absolute top-0 right-0 bg-primary text-white px-3 py-1 text-sm font-medium",children:"Recommended"}),(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-8",children:[i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-2xl",children:"Legacy Preserver"}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Advanced features for complete peace of mind"}),(0,i.jsxs)("div",{className:"mt-4",children:[i.jsx("span",{className:"text-4xl font-bold",children:nJ(y.price)}),i.jsx("span",{className:"text-gray-500 ml-2",children:"/ year"})]})]}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:i.jsx("ul",{className:"space-y-3",children:y.features.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-start",children:[i.jsx(c.Z,{className:"h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5"}),i.jsx("span",{children:e})]},t))})}),i.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:i.jsx(h.z,{className:"w-full",onClick:()=>v(nV.PREMIUM),disabled:p||o&&"premium"===r,children:p?(0,i.jsxs)(i.Fragment,{children:[i.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):o&&"premium"===r?"Current Plan":"Subscribe"})})]})]}),(0,i.jsxs)("div",{className:"text-center mt-16 text-gray-600",children:[i.jsx("p",{className:"mb-2",children:"All plans include:"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[i.jsx("li",{children:"Secure data storage"}),i.jsx("li",{children:"Regular updates and improvements"}),i.jsx("li",{children:"Email support"})]})]})]})}!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>u});var o=r(10326),n=r(17577),i=r(34214),a=r(79360),s=r(51223);let l=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...a},u)=>{let c=n?i.g7:"button";return o.jsx(c,{className:(0,s.cn)(l({variant:t,size:r,className:e})),ref:u,...a})});u.displayName="Button"},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var o=r(41135),n=r(31009);function i(...e){return(0,n.m6)((0,o.W)(e))}},59594:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/pricing/page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,8987,5981,8002],()=>r(75900));module.exports=o})();