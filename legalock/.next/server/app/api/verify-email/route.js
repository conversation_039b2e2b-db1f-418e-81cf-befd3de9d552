"use strict";(()=>{var e={};e.id=3024,e.ids=[3024],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},28177:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(49303),i=t(88716),a=t(60670),n=t(87070),u=t(20344),p=t(71615);async function c(e){try{let{email:r}=await e.json();if(!r)return n.NextResponse.json({error:"Email is required"},{status:400});let t=(0,p.cookies)(),s=(0,u.createRouteHandlerClient)({cookies:()=>t}),{data:o,error:i}=await s.auth.admin.updateUserById(r,{email_confirmed:!0});if(i)return console.error("Error updating user metadata:",i),n.NextResponse.json({error:i.message},{status:500});return n.NextResponse.json({success:!0})}catch(e){return console.error("Unexpected error in verify-email:",e),n.NextResponse.json({error:e.message||"An error occurred"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/verify-email/route",pathname:"/api/verify-email",filename:"route",bundlePath:"app/api/verify-email/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/verify-email/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:x,serverHooks:m}=l,q="/api/verify-email/route";function f(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:x})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,135,958],()=>t(28177));module.exports=s})();