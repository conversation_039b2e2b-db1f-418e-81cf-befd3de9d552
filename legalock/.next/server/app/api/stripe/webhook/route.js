"use strict";(()=>{var e={};e.id=2757,e.ids=[2757],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},43225:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>f});var o={};t.r(o),t.d(o,{POST:()=>p});var i=t(49303),s=t(88716),n=t(60670),a=t(87070),l=t(46029),c=t(43478);let u=process.env.STRIPE_WEBHOOK_SECRET;async function p(e){let r;let t=await e.text(),o=e.headers.get("stripe-signature");try{r=l.ZP.webhooks.constructEvent(t,o,u)}catch(e){return console.error(`Webhook Error: ${e.message}`),a.NextResponse.json({error:`Webhook Error: ${e.message}`},{status:400})}switch(r.type){case"checkout.session.completed":let i=r.data.object,s=i.metadata.userId,n=i.customer;try{let{error:e}=await c.pR.from("profiles").update({stripe_customer_id:n,subscription_tier:"premium",subscription_status:"active",subscription_start_date:new Date().toISOString()}).eq("id",s);e&&console.error("Error updating user profile:",e)}catch(e){console.error("Error in checkout.session.completed handler:",e)}break;case"customer.subscription.updated":let p=r.data.object,d=p.customer;try{let{data:e,error:r}=await c.pR.from("profiles").select("id").eq("stripe_customer_id",d);if(r||!e||!e.length){console.error("Error finding user profile:",r);break}let t=e[0].id,{error:o}=await c.pR.from("profiles").update({subscription_status:p.status,subscription_end_date:p.cancel_at?new Date(1e3*p.cancel_at).toISOString():null}).eq("id",t);o&&console.error("Error updating subscription status:",o)}catch(e){console.error("Error in customer.subscription.updated handler:",e)}break;case"customer.subscription.deleted":let m=r.data.object.customer;try{let{data:e,error:r}=await c.pR.from("profiles").select("id").eq("stripe_customer_id",m);if(r||!e||!e.length){console.error("Error finding user profile:",r);break}let t=e[0].id,{error:o}=await c.pR.from("profiles").update({subscription_tier:"free",subscription_status:"inactive",subscription_end_date:new Date().toISOString()}).eq("id",t);o&&console.error("Error updating subscription status:",o)}catch(e){console.error("Error in customer.subscription.deleted handler:",e)}break;default:console.log(`Unhandled event type ${r.type}`)}return a.NextResponse.json({received:!0})}let d=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/stripe/webhook/route",pathname:"/api/stripe/webhook",filename:"route",bundlePath:"app/api/stripe/webhook/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/stripe/webhook/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:g}=d,h="/api/stripe/webhook/route";function x(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:f})}},79835:(e,r,t)=>{t.d(r,{C:()=>o});function o(e,r){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${r?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${r}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,r,t)=>{t.d(r,{CX:()=>_,GA:()=>w,Gv:()=>m,Lj:()=>k,M3:()=>E,SO:()=>v,SX:()=>p,VP:()=>h,Zy:()=>R,cJ:()=>q,ed:()=>b,pO:()=>S,pR:()=>u,r4:()=>y,xJ:()=>g,xv:()=>x,zk:()=>f});var o=t(84770),i=t.n(o),s=t(31518),n=t(2723),a=t(79835);let l=new n.R(process.env.RESEND_API_KEY),c=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,s.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",c,{auth:{autoRefreshToken:!1,persistSession:!1}});function p(){return Math.floor(1e5+9e5*Math.random()).toString()}function d(e){return new Promise((r,t)=>{let o=i().randomBytes(16).toString("hex");i().pbkdf2(e,o,1e4,64,"sha512",(e,i)=>{if(e){t(e);return}r(`10000:${o}:${i.toString("hex")}`)})})}function m(e,r){return new Promise((t,o)=>{let[s,n,a]=r.split(":"),l=parseInt(s);i().pbkdf2(e,n,l,64,"sha512",(e,r)=>{if(e){o(e);return}t(r.toString("hex")===a)})})}async function f(e,r){try{let{data:t,error:o}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,r)});if(o)return console.error("Error sending verification email:",o),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function g(e,r,t=24){let o=new Date;o.setHours(o.getHours()+t);let{data:i,error:s}=await u.from("verification_codes").insert({email:e,code:r,expires_at:o.toISOString()}).select();if(s)throw console.error("Error storing verification code:",s),Error("Failed to store verification code");return i}async function h(e,r){let{data:t,error:o}=await u.from("verification_codes").select("*").eq("email",e).eq("code",r).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(o)return console.error("Error verifying code:",o),!1;if(!t||0===t.length)return!1;let{error:i}=await u.from("verification_codes").update({used:!0}).eq("id",t[0].id);return i&&console.error("Error marking code as used:",i),!0}async function x(e,r,t,o){try{let{error:i}=await u.from("profiles").insert({id:e,email:r,first_name:t,last_name:o,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});i&&console.error("Error creating user profile:",i)}catch(e){console.error("Exception creating user profile:",e)}}async function y(e,r,t,o){let i=await d(r),{data:s,error:n}=await u.from("custom_users").insert({email:e,password_hash:i,first_name:t,last_name:o}).select();if(n)throw console.error("Error creating user:",n),Error("Failed to create user");return await x(s[0].id,e,t,o),s[0]}async function _(e){let{data:r,error:t}=await u.from("custom_users").select("*").eq("email",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by email:",t),Error("Failed to get user")}return r}async function w(e){let{data:r,error:t}=await u.from("custom_users").select("*").eq("id",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by ID:",t),Error("Failed to get user")}return r}async function b(e,r=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let t=i().randomBytes(32).toString("hex"),o=new Date;o.setDate(o.getDate()+r);let{data:s,error:n}=await u.from("user_sessions").insert({user_id:e,session_token:t,expires_at:o.toISOString()}).select();if(n)throw console.error("Error creating session:",n),Error("Failed to create session");return{sessionToken:t,expiresAt:o,userId:e}}async function E(e){try{let{data:r,error:t}=await u.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(t){if("PGRST116"===t.code)return null;return console.error("Error getting session by token:",t),null}return r}catch(e){return console.error("Exception getting session by token:",e),null}}async function v(e){let{error:r}=await u.from("user_sessions").delete().eq("session_token",e);if(r)throw console.error("Error deleting session:",r),Error("Failed to delete session");return!0}async function S(e){let{error:r}=await u.from("custom_users").update({email_verified:!0}).eq("email",e);if(r)throw console.error("Error marking email as verified:",r),Error("Failed to mark email as verified");return!0}async function k(e,r,t){let{error:o}=await u.from("custom_users").update({first_name:r,last_name:t}).eq("id",e);if(o)throw console.error("Error updating user profile:",o),Error("Failed to update user profile");return!0}async function q(e,r){let{error:t}=await u.from("custom_users").update({email:r,email_verified:!1}).eq("id",e);if(t)throw console.error("Error updating user email:",t),Error("Failed to update user email");return!0}async function R(e,r){let t=await d(r),{error:o}=await u.from("custom_users").update({password_hash:t}).eq("id",e);if(o)throw console.error("Error updating user password:",o),Error("Failed to update user password");return!0}},46029:(e,r,t)=>{t.d(r,{ZP:()=>o,dO:()=>i});let o=new(t(31059)).Z(process.env.STRIPE_SECRET_KEY||"",{apiVersion:"2023-10-16"}),i={PREMIUM:process.env.STRIPE_PREMIUM_PLAN_ID||"price_premium"}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,135,541],()=>t(43225));module.exports=o})();