"use strict";(()=>{var e={};e.id=5457,e.ids=[5457],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},87743:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>h,requestAsyncStorage:()=>f,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var o={};r.r(o),r.d(o,{POST:()=>d});var s=r(49303),n=r(88716),i=r(60670),a=r(87070),l=r(71615),c=r(46029),u=r(43478);async function d(e){try{let t=(0,l.cookies)(),r=t.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Authentication required"},{status:401});let o=await (0,u.M3)(r);if(!o)return a.NextResponse.json({error:"Invalid session"},{status:401});let s=await (0,u.GA)(o.user_id);if(!s)return a.NextResponse.json({error:"User not found"},{status:404});let{plan:n}=await e.json();if(!n||"premium"!==n)return a.NextResponse.json({error:"Invalid plan selected"},{status:400});let i=c.dO.PREMIUM,d=await c.ZP.checkout.sessions.create({payment_method_types:["card"],line_items:[{price:i,quantity:1}],mode:"subscription",success_url:`${e.headers.get("origin")}/dashboard?checkout=success`,cancel_url:`${e.headers.get("origin")}/pricing?checkout=canceled`,customer_email:s.email,client_reference_id:s.id,metadata:{userId:s.id}});return a.NextResponse.json({url:d.url})}catch(e){return console.error("Error creating checkout session:",e),a.NextResponse.json({error:e.message||"An error occurred while creating checkout session"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/stripe/checkout/route",pathname:"/api/stripe/checkout",filename:"route",bundlePath:"app/api/stripe/checkout/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/stripe/checkout/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:m}=p,y="/api/stripe/checkout/route";function h(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},71615:(e,t,r)=>{var o=r(88757);r.o(o,"cookies")&&r.d(t,{cookies:function(){return o.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return n}});let o=r(45869),s=r(6278);class n{get isEnabled(){return this._provider.isEnabled}enable(){let e=o.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=o.staticGenerationAsyncStorage.getStore();return e&&(0,s.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return f},headers:function(){return d}});let o=r(68996),s=r(53047),n=r(92044),i=r(72934),a=r(33085),l=r(6278),c=r(45869),u=r(54580);function d(){let e="headers",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,u.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.RequestCookiesAdapter.seal(new n.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,u.getExpectedRequestStore)(e),s=i.actionAsyncStorage.getStore();return(null==s?void 0:s.isAction)||(null==s?void 0:s.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,u.getExpectedRequestStore)("draftMode");return new a.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return n},ReadonlyHeadersError:function(){return s}});let o=r(38238);class s extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new s}}class n extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,s){if("symbol"==typeof r)return o.ReflectAdapter.get(t,r,s);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==i)return o.ReflectAdapter.get(t,i,s)},set(t,r,s,n){if("symbol"==typeof r)return o.ReflectAdapter.set(t,r,s,n);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return o.ReflectAdapter.set(t,a??r,s,n)},has(t,r){if("symbol"==typeof r)return o.ReflectAdapter.has(t,r);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0!==n&&o.ReflectAdapter.has(t,n)},deleteProperty(t,r){if("symbol"==typeof r)return o.ReflectAdapter.deleteProperty(t,r);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);return void 0===n||o.ReflectAdapter.deleteProperty(t,n)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return s.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new n(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,o]of this.entries())e.call(t,o,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return i},RequestCookiesAdapter:function(){return a},appendMutableCookies:function(){return u},getModifiedCookieValues:function(){return c}});let o=r(92044),s=r(38238),n=r(45869);class i extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new i}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return i.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function c(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=c(t);if(0===r.length)return!1;let s=new o.ResponseCookies(e),n=s.getAll();for(let e of r)s.set(e);for(let e of n)s.set(e);return!0}class d{static wrap(e,t){let r=new o.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],a=new Set,c=()=>{let e=n.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of i){let r=new o.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return i;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{c()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{c()}};default:return s.ReflectAdapter.get(e,t,r)}}})}}},79835:(e,t,r)=>{r.d(t,{C:()=>o});function o(e,t){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${t?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${t}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,t,r)=>{r.d(t,{CX:()=>w,GA:()=>b,Gv:()=>f,Lj:()=>E,M3:()=>v,SO:()=>k,SX:()=>d,VP:()=>y,Zy:()=>R,cJ:()=>A,ed:()=>_,pO:()=>S,pR:()=>u,r4:()=>x,xJ:()=>m,xv:()=>h,zk:()=>g});var o=r(84770),s=r.n(o),n=r(31518),i=r(2723),a=r(79835);let l=new i.R(process.env.RESEND_API_KEY),c=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,n.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",c,{auth:{autoRefreshToken:!1,persistSession:!1}});function d(){return Math.floor(1e5+9e5*Math.random()).toString()}function p(e){return new Promise((t,r)=>{let o=s().randomBytes(16).toString("hex");s().pbkdf2(e,o,1e4,64,"sha512",(e,s)=>{if(e){r(e);return}t(`10000:${o}:${s.toString("hex")}`)})})}function f(e,t){return new Promise((r,o)=>{let[n,i,a]=t.split(":"),l=parseInt(n);s().pbkdf2(e,i,l,64,"sha512",(e,t)=>{if(e){o(e);return}r(t.toString("hex")===a)})})}async function g(e,t){try{let{data:r,error:o}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,t)});if(o)return console.error("Error sending verification email:",o),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function m(e,t,r=24){let o=new Date;o.setHours(o.getHours()+r);let{data:s,error:n}=await u.from("verification_codes").insert({email:e,code:t,expires_at:o.toISOString()}).select();if(n)throw console.error("Error storing verification code:",n),Error("Failed to store verification code");return s}async function y(e,t){let{data:r,error:o}=await u.from("verification_codes").select("*").eq("email",e).eq("code",t).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(o)return console.error("Error verifying code:",o),!1;if(!r||0===r.length)return!1;let{error:s}=await u.from("verification_codes").update({used:!0}).eq("id",r[0].id);return s&&console.error("Error marking code as used:",s),!0}async function h(e,t,r,o){try{let{error:s}=await u.from("profiles").insert({id:e,email:t,first_name:r,last_name:o,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});s&&console.error("Error creating user profile:",s)}catch(e){console.error("Exception creating user profile:",e)}}async function x(e,t,r,o){let s=await p(t),{data:n,error:i}=await u.from("custom_users").insert({email:e,password_hash:s,first_name:r,last_name:o}).select();if(i)throw console.error("Error creating user:",i),Error("Failed to create user");return await h(n[0].id,e,r,o),n[0]}async function w(e){let{data:t,error:r}=await u.from("custom_users").select("*").eq("email",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by email:",r),Error("Failed to get user")}return t}async function b(e){let{data:t,error:r}=await u.from("custom_users").select("*").eq("id",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by ID:",r),Error("Failed to get user")}return t}async function _(e,t=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let r=s().randomBytes(32).toString("hex"),o=new Date;o.setDate(o.getDate()+t);let{data:n,error:i}=await u.from("user_sessions").insert({user_id:e,session_token:r,expires_at:o.toISOString()}).select();if(i)throw console.error("Error creating session:",i),Error("Failed to create session");return{sessionToken:r,expiresAt:o,userId:e}}async function v(e){try{let{data:t,error:r}=await u.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(r){if("PGRST116"===r.code)return null;return console.error("Error getting session by token:",r),null}return t}catch(e){return console.error("Exception getting session by token:",e),null}}async function k(e){let{error:t}=await u.from("user_sessions").delete().eq("session_token",e);if(t)throw console.error("Error deleting session:",t),Error("Failed to delete session");return!0}async function S(e){let{error:t}=await u.from("custom_users").update({email_verified:!0}).eq("email",e);if(t)throw console.error("Error marking email as verified:",t),Error("Failed to mark email as verified");return!0}async function E(e,t,r){let{error:o}=await u.from("custom_users").update({first_name:t,last_name:r}).eq("id",e);if(o)throw console.error("Error updating user profile:",o),Error("Failed to update user profile");return!0}async function A(e,t){let{error:r}=await u.from("custom_users").update({email:t,email_verified:!1}).eq("id",e);if(r)throw console.error("Error updating user email:",r),Error("Failed to update user email");return!0}async function R(e,t){let r=await p(t),{error:o}=await u.from("custom_users").update({password_hash:r}).eq("id",e);if(o)throw console.error("Error updating user password:",o),Error("Failed to update user password");return!0}},46029:(e,t,r)=>{r.d(t,{ZP:()=>o,dO:()=>s});let o=new(r(31059)).Z(process.env.STRIPE_SECRET_KEY||"",{apiVersion:"2023-10-16"}),s={PREMIUM:process.env.STRIPE_PREMIUM_PLAN_ID||"price_premium"}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,135,541],()=>r(87743));module.exports=o})();