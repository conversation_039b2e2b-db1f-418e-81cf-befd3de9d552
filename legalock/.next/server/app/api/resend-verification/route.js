"use strict";(()=>{var e={};e.id=7525,e.ids=[7525],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},99143:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>b,patchFetch:()=>w,requestAsyncStorage:()=>g,routeModule:()=>y,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{POST:()=>f});var n=r(49303),i=r(88716),o=r(60670),a=r(87070),l=r(31518),c=r(2723),u=r(79835);let d=process.env.RESEND_API_KEY||"dummy_key_for_development",h=new c.R(d),p=process.env.SUPABASE_SERVICE_ROLE_KEY||"dummy_service_key_for_development",m=(0,l.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",p,{auth:{autoRefreshToken:!1,persistSession:!1}});async function f(e){try{let{email:t}=await e.json();if(!t)return console.error("Resend verification: Email is required"),a.NextResponse.json({error:"Email is required"},{status:400});console.log("Resending verification code for email:",t);let r=new Date;r.setHours(r.getHours()-1);let{count:s,error:n}=await m.from("verification_codes").select("*",{count:"exact",head:!0}).eq("email",t).gte("created_at",r.toISOString());if(n)return console.error("Error checking rate limit:",n),a.NextResponse.json({error:n.message},{status:500});if(s&&s>=3)return console.error("Rate limit exceeded for email:",t),a.NextResponse.json({error:"Too many attempts. Please wait a few minutes before trying again."},{status:429});let i=Math.floor(1e5+9e5*Math.random()).toString();console.log(`Generated OTP for ${t}: ${i}`);let o=new Date;o.setHours(o.getHours()+24);let{data:l,error:c}=await m.from("verification_codes").insert({email:t,code:i,expires_at:o.toISOString()}).select();if(c)return console.error("Error storing verification code:",c),a.NextResponse.json({error:c.message},{status:500});try{let{data:e,error:r}=await h.emails.send({from:"Legalock <<EMAIL>>",to:[t],subject:"Verify your Legalock account",html:(0,u.C)(t,i)});if(r)return console.error("Error sending custom email with Resend:",r),a.NextResponse.json({error:r.message},{status:500});console.log("Custom verification email sent successfully")}catch(e){return console.error("Exception sending custom email with Resend:",e),a.NextResponse.json({error:"Failed to send verification email"},{status:500})}return console.log("Verification code resent successfully"),a.NextResponse.json({success:!0})}catch(e){return console.error("Unexpected error in resend-verification:",e),a.NextResponse.json({error:e.message||"An error occurred"},{status:500})}}let y=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/resend-verification/route",pathname:"/api/resend-verification",filename:"route",bundlePath:"app/api/resend-verification/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/resend-verification/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:v}=y,b="/api/resend-verification/route";function w(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}},79835:(e,t,r)=>{r.d(t,{C:()=>s});function s(e,t){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${t?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${t}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},2723:(e,t,r)=>{r.d(t,{R:()=>k});var s=Object.defineProperty,n=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,c=(e,t,r)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,u=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&c(e,r,t[r]);if(o)for(var r of o(t))l.call(t,r)&&c(e,r,t[r]);return e},d=(e,t)=>n(e,i(t)),h=(e,t,r)=>new Promise((s,n)=>{var i=e=>{try{a(r.next(e))}catch(e){n(e)}},o=e=>{try{a(r.throw(e))}catch(e){n(e)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(i,o);a((r=r.apply(e,t)).next())}),p=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},m=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function f(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var y=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){let s=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}s.push(f(t))}return yield this.resend.post("/emails/batch",s,t)})}},g=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return h(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return h(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return h(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},x=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts/${e.id}`)})}update(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},v=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return h(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},b=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",f(e),t)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return h(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},w="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",_="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.2.0",k=class{constructor(e){if(this.key=e,this.apiKeys=new p(this),this.audiences=new m(this),this.batch=new y(this),this.broadcasts=new g(this),this.contacts=new x(this),this.domains=new v(this),this.emails=new b(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":_,"Content-Type":"application/json"})}fetchRequest(e){return h(this,arguments,function*(e,t={}){try{let r=yield fetch(`${w}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:d(u({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return h(this,arguments,function*(e,t,r={}){let s=u({method:"POST",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}get(e){return h(this,arguments,function*(e,t={}){let r=u({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return h(this,arguments,function*(e,t,r={}){let s=u({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}patch(e,t){return h(this,arguments,function*(e,t,r={}){let s=u({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}delete(e,t){return h(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,135],()=>r(99143));module.exports=s})();