"use strict";(()=>{var e={};e.id=577,e.ids=[577],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},22713:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>w,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>_,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>f,GET:()=>p,POST:()=>d,PUT:()=>g});var o=t(49303),i=t(88716),n=t(60670),a=t(87070),u=t(71615),l=t(43478),c=t(1926);async function p(e){try{let e=(0,u.cookies)(),r=e.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await (0,l.M3)(r);if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,l.GA)(t.user_id);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=(0,c.S6)(),{data:i,error:n}=await o.from("auth_users_mapping").select("auth_user_id").eq("custom_user_id",s.id).single();if(n||!i)return console.error("Error getting auth user:",n),a.NextResponse.json({error:"Failed to get auth user mapping"},{status:500});let{data:p,error:d}=await o.from("trustees").select("*").eq("user_id",i.auth_user_id).order("created_at",{ascending:!1});if(d)return console.error("Error fetching trustees:",d),a.NextResponse.json({error:"Failed to fetch trustees"},{status:500});return a.NextResponse.json(p)}catch(e){return console.error("Error in trustees API:",e),a.NextResponse.json({error:e.message||"An error occurred"},{status:500})}}async function d(e){try{let r=(0,u.cookies)(),t=r.get("session_token")?.value;if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,l.M3)(t);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,l.GA)(s.user_id);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let i=await e.json();if(!i.first_name||!i.last_name||!i.email||!i.permissions)return a.NextResponse.json({error:"Missing required fields"},{status:400});let n=(0,c.S6)(),{data:p,error:d}=await n.from("auth_users_mapping").select("auth_user_id").eq("custom_user_id",o.id).single();if(d||!p)return console.error("Error getting auth user:",d),a.NextResponse.json({error:"Failed to get auth user mapping"},{status:500});let{data:g,error:f}=await n.from("profiles").select("id").eq("email",i.email).single(),m={user_id:p.auth_user_id,trustee_email:i.email,trustee_user_id:g?.id||null,first_name:i.first_name,last_name:i.last_name,relationship:i.relationship||"",status:"pending",permissions:i.permissions,invitation_sent_at:null};try{let{data:e,error:r}=await n.from("trustees").insert(m).select();if(r)return console.error("Error creating trustee:",r),a.NextResponse.json({error:`Failed to create trustee: ${r.message}`},{status:500});if(!e||0===e.length)return a.NextResponse.json({error:"No data returned after trustee creation"},{status:500});return a.NextResponse.json(e[0])}catch(e){return console.error("Exception creating trustee:",e),a.NextResponse.json({error:`Exception creating trustee: ${e.message}`},{status:500})}}catch(e){return console.error("Error in trustees API:",e),a.NextResponse.json({error:e.message||"An error occurred"},{status:500})}}async function g(e){try{let r=(0,u.cookies)(),t=r.get("session_token")?.value;if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,l.M3)(t);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,l.GA)(s.user_id);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{id:i,...n}=await e.json();if(!i)return a.NextResponse.json({error:"Trustee ID is required"},{status:400});let p=(0,c.S6)(),{data:d,error:g}=await p.from("auth_users_mapping").select("auth_user_id").eq("custom_user_id",o.id).single();if(g||!d)return console.error("Error getting auth user:",g),a.NextResponse.json({error:"Failed to get auth user mapping"},{status:500});let{data:f,error:m}=await p.from("trustees").select("id").eq("id",i).eq("user_id",d.auth_user_id).single();if(m)return console.error("Error fetching trustee:",m),a.NextResponse.json({error:"Trustee not found or you do not have permission to update it"},{status:404});let{data:h,error:x}=await p.from("trustees").update({...n,updated_at:new Date().toISOString()}).eq("id",i).eq("user_id",d.auth_user_id).select();if(x)return console.error("Error updating trustee:",x),a.NextResponse.json({error:`Failed to update trustee: ${x.message}`},{status:500});return a.NextResponse.json(h)}catch(e){return console.error("Error updating trustee:",e),a.NextResponse.json({error:e.message||"Failed to update trustee"},{status:500})}}async function f(e){try{let r=(0,u.cookies)(),t=r.get("session_token")?.value;if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,l.M3)(t);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,l.GA)(s.user_id);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let i=new URL(e.url).searchParams.get("id");if(!i)return a.NextResponse.json({error:"Trustee ID is required"},{status:400});let n=(0,c.S6)(),{data:p,error:d}=await n.from("auth_users_mapping").select("auth_user_id").eq("custom_user_id",o.id).single();if(d||!p)return console.error("Error getting auth user:",d),a.NextResponse.json({error:"Failed to get auth user mapping"},{status:500});let{data:g,error:f}=await n.from("trustees").select("id").eq("id",i).eq("user_id",p.auth_user_id).single();if(f)return console.error("Error fetching trustee:",f),a.NextResponse.json({error:"Trustee not found or you do not have permission to delete it"},{status:404});let{error:m}=await n.from("trustees").delete().eq("id",i).eq("user_id",p.auth_user_id);if(m)return console.error("Error deleting trustee:",m),a.NextResponse.json({error:`Failed to delete trustee: ${m.message}`},{status:500});return a.NextResponse.json({success:!0})}catch(e){return console.error("Error deleting trustee:",e),a.NextResponse.json({error:e.message||"Failed to delete trustee"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/trustees/route",pathname:"/api/trustees",filename:"route",bundlePath:"app/api/trustees/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/trustees/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:x,serverHooks:_}=m,y="/api/trustees/route";function w(){return(0,n.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:x})}},79835:(e,r,t)=>{t.d(r,{C:()=>s});function s(e,r){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${r?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${r}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,r,t)=>{t.d(r,{CX:()=>y,GA:()=>w,Gv:()=>g,Lj:()=>S,M3:()=>v,SO:()=>I,SX:()=>p,VP:()=>h,Zy:()=>q,cJ:()=>j,ed:()=>E,pO:()=>R,pR:()=>c,r4:()=>_,xJ:()=>m,xv:()=>x,zk:()=>f});var s=t(84770),o=t.n(s),i=t(31518),n=t(2723),a=t(79835);let u=new n.R(process.env.RESEND_API_KEY),l=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,i.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",l,{auth:{autoRefreshToken:!1,persistSession:!1}});function p(){return Math.floor(1e5+9e5*Math.random()).toString()}function d(e){return new Promise((r,t)=>{let s=o().randomBytes(16).toString("hex");o().pbkdf2(e,s,1e4,64,"sha512",(e,o)=>{if(e){t(e);return}r(`10000:${s}:${o.toString("hex")}`)})})}function g(e,r){return new Promise((t,s)=>{let[i,n,a]=r.split(":"),u=parseInt(i);o().pbkdf2(e,n,u,64,"sha512",(e,r)=>{if(e){s(e);return}t(r.toString("hex")===a)})})}async function f(e,r){try{let{data:t,error:s}=await u.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,r)});if(s)return console.error("Error sending verification email:",s),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function m(e,r,t=24){let s=new Date;s.setHours(s.getHours()+t);let{data:o,error:i}=await c.from("verification_codes").insert({email:e,code:r,expires_at:s.toISOString()}).select();if(i)throw console.error("Error storing verification code:",i),Error("Failed to store verification code");return o}async function h(e,r){let{data:t,error:s}=await c.from("verification_codes").select("*").eq("email",e).eq("code",r).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(s)return console.error("Error verifying code:",s),!1;if(!t||0===t.length)return!1;let{error:o}=await c.from("verification_codes").update({used:!0}).eq("id",t[0].id);return o&&console.error("Error marking code as used:",o),!0}async function x(e,r,t,s){try{let{error:o}=await c.from("profiles").insert({id:e,email:r,first_name:t,last_name:s,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});o&&console.error("Error creating user profile:",o)}catch(e){console.error("Exception creating user profile:",e)}}async function _(e,r,t,s){let o=await d(r),{data:i,error:n}=await c.from("custom_users").insert({email:e,password_hash:o,first_name:t,last_name:s}).select();if(n)throw console.error("Error creating user:",n),Error("Failed to create user");return await x(i[0].id,e,t,s),i[0]}async function y(e){let{data:r,error:t}=await c.from("custom_users").select("*").eq("email",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by email:",t),Error("Failed to get user")}return r}async function w(e){let{data:r,error:t}=await c.from("custom_users").select("*").eq("id",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by ID:",t),Error("Failed to get user")}return r}async function E(e,r=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let t=o().randomBytes(32).toString("hex"),s=new Date;s.setDate(s.getDate()+r);let{data:i,error:n}=await c.from("user_sessions").insert({user_id:e,session_token:t,expires_at:s.toISOString()}).select();if(n)throw console.error("Error creating session:",n),Error("Failed to create session");return{sessionToken:t,expiresAt:s,userId:e}}async function v(e){try{let{data:r,error:t}=await c.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(t){if("PGRST116"===t.code)return null;return console.error("Error getting session by token:",t),null}return r}catch(e){return console.error("Exception getting session by token:",e),null}}async function I(e){let{error:r}=await c.from("user_sessions").delete().eq("session_token",e);if(r)throw console.error("Error deleting session:",r),Error("Failed to delete session");return!0}async function R(e){let{error:r}=await c.from("custom_users").update({email_verified:!0}).eq("email",e);if(r)throw console.error("Error marking email as verified:",r),Error("Failed to mark email as verified");return!0}async function S(e,r,t){let{error:s}=await c.from("custom_users").update({first_name:r,last_name:t}).eq("id",e);if(s)throw console.error("Error updating user profile:",s),Error("Failed to update user profile");return!0}async function j(e,r){let{error:t}=await c.from("custom_users").update({email:r,email_verified:!1}).eq("id",e);if(t)throw console.error("Error updating user email:",t),Error("Failed to update user email");return!0}async function q(e,r){let t=await d(r),{error:s}=await c.from("custom_users").update({password_hash:t}).eq("id",e);if(s)throw console.error("Error updating user password:",s),Error("Failed to update user password");return!0}},1926:(e,r,t)=>{t.d(r,{S6:()=>a});var s=t(31518);let o="https://ccwvtcudztphwwzzgwvg.supabase.co",i=null,n=null;function a(){if(n)return n;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";return n=(0,s.eI)(o,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}(0,s.eI)(o,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}}),a()}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,135,9322],()=>t(22713));module.exports=s})();