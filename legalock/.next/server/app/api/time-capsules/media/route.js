"use strict";(()=>{var e={};e.id=9586,e.ids=[9586],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},99472:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>y,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>x,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>d,POST:()=>p});var s=t(49303),i=t(88716),n=t(60670),a=t(87070),l=t(71615),u=t(43478),c=t(1926);async function p(e){try{let r=(0,l.cookies)(),t=r.get("session_token")?.value;if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,u.M3)(t);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,u.GA)(o.user_id);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{capsule_id:i,files:n}=await e.json();if(!i||!n||!Array.isArray(n)||0===n.length)return a.NextResponse.json({error:"Capsule ID and files are required"},{status:400});let p=(0,c.S6)(),d=s.id;console.log("Using user ID for media POST:",d);let{data:m,error:f}=await p.from("time_capsules").select("id").eq("id",i).eq("user_id",d).single();if(f||!m)return console.error("Error verifying capsule ownership:",f),a.NextResponse.json({error:"Capsule not found or you do not have permission to add media to it"},{status:404});let g=n.map(e=>({capsule_id:i,file_path:e.path,file_name:e.name,file_type:e.type,file_size:e.size})),{data:x,error:h}=await p.from("time_capsule_media").insert(g).select();if(h)return console.error("Error saving media metadata:",h),a.NextResponse.json({error:"Failed to save media metadata"},{status:500});return a.NextResponse.json(x)}catch(e){return console.error("Error in POST /api/time-capsules/media:",e),a.NextResponse.json({error:e.message||"An unexpected error occurred"},{status:500})}}async function d(e){try{let{searchParams:r}=new URL(e.url),t=r.get("capsule_id");if(!t)return a.NextResponse.json({error:"Capsule ID is required"},{status:400});let o=(0,l.cookies)(),s=o.get("session_token")?.value;if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let i=await (0,u.M3)(s);if(!i)return a.NextResponse.json({error:"Unauthorized"},{status:401});let n=await (0,u.GA)(i.user_id);if(!n)return a.NextResponse.json({error:"Unauthorized"},{status:401});let p=(0,c.S6)(),d=n.id;console.log("Using user ID for media GET:",d);let{data:m,error:f}=await p.from("time_capsules").select("id").eq("id",t).eq("user_id",d).single();if(f)return console.error("Error verifying capsule ownership:",f),a.NextResponse.json({error:"Capsule not found or you do not have permission to view its media"},{status:404});let{data:g,error:x}=await p.from("time_capsule_media").select("*").eq("capsule_id",t).order("created_at",{ascending:!1});if(x)return console.error("Error fetching media:",x),a.NextResponse.json({error:"Failed to fetch media"},{status:500});let h=await Promise.all(g.map(async e=>{let{data:r}=await p.storage.from("time_capsule_media").createSignedUrl(`${d}/${e.file_path}`,3600);return{...e,signed_url:r?.signedUrl||null}}));return a.NextResponse.json(h)}catch(e){return console.error("Error in GET /api/time-capsules/media:",e),a.NextResponse.json({error:e.message||"An unexpected error occurred"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/time-capsules/media/route",pathname:"/api/time-capsules/media",filename:"route",bundlePath:"app/api/time-capsules/media/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/time-capsules/media/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:x}=m,h="/api/time-capsules/media/route";function y(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:g})}},79835:(e,r,t)=>{t.d(r,{C:()=>o});function o(e,r){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${r?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${r}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,r,t)=>{t.d(r,{CX:()=>w,GA:()=>_,Gv:()=>m,Lj:()=>b,M3:()=>I,SO:()=>E,SX:()=>p,VP:()=>x,Zy:()=>q,cJ:()=>k,ed:()=>v,pO:()=>S,pR:()=>c,r4:()=>y,xJ:()=>g,xv:()=>h,zk:()=>f});var o=t(84770),s=t.n(o),i=t(31518),n=t(2723),a=t(79835);let l=new n.R(process.env.RESEND_API_KEY),u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,i.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",u,{auth:{autoRefreshToken:!1,persistSession:!1}});function p(){return Math.floor(1e5+9e5*Math.random()).toString()}function d(e){return new Promise((r,t)=>{let o=s().randomBytes(16).toString("hex");s().pbkdf2(e,o,1e4,64,"sha512",(e,s)=>{if(e){t(e);return}r(`10000:${o}:${s.toString("hex")}`)})})}function m(e,r){return new Promise((t,o)=>{let[i,n,a]=r.split(":"),l=parseInt(i);s().pbkdf2(e,n,l,64,"sha512",(e,r)=>{if(e){o(e);return}t(r.toString("hex")===a)})})}async function f(e,r){try{let{data:t,error:o}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,r)});if(o)return console.error("Error sending verification email:",o),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function g(e,r,t=24){let o=new Date;o.setHours(o.getHours()+t);let{data:s,error:i}=await c.from("verification_codes").insert({email:e,code:r,expires_at:o.toISOString()}).select();if(i)throw console.error("Error storing verification code:",i),Error("Failed to store verification code");return s}async function x(e,r){let{data:t,error:o}=await c.from("verification_codes").select("*").eq("email",e).eq("code",r).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(o)return console.error("Error verifying code:",o),!1;if(!t||0===t.length)return!1;let{error:s}=await c.from("verification_codes").update({used:!0}).eq("id",t[0].id);return s&&console.error("Error marking code as used:",s),!0}async function h(e,r,t,o){try{let{error:s}=await c.from("profiles").insert({id:e,email:r,first_name:t,last_name:o,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});s&&console.error("Error creating user profile:",s)}catch(e){console.error("Exception creating user profile:",e)}}async function y(e,r,t,o){let s=await d(r),{data:i,error:n}=await c.from("custom_users").insert({email:e,password_hash:s,first_name:t,last_name:o}).select();if(n)throw console.error("Error creating user:",n),Error("Failed to create user");return await h(i[0].id,e,t,o),i[0]}async function w(e){let{data:r,error:t}=await c.from("custom_users").select("*").eq("email",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by email:",t),Error("Failed to get user")}return r}async function _(e){let{data:r,error:t}=await c.from("custom_users").select("*").eq("id",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by ID:",t),Error("Failed to get user")}return r}async function v(e,r=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let t=s().randomBytes(32).toString("hex"),o=new Date;o.setDate(o.getDate()+r);let{data:i,error:n}=await c.from("user_sessions").insert({user_id:e,session_token:t,expires_at:o.toISOString()}).select();if(n)throw console.error("Error creating session:",n),Error("Failed to create session");return{sessionToken:t,expiresAt:o,userId:e}}async function I(e){try{let{data:r,error:t}=await c.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(t){if("PGRST116"===t.code)return null;return console.error("Error getting session by token:",t),null}return r}catch(e){return console.error("Exception getting session by token:",e),null}}async function E(e){let{error:r}=await c.from("user_sessions").delete().eq("session_token",e);if(r)throw console.error("Error deleting session:",r),Error("Failed to delete session");return!0}async function S(e){let{error:r}=await c.from("custom_users").update({email_verified:!0}).eq("email",e);if(r)throw console.error("Error marking email as verified:",r),Error("Failed to mark email as verified");return!0}async function b(e,r,t){let{error:o}=await c.from("custom_users").update({first_name:r,last_name:t}).eq("id",e);if(o)throw console.error("Error updating user profile:",o),Error("Failed to update user profile");return!0}async function k(e,r){let{error:t}=await c.from("custom_users").update({email:r,email_verified:!1}).eq("id",e);if(t)throw console.error("Error updating user email:",t),Error("Failed to update user email");return!0}async function q(e,r){let t=await d(r),{error:o}=await c.from("custom_users").update({password_hash:t}).eq("id",e);if(o)throw console.error("Error updating user password:",o),Error("Failed to update user password");return!0}},1926:(e,r,t)=>{t.d(r,{S6:()=>a});var o=t(31518);let s="https://ccwvtcudztphwwzzgwvg.supabase.co",i=null,n=null;function a(){if(n)return n;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";return n=(0,o.eI)(s,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}(0,o.eI)(s,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}}),a()}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,135,9322],()=>t(99472));module.exports=o})();