"use strict";(()=>{var e={};e.id=8428,e.ids=[8428],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},98908:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>_,requestAsyncStorage:()=>x,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{DELETE:()=>g,GET:()=>p,POST:()=>d,PUT:()=>f});var s=r(49303),n=r(88716),i=r(60670),a=r(87070),c=r(71615),u=r(43478),l=r(1926);async function p(e){try{let e=(0,c.cookies)(),t=e.get("session_token")?.value;if(!t)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await (0,u.M3)(t);if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,u.GA)(r.user_id);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=(0,l.S6)(),{error:n}=await s.from("profiles").upsert({id:o.id,email:o.email,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{onConflict:"id"});n&&console.error("Error creating profile:",n);let i=o.id,{data:p,error:d}=await s.from("contacts").select("*").eq("user_id",i).order("first_name",{ascending:!0});if(d)return console.error("Error fetching contacts:",d),a.NextResponse.json({error:"Failed to fetch contacts"},{status:500});return a.NextResponse.json(p||[])}catch(e){return console.error("Error fetching contacts:",e),a.NextResponse.json({error:e.message||"Failed to fetch contacts"},{status:500})}}async function d(e){try{let t=(0,c.cookies)(),r=t.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,u.M3)(r);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,u.GA)(o.user_id);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let n=await e.json();if(!n.email&&!n.phone)return a.NextResponse.json({error:"Either email or phone number is required"},{status:400});let i=(0,l.S6)(),p=s.id,{data:d,error:f}=await i.from("custom_users").select("id").eq("id",s.id).single();if(f||!d)return console.error("No user found in custom_users table:",s.id),a.NextResponse.json({error:"User not found"},{status:404});let{error:g}=await i.from("profiles").upsert({id:s.id,email:s.email,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{onConflict:"id"});if(g)return console.error("Error creating profile:",g),a.NextResponse.json({error:"Failed to verify user profile"},{status:500});let m={...n,user_id:p};try{let{data:e,error:t}=await i.from("contacts").insert(m).select();if(t)return console.error("Error creating contact:",t),a.NextResponse.json({error:`Failed to create contact: ${t.message}`},{status:500});if(!e||0===e.length)return a.NextResponse.json({error:"No data returned after contact creation"},{status:500});return a.NextResponse.json(e[0])}catch(e){return console.error("Exception creating contact:",e),a.NextResponse.json({error:`Exception creating contact: ${e.message}`},{status:500})}}catch(e){return console.error("Error adding contact:",e),a.NextResponse.json({error:e.message||"Failed to add contact"},{status:500})}}async function f(e){try{let t=(0,c.cookies)(),r=t.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,u.M3)(r);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,u.GA)(o.user_id);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{id:n,...i}=await e.json();if(!n)return a.NextResponse.json({error:"Contact ID is required"},{status:400});if(!i.email&&!i.phone)return a.NextResponse.json({error:"Either email or phone number is required"},{status:400});let p=(0,l.S6)(),d=s.id,{data:f,error:g}=await p.from("contacts").select("id").eq("id",n).eq("user_id",d).single();if(g)return console.error("Error fetching contact:",g),a.NextResponse.json({error:"Contact not found or you do not have permission to update it"},{status:404});let{data:m,error:x}=await p.from("contacts").update({...i,updated_at:new Date().toISOString()}).eq("id",n).eq("user_id",d).select().single();if(x)return console.error("Error updating contact:",x),a.NextResponse.json({error:`Failed to update contact: ${x.message}`},{status:500});return a.NextResponse.json(m)}catch(e){return console.error("Error updating contact:",e),a.NextResponse.json({error:e.message||"Failed to update contact"},{status:500})}}async function g(e){try{let t=(0,c.cookies)(),r=t.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,u.M3)(r);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,u.GA)(o.user_id);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let n=new URL(e.url).searchParams.get("id");if(!n)return a.NextResponse.json({error:"Contact ID is required"},{status:400});let i=(0,l.S6)(),p=s.id,{data:d,error:f}=await i.from("contacts").select("id").eq("id",n).eq("user_id",p).single();if(f)return console.error("Error fetching contact:",f),a.NextResponse.json({error:"Contact not found or you do not have permission to delete it"},{status:404});let{error:g}=await i.from("contacts").delete().eq("id",n).eq("user_id",p);if(g)return console.error("Error deleting contact:",g),a.NextResponse.json({error:`Failed to delete contact: ${g.message}`},{status:500});return a.NextResponse.json({success:!0})}catch(e){return console.error("Error deleting contact:",e),a.NextResponse.json({error:e.message||"Failed to delete contact"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/contacts/route",pathname:"/api/contacts",filename:"route",bundlePath:"app/api/contacts/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/contacts/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:x,staticGenerationAsyncStorage:h,serverHooks:y}=m,w="/api/contacts/route";function _(){return(0,i.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},79835:(e,t,r)=>{r.d(t,{C:()=>o});function o(e,t){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${t?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${t}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,t,r)=>{r.d(t,{CX:()=>w,GA:()=>_,Gv:()=>f,Lj:()=>R,M3:()=>v,SO:()=>S,SX:()=>p,VP:()=>x,Zy:()=>j,cJ:()=>b,ed:()=>E,pO:()=>I,pR:()=>l,r4:()=>y,xJ:()=>m,xv:()=>h,zk:()=>g});var o=r(84770),s=r.n(o),n=r(31518),i=r(2723),a=r(79835);let c=new i.R(process.env.RESEND_API_KEY),u=process.env.SUPABASE_SERVICE_ROLE_KEY,l=(0,n.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",u,{auth:{autoRefreshToken:!1,persistSession:!1}});function p(){return Math.floor(1e5+9e5*Math.random()).toString()}function d(e){return new Promise((t,r)=>{let o=s().randomBytes(16).toString("hex");s().pbkdf2(e,o,1e4,64,"sha512",(e,s)=>{if(e){r(e);return}t(`10000:${o}:${s.toString("hex")}`)})})}function f(e,t){return new Promise((r,o)=>{let[n,i,a]=t.split(":"),c=parseInt(n);s().pbkdf2(e,i,c,64,"sha512",(e,t)=>{if(e){o(e);return}r(t.toString("hex")===a)})})}async function g(e,t){try{let{data:r,error:o}=await c.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,t)});if(o)return console.error("Error sending verification email:",o),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function m(e,t,r=24){let o=new Date;o.setHours(o.getHours()+r);let{data:s,error:n}=await l.from("verification_codes").insert({email:e,code:t,expires_at:o.toISOString()}).select();if(n)throw console.error("Error storing verification code:",n),Error("Failed to store verification code");return s}async function x(e,t){let{data:r,error:o}=await l.from("verification_codes").select("*").eq("email",e).eq("code",t).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(o)return console.error("Error verifying code:",o),!1;if(!r||0===r.length)return!1;let{error:s}=await l.from("verification_codes").update({used:!0}).eq("id",r[0].id);return s&&console.error("Error marking code as used:",s),!0}async function h(e,t,r,o){try{let{error:s}=await l.from("profiles").insert({id:e,email:t,first_name:r,last_name:o,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});s&&console.error("Error creating user profile:",s)}catch(e){console.error("Exception creating user profile:",e)}}async function y(e,t,r,o){let s=await d(t),{data:n,error:i}=await l.from("custom_users").insert({email:e,password_hash:s,first_name:r,last_name:o}).select();if(i)throw console.error("Error creating user:",i),Error("Failed to create user");return await h(n[0].id,e,r,o),n[0]}async function w(e){let{data:t,error:r}=await l.from("custom_users").select("*").eq("email",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by email:",r),Error("Failed to get user")}return t}async function _(e){let{data:t,error:r}=await l.from("custom_users").select("*").eq("id",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by ID:",r),Error("Failed to get user")}return t}async function E(e,t=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let r=s().randomBytes(32).toString("hex"),o=new Date;o.setDate(o.getDate()+t);let{data:n,error:i}=await l.from("user_sessions").insert({user_id:e,session_token:r,expires_at:o.toISOString()}).select();if(i)throw console.error("Error creating session:",i),Error("Failed to create session");return{sessionToken:r,expiresAt:o,userId:e}}async function v(e){try{let{data:t,error:r}=await l.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(r){if("PGRST116"===r.code)return null;return console.error("Error getting session by token:",r),null}return t}catch(e){return console.error("Exception getting session by token:",e),null}}async function S(e){let{error:t}=await l.from("user_sessions").delete().eq("session_token",e);if(t)throw console.error("Error deleting session:",t),Error("Failed to delete session");return!0}async function I(e){let{error:t}=await l.from("custom_users").update({email_verified:!0}).eq("email",e);if(t)throw console.error("Error marking email as verified:",t),Error("Failed to mark email as verified");return!0}async function R(e,t,r){let{error:o}=await l.from("custom_users").update({first_name:t,last_name:r}).eq("id",e);if(o)throw console.error("Error updating user profile:",o),Error("Failed to update user profile");return!0}async function b(e,t){let{error:r}=await l.from("custom_users").update({email:t,email_verified:!1}).eq("id",e);if(r)throw console.error("Error updating user email:",r),Error("Failed to update user email");return!0}async function j(e,t){let r=await d(t),{error:o}=await l.from("custom_users").update({password_hash:r}).eq("id",e);if(o)throw console.error("Error updating user password:",o),Error("Failed to update user password");return!0}},1926:(e,t,r)=>{r.d(t,{S6:()=>a});var o=r(31518);let s="https://ccwvtcudztphwwzzgwvg.supabase.co",n=null,i=null;function a(){if(i)return i;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";return i=(0,o.eI)(s,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}(0,o.eI)(s,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}}),a()}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,135,9322],()=>r(98908));module.exports=o})();