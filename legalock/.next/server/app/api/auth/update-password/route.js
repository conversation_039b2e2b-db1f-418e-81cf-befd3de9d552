"use strict";(()=>{var e={};e.id=4403,e.ids=[4403,9322],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},52835:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{POST:()=>c});var n=r(49303),o=r(88716),i=r(60670),a=r(87070),l=r(71615),u=r(43478);async function c(e){try{let t=(0,l.cookies)(),r=t.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,u.M3)(r);if(!s)return t.delete("session_token"),a.NextResponse.json({error:"Unauthorized"},{status:401});let{password:n}=await e.json();if(!n)return a.NextResponse.json({error:"Password is required"},{status:400});if(n.length<8)return a.NextResponse.json({error:"Password must be at least 8 characters long"},{status:400});return await (0,u.Zy)(s.user_id,n),a.NextResponse.json({success:!0,message:"Password updated successfully"})}catch(e){return console.error("Error in update-password:",e),a.NextResponse.json({error:e.message||"An error occurred while updating password"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/update-password/route",pathname:"/api/auth/update-password",filename:"route",bundlePath:"app/api/auth/update-password/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/auth/update-password/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:f,serverHooks:h}=d,y="/api/auth/update-password/route";function m(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},71615:(e,t,r)=>{var s=r(88757);r.o(s,"cookies")&&r.d(t,{cookies:function(){return s.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return o}});let s=r(45869),n=r(6278);class o{get isEnabled(){return this._provider.isEnabled}enable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,n.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,n.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return f},headers:function(){return d}});let s=r(68996),n=r(53047),o=r(92044),i=r(72934),a=r(33085),l=r(6278),u=r(45869),c=r(54580);function d(){let e="headers",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,c.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.RequestCookiesAdapter.seal(new o.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,c.getExpectedRequestStore)(e),n=i.actionAsyncStorage.getStore();return(null==n?void 0:n.isAction)||(null==n?void 0:n.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,c.getExpectedRequestStore)("draftMode");return new a.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return n}});let s=r(38238);class n extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new n}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,n);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==i)return s.ReflectAdapter.get(t,i,n)},set(t,r,n,o){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,n,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return s.ReflectAdapter.set(t,a??r,n,o)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let n=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==o&&s.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===o||s.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return n.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return i},RequestCookiesAdapter:function(){return a},appendMutableCookies:function(){return c},getModifiedCookieValues:function(){return u}});let s=r(92044),n=r(38238),o=r(45869);class i extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new i}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function c(e,t){let r=u(t);if(0===r.length)return!1;let n=new s.ResponseCookies(e),o=n.getAll();for(let e of r)n.set(e);for(let e of o)n.set(e);return!0}class d{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],a=new Set,u=()=>{let e=o.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of i){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return i;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{u()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{u()}};default:return n.ReflectAdapter.get(e,t,r)}}})}}},79835:(e,t,r)=>{r.d(t,{C:()=>s});function s(e,t){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${t?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${t}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,t,r)=>{r.d(t,{CX:()=>w,GA:()=>x,Gv:()=>f,Lj:()=>S,M3:()=>_,SO:()=>k,SX:()=>d,VP:()=>m,Zy:()=>R,cJ:()=>E,ed:()=>v,pO:()=>A,pR:()=>c,r4:()=>b,xJ:()=>y,xv:()=>g,zk:()=>h});var s=r(84770),n=r.n(s),o=r(31518),i=r(2723),a=r(79835);let l=new i.R(process.env.RESEND_API_KEY),u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,o.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",u,{auth:{autoRefreshToken:!1,persistSession:!1}});function d(){return Math.floor(1e5+9e5*Math.random()).toString()}function p(e){return new Promise((t,r)=>{let s=n().randomBytes(16).toString("hex");n().pbkdf2(e,s,1e4,64,"sha512",(e,n)=>{if(e){r(e);return}t(`10000:${s}:${n.toString("hex")}`)})})}function f(e,t){return new Promise((r,s)=>{let[o,i,a]=t.split(":"),l=parseInt(o);n().pbkdf2(e,i,l,64,"sha512",(e,t)=>{if(e){s(e);return}r(t.toString("hex")===a)})})}async function h(e,t){try{let{data:r,error:s}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,t)});if(s)return console.error("Error sending verification email:",s),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function y(e,t,r=24){let s=new Date;s.setHours(s.getHours()+r);let{data:n,error:o}=await c.from("verification_codes").insert({email:e,code:t,expires_at:s.toISOString()}).select();if(o)throw console.error("Error storing verification code:",o),Error("Failed to store verification code");return n}async function m(e,t){let{data:r,error:s}=await c.from("verification_codes").select("*").eq("email",e).eq("code",t).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(s)return console.error("Error verifying code:",s),!1;if(!r||0===r.length)return!1;let{error:n}=await c.from("verification_codes").update({used:!0}).eq("id",r[0].id);return n&&console.error("Error marking code as used:",n),!0}async function g(e,t,r,s){try{let{error:n}=await c.from("profiles").insert({id:e,email:t,first_name:r,last_name:s,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});n&&console.error("Error creating user profile:",n)}catch(e){console.error("Exception creating user profile:",e)}}async function b(e,t,r,s){let n=await p(t),{data:o,error:i}=await c.from("custom_users").insert({email:e,password_hash:n,first_name:r,last_name:s}).select();if(i)throw console.error("Error creating user:",i),Error("Failed to create user");return await g(o[0].id,e,r,s),o[0]}async function w(e){let{data:t,error:r}=await c.from("custom_users").select("*").eq("email",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by email:",r),Error("Failed to get user")}return t}async function x(e){let{data:t,error:r}=await c.from("custom_users").select("*").eq("id",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by ID:",r),Error("Failed to get user")}return t}async function v(e,t=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let r=n().randomBytes(32).toString("hex"),s=new Date;s.setDate(s.getDate()+t);let{data:o,error:i}=await c.from("user_sessions").insert({user_id:e,session_token:r,expires_at:s.toISOString()}).select();if(i)throw console.error("Error creating session:",i),Error("Failed to create session");return{sessionToken:r,expiresAt:s,userId:e}}async function _(e){try{let{data:t,error:r}=await c.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(r){if("PGRST116"===r.code)return null;return console.error("Error getting session by token:",r),null}return t}catch(e){return console.error("Exception getting session by token:",e),null}}async function k(e){let{error:t}=await c.from("user_sessions").delete().eq("session_token",e);if(t)throw console.error("Error deleting session:",t),Error("Failed to delete session");return!0}async function A(e){let{error:t}=await c.from("custom_users").update({email_verified:!0}).eq("email",e);if(t)throw console.error("Error marking email as verified:",t),Error("Failed to mark email as verified");return!0}async function S(e,t,r){let{error:s}=await c.from("custom_users").update({first_name:t,last_name:r}).eq("id",e);if(s)throw console.error("Error updating user profile:",s),Error("Failed to update user profile");return!0}async function E(e,t){let{error:r}=await c.from("custom_users").update({email:t,email_verified:!1}).eq("id",e);if(r)throw console.error("Error updating user email:",r),Error("Failed to update user email");return!0}async function R(e,t){let r=await p(t),{error:s}=await c.from("custom_users").update({password_hash:r}).eq("id",e);if(s)throw console.error("Error updating user password:",s),Error("Failed to update user password");return!0}},2723:(e,t,r)=>{r.d(t,{R:()=>k});var s=Object.defineProperty,n=Object.defineProperties,o=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&u(e,r,t[r]);if(i)for(var r of i(t))l.call(t,r)&&u(e,r,t[r]);return e},d=(e,t)=>n(e,o(t)),p=(e,t,r)=>new Promise((s,n)=>{var o=e=>{try{a(r.next(e))}catch(e){n(e)}},i=e=>{try{a(r.throw(e))}catch(e){n(e)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(o,i);a((r=r.apply(e,t)).next())}),f=class{constructor(e){this.resend=e}create(e){return p(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return p(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return p(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},h=class{constructor(e){this.resend=e}create(e){return p(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return p(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return p(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return p(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function y(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var m=class{constructor(e){this.resend=e}send(e){return p(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return p(this,arguments,function*(e,t={}){let s=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}s.push(y(t))}return yield this.resend.post("/emails/batch",s,t)})}},g=class{constructor(e){this.resend=e}create(e){return p(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return p(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return p(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return p(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return p(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return p(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},b=class{constructor(e){this.resend=e}create(e){return p(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return p(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return p(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts/${e.id}`)})}update(e){return p(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return p(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},w=class{constructor(e){this.resend=e}create(e){return p(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",e,t)})}list(){return p(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return p(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return p(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return p(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return p(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},x=class{constructor(e){this.resend=e}send(e){return p(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return p(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",y(e),t)})}get(e){return p(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return p(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return p(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},v="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",_="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.2.0",k=class{constructor(e){if(this.key=e,this.apiKeys=new f(this),this.audiences=new h(this),this.batch=new m(this),this.broadcasts=new g(this),this.contacts=new b(this),this.domains=new w(this),this.emails=new x(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":_,"Content-Type":"application/json"})}fetchRequest(e){return p(this,arguments,function*(e,t={}){try{let r=yield fetch(`${v}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:d(c({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return p(this,arguments,function*(e,t,r={}){let s=c({method:"POST",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}get(e){return p(this,arguments,function*(e,t={}){let r=c({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return p(this,arguments,function*(e,t,r={}){let s=c({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}patch(e,t){return p(this,arguments,function*(e,t,r={}){let s=c({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}delete(e,t){return p(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,135],()=>r(52835));module.exports=s})();