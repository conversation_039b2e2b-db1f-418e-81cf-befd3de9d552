"use strict";(()=>{var e={};e.id=4596,e.ids=[4596],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},30117:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>p,staticGenerationAsyncStorage:()=>h});var n={};r.r(n),r.d(n,{POST:()=>u});var s=r(49303),i=r(88716),o=r(60670),a=r(87070),l=r(43478);async function u(e){try{let{email:t}=await e.json();if(!t)return a.NextResponse.json({error:"Email is required"},{status:400});if(!await (0,l.CX)(t))return a.NextResponse.json({error:"User not found"},{status:404});let r=new Date;r.setHours(r.getHours()-1);let{count:n,error:s}=await l.pR.from("verification_codes").select("*",{count:"exact",head:!0}).eq("email",t).gte("created_at",r.toISOString());if(s)return console.error("Error checking rate limit:",s),a.NextResponse.json({error:s.message},{status:500});if(n&&n>=3)return a.NextResponse.json({error:"Too many attempts. Please wait a few minutes before trying again."},{status:429});let i=(0,l.SX)();if(await (0,l.xJ)(t,i),!await (0,l.zk)(t,i))return a.NextResponse.json({error:"Failed to send verification email"},{status:500});return a.NextResponse.json({success:!0,message:"Verification code sent successfully"})}catch(e){return console.error("Error in resend verification:",e),a.NextResponse.json({error:e.message||"An error occurred while resending verification code"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/resend-verification/route",pathname:"/api/auth/resend-verification",filename:"route",bundlePath:"app/api/auth/resend-verification/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/auth/resend-verification/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:p}=c,f="/api/auth/resend-verification/route";function m(){return(0,o.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:h})}},79835:(e,t,r)=>{r.d(t,{C:()=>n});function n(e,t){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${t?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${t}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,t,r)=>{r.d(t,{CX:()=>v,GA:()=>b,Gv:()=>p,Lj:()=>k,M3:()=>_,SO:()=>E,SX:()=>d,VP:()=>g,Zy:()=>R,cJ:()=>q,ed:()=>w,pO:()=>S,pR:()=>c,r4:()=>x,xJ:()=>m,xv:()=>y,zk:()=>f});var n=r(84770),s=r.n(n),i=r(31518),o=r(2723),a=r(79835);let l=new o.R(process.env.RESEND_API_KEY),u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,i.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",u,{auth:{autoRefreshToken:!1,persistSession:!1}});function d(){return Math.floor(1e5+9e5*Math.random()).toString()}function h(e){return new Promise((t,r)=>{let n=s().randomBytes(16).toString("hex");s().pbkdf2(e,n,1e4,64,"sha512",(e,s)=>{if(e){r(e);return}t(`10000:${n}:${s.toString("hex")}`)})})}function p(e,t){return new Promise((r,n)=>{let[i,o,a]=t.split(":"),l=parseInt(i);s().pbkdf2(e,o,l,64,"sha512",(e,t)=>{if(e){n(e);return}r(t.toString("hex")===a)})})}async function f(e,t){try{let{data:r,error:n}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,t)});if(n)return console.error("Error sending verification email:",n),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function m(e,t,r=24){let n=new Date;n.setHours(n.getHours()+r);let{data:s,error:i}=await c.from("verification_codes").insert({email:e,code:t,expires_at:n.toISOString()}).select();if(i)throw console.error("Error storing verification code:",i),Error("Failed to store verification code");return s}async function g(e,t){let{data:r,error:n}=await c.from("verification_codes").select("*").eq("email",e).eq("code",t).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(n)return console.error("Error verifying code:",n),!1;if(!r||0===r.length)return!1;let{error:s}=await c.from("verification_codes").update({used:!0}).eq("id",r[0].id);return s&&console.error("Error marking code as used:",s),!0}async function y(e,t,r,n){try{let{error:s}=await c.from("profiles").insert({id:e,email:t,first_name:r,last_name:n,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});s&&console.error("Error creating user profile:",s)}catch(e){console.error("Exception creating user profile:",e)}}async function x(e,t,r,n){let s=await h(t),{data:i,error:o}=await c.from("custom_users").insert({email:e,password_hash:s,first_name:r,last_name:n}).select();if(o)throw console.error("Error creating user:",o),Error("Failed to create user");return await y(i[0].id,e,r,n),i[0]}async function v(e){let{data:t,error:r}=await c.from("custom_users").select("*").eq("email",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by email:",r),Error("Failed to get user")}return t}async function b(e){let{data:t,error:r}=await c.from("custom_users").select("*").eq("id",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by ID:",r),Error("Failed to get user")}return t}async function w(e,t=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let r=s().randomBytes(32).toString("hex"),n=new Date;n.setDate(n.getDate()+t);let{data:i,error:o}=await c.from("user_sessions").insert({user_id:e,session_token:r,expires_at:n.toISOString()}).select();if(o)throw console.error("Error creating session:",o),Error("Failed to create session");return{sessionToken:r,expiresAt:n,userId:e}}async function _(e){try{let{data:t,error:r}=await c.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(r){if("PGRST116"===r.code)return null;return console.error("Error getting session by token:",r),null}return t}catch(e){return console.error("Exception getting session by token:",e),null}}async function E(e){let{error:t}=await c.from("user_sessions").delete().eq("session_token",e);if(t)throw console.error("Error deleting session:",t),Error("Failed to delete session");return!0}async function S(e){let{error:t}=await c.from("custom_users").update({email_verified:!0}).eq("email",e);if(t)throw console.error("Error marking email as verified:",t),Error("Failed to mark email as verified");return!0}async function k(e,t,r){let{error:n}=await c.from("custom_users").update({first_name:t,last_name:r}).eq("id",e);if(n)throw console.error("Error updating user profile:",n),Error("Failed to update user profile");return!0}async function q(e,t){let{error:r}=await c.from("custom_users").update({email:t,email_verified:!1}).eq("id",e);if(r)throw console.error("Error updating user email:",r),Error("Failed to update user email");return!0}async function R(e,t){let r=await h(t),{error:n}=await c.from("custom_users").update({password_hash:r}).eq("id",e);if(n)throw console.error("Error updating user password:",n),Error("Failed to update user password");return!0}},2723:(e,t,r)=>{r.d(t,{R:()=>E});var n=Object.defineProperty,s=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&u(e,r,t[r]);if(o)for(var r of o(t))l.call(t,r)&&u(e,r,t[r]);return e},d=(e,t)=>s(e,i(t)),h=(e,t,r)=>new Promise((n,s)=>{var i=e=>{try{a(r.next(e))}catch(e){s(e)}},o=e=>{try{a(r.throw(e))}catch(e){s(e)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(i,o);a((r=r.apply(e,t)).next())}),p=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},f=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function m(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var g=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){let n=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}n.push(m(t))}return yield this.resend.post("/emails/batch",n,t)})}},y=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return h(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return h(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return h(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},x=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts/${e.id}`)})}update(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},v=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return h(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},b=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(7044).then(r.bind(r,97044));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",m(e),t)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return h(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},w="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",_="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.2.0",E=class{constructor(e){if(this.key=e,this.apiKeys=new p(this),this.audiences=new f(this),this.batch=new g(this),this.broadcasts=new y(this),this.contacts=new x(this),this.domains=new v(this),this.emails=new b(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":_,"Content-Type":"application/json"})}fetchRequest(e){return h(this,arguments,function*(e,t={}){try{let r=yield fetch(`${w}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:d(c({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return h(this,arguments,function*(e,t,r={}){let n=c({method:"POST",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}get(e){return h(this,arguments,function*(e,t={}){let r=c({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return h(this,arguments,function*(e,t,r={}){let n=c({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}patch(e,t){return h(this,arguments,function*(e,t,r={}){let n=c({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}delete(e,t){return h(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,135],()=>r(30117));module.exports=n})();