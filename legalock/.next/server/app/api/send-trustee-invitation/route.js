"use strict";(()=>{var e={};e.id=5347,e.ids=[5347],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},93364:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>h,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var o={};r.r(o),r.d(o,{POST:()=>p});var i=r(49303),s=r(88716),n=r(60670),a=r(87070),l=r(71615),u=r(43478),c=r(1926);async function p(e){try{let t=(0,l.cookies)(),r=t.get("session_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await (0,u.M3)(r);if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let i=await (0,u.GA)(o.user_id);if(!i)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=(0,c.S6)(),{inviterName:n,inviterEmail:p,trusteeName:d,trusteeEmail:f,permissions:g,message:m,inviteId:x}=await e.json();if(!f||!x)return a.NextResponse.json({error:"Missing required fields"},{status:400});process.env.NEXT_PUBLIC_APP_URL;try{let e=await fetch("https://ccwvtcudztphwwzzgwvg.supabase.co/functions/v1/send-trustee-invitation",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`},body:JSON.stringify({inviterName:n,inviterEmail:p,trusteeName:d,trusteeEmail:f,permissions:g})});if(!e.ok){let t=await e.json();return console.error("Error from Supabase function:",t),a.NextResponse.json({error:"Failed to send invitation email"},{status:500})}console.log("Email sent successfully via Supabase function")}catch(e){return console.error("Error calling Supabase function:",e),a.NextResponse.json({error:"Failed to send invitation email"},{status:500})}let{data:h,error:y}=await s.from("auth_users_mapping").select("auth_user_id").eq("custom_user_id",i.id).single();if(y||!h)return console.error("Error getting auth user:",y),a.NextResponse.json({error:"Failed to get auth user mapping"},{status:500});let{error:w}=await s.from("trustees").update({invitation_sent_at:new Date().toISOString()}).eq("id",x);if(w)throw w;return a.NextResponse.json({success:!0})}catch(e){return console.error("Error sending trustee invitation:",e),a.NextResponse.json({error:e.message||"Failed to send invitation"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/send-trustee-invitation/route",pathname:"/api/send-trustee-invitation",filename:"route",bundlePath:"app/api/send-trustee-invitation/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/send-trustee-invitation/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:m}=d,x="/api/send-trustee-invitation/route";function h(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}},79835:(e,t,r)=>{r.d(t,{C:()=>o});function o(e,t){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${t?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${t}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,t,r)=>{r.d(t,{CX:()=>w,GA:()=>v,Gv:()=>f,Lj:()=>b,M3:()=>S,SO:()=>E,SX:()=>p,VP:()=>x,Zy:()=>q,cJ:()=>k,ed:()=>_,pO:()=>I,pR:()=>c,r4:()=>y,xJ:()=>m,xv:()=>h,zk:()=>g});var o=r(84770),i=r.n(o),s=r(31518),n=r(2723),a=r(79835);let l=new n.R(process.env.RESEND_API_KEY),u=process.env.SUPABASE_SERVICE_ROLE_KEY,c=(0,s.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",u,{auth:{autoRefreshToken:!1,persistSession:!1}});function p(){return Math.floor(1e5+9e5*Math.random()).toString()}function d(e){return new Promise((t,r)=>{let o=i().randomBytes(16).toString("hex");i().pbkdf2(e,o,1e4,64,"sha512",(e,i)=>{if(e){r(e);return}t(`10000:${o}:${i.toString("hex")}`)})})}function f(e,t){return new Promise((r,o)=>{let[s,n,a]=t.split(":"),l=parseInt(s);i().pbkdf2(e,n,l,64,"sha512",(e,t)=>{if(e){o(e);return}r(t.toString("hex")===a)})})}async function g(e,t){try{let{data:r,error:o}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,t)});if(o)return console.error("Error sending verification email:",o),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function m(e,t,r=24){let o=new Date;o.setHours(o.getHours()+r);let{data:i,error:s}=await c.from("verification_codes").insert({email:e,code:t,expires_at:o.toISOString()}).select();if(s)throw console.error("Error storing verification code:",s),Error("Failed to store verification code");return i}async function x(e,t){let{data:r,error:o}=await c.from("verification_codes").select("*").eq("email",e).eq("code",t).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(o)return console.error("Error verifying code:",o),!1;if(!r||0===r.length)return!1;let{error:i}=await c.from("verification_codes").update({used:!0}).eq("id",r[0].id);return i&&console.error("Error marking code as used:",i),!0}async function h(e,t,r,o){try{let{error:i}=await c.from("profiles").insert({id:e,email:t,first_name:r,last_name:o,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});i&&console.error("Error creating user profile:",i)}catch(e){console.error("Exception creating user profile:",e)}}async function y(e,t,r,o){let i=await d(t),{data:s,error:n}=await c.from("custom_users").insert({email:e,password_hash:i,first_name:r,last_name:o}).select();if(n)throw console.error("Error creating user:",n),Error("Failed to create user");return await h(s[0].id,e,r,o),s[0]}async function w(e){let{data:t,error:r}=await c.from("custom_users").select("*").eq("email",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by email:",r),Error("Failed to get user")}return t}async function v(e){let{data:t,error:r}=await c.from("custom_users").select("*").eq("id",e).single();if(r){if("PGRST116"===r.code)return null;throw console.error("Error getting user by ID:",r),Error("Failed to get user")}return t}async function _(e,t=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let r=i().randomBytes(32).toString("hex"),o=new Date;o.setDate(o.getDate()+t);let{data:s,error:n}=await c.from("user_sessions").insert({user_id:e,session_token:r,expires_at:o.toISOString()}).select();if(n)throw console.error("Error creating session:",n),Error("Failed to create session");return{sessionToken:r,expiresAt:o,userId:e}}async function S(e){try{let{data:t,error:r}=await c.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(r){if("PGRST116"===r.code)return null;return console.error("Error getting session by token:",r),null}return t}catch(e){return console.error("Exception getting session by token:",e),null}}async function E(e){let{error:t}=await c.from("user_sessions").delete().eq("session_token",e);if(t)throw console.error("Error deleting session:",t),Error("Failed to delete session");return!0}async function I(e){let{error:t}=await c.from("custom_users").update({email_verified:!0}).eq("email",e);if(t)throw console.error("Error marking email as verified:",t),Error("Failed to mark email as verified");return!0}async function b(e,t,r){let{error:o}=await c.from("custom_users").update({first_name:t,last_name:r}).eq("id",e);if(o)throw console.error("Error updating user profile:",o),Error("Failed to update user profile");return!0}async function k(e,t){let{error:r}=await c.from("custom_users").update({email:t,email_verified:!1}).eq("id",e);if(r)throw console.error("Error updating user email:",r),Error("Failed to update user email");return!0}async function q(e,t){let r=await d(t),{error:o}=await c.from("custom_users").update({password_hash:r}).eq("id",e);if(o)throw console.error("Error updating user password:",o),Error("Failed to update user password");return!0}},1926:(e,t,r)=>{r.d(t,{S6:()=>a});var o=r(31518);let i="https://ccwvtcudztphwwzzgwvg.supabase.co",s=null,n=null;function a(){if(n)return n;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";return n=(0,o.eI)(i,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}(0,o.eI)(i,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}}),a()}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,135,9322],()=>r(93364));module.exports=o})();