"use strict";(()=>{var e={};e.id=6617,e.ids=[6617],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},10586:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>q,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>l,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>n});var o=t(49303),u=t(88716),a=t(60670),i=t(87070);let p=(0,t(31518).eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});async function n(e){try{let{codeId:r}=await e.json();if(!r)return i.NextResponse.json({error:"Code ID is required"},{status:400});let{error:t}=await p.from("verification_codes").update({used:!0}).eq("id",r);if(t)return console.error("Error marking code as used:",t),i.NextResponse.json({error:t.message},{status:500});return i.NextResponse.json({success:!0})}catch(e){return console.error("Unexpected error in mark-code-used:",e),i.NextResponse.json({error:e.message||"An error occurred"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:u.x.APP_ROUTE,page:"/api/mark-code-used/route",pathname:"/api/mark-code-used",filename:"route",bundlePath:"app/api/mark-code-used/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/mark-code-used/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:c,staticGenerationAsyncStorage:x,serverHooks:l}=d,m="/api/mark-code-used/route";function q(){return(0,a.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:x})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,135],()=>t(10586));module.exports=s})();