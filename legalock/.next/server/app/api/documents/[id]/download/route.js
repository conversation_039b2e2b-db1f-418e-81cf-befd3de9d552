"use strict";(()=>{var e={};e.id=9374,e.ids=[9374],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61212:e=>{e.exports=require("async_hooks")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63757:e=>{e.exports=import("prettier/plugins/html")},45747:e=>{e.exports=import("prettier/standalone")},84492:e=>{e.exports=require("node:stream")},27850:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>y,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>f});var o={};t.r(o),t.d(o,{GET:()=>p});var s=t(49303),n=t(88716),i=t(60670),a=t(87070),l=t(71615),c=t(43478),u=t(1926);async function p(e,{params:r}){try{let e=r.id,t=(0,l.cookies)(),o=t.get("session_token")?.value;if(!o)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=await (0,c.M3)(o);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let n=await (0,c.GA)(s.user_id);if(!n)return a.NextResponse.json({error:"Unauthorized"},{status:401});let i=(0,u.S6)(),{data:p,error:d}=await i.from("vault_documents").select("*").eq("id",e).single();if(d)return console.error("Error fetching document:",d),a.NextResponse.json({error:"Document not found"},{status:404});if(p.user_id!==n.id)return a.NextResponse.json({error:"You do not have permission to access this document"},{status:403});let{data:m,error:f}=await i.storage.from("documents").download(p.file_path);if(f)return console.error("Error downloading file:",f),a.NextResponse.json({error:`Failed to download file: ${f.message}`},{status:500});if(!m)return a.NextResponse.json({error:"No data returned from storage"},{status:500});let g=new Headers;return g.set("Content-Type",p.file_type||"application/octet-stream"),g.set("Content-Disposition",`attachment; filename="${p.name}"`),g.set("X-Document-Name",p.name),g.set("X-Document-Path",p.file_path),g.set("X-Document-Type",p.file_type||"application/octet-stream"),g.set("X-Encryption-Key",p.encryption_key),new a.NextResponse(m,{status:200,headers:g})}catch(e){return console.error("Error in document download API:",e),a.NextResponse.json({error:e.message||"An error occurred"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/documents/[id]/download/route",pathname:"/api/documents/[id]/download",filename:"route",bundlePath:"app/api/documents/[id]/download/route"},resolvedPagePath:"/Users/<USER>/Desktop/Legalock/legalock/src/app/api/documents/[id]/download/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:g}=d,x="/api/documents/[id]/download/route";function y(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:f})}},79835:(e,r,t)=>{t.d(r,{C:()=>o});function o(e,r){return`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
        <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
      </div>

      <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
        Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
      </p>

      <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
        ${r?`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code:
        </p>
        <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
          ${r}
        </p>
        `:`
        <p style="font-size: 16px; margin-bottom: 16px;">
          Your verification code has been sent to:
        </p>
        <p style="font-size: 18px; font-weight: bold; margin-bottom: 16px;">
          ${e}
        </p>
        `}
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          This code will expire in 24 hours.
        </p>
      </div>

      <div style="margin: 32px 0; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 16px;">
          After verification, you'll have access to all Legalock features:
        </p>
        <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
          <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
          <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
          <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
          <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
          <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
        </ul>
      </div>

      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `}},43478:(e,r,t)=>{t.d(r,{CX:()=>w,GA:()=>_,Gv:()=>m,Lj:()=>b,M3:()=>I,SO:()=>E,SX:()=>p,VP:()=>x,Zy:()=>q,cJ:()=>k,ed:()=>v,pO:()=>S,pR:()=>u,r4:()=>h,xJ:()=>g,xv:()=>y,zk:()=>f});var o=t(84770),s=t.n(o),n=t(31518),i=t(2723),a=t(79835);let l=new i.R(process.env.RESEND_API_KEY),c=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,n.eI)("https://ccwvtcudztphwwzzgwvg.supabase.co",c,{auth:{autoRefreshToken:!1,persistSession:!1}});function p(){return Math.floor(1e5+9e5*Math.random()).toString()}function d(e){return new Promise((r,t)=>{let o=s().randomBytes(16).toString("hex");s().pbkdf2(e,o,1e4,64,"sha512",(e,s)=>{if(e){t(e);return}r(`10000:${o}:${s.toString("hex")}`)})})}function m(e,r){return new Promise((t,o)=>{let[n,i,a]=r.split(":"),l=parseInt(n);s().pbkdf2(e,i,l,64,"sha512",(e,r)=>{if(e){o(e);return}t(r.toString("hex")===a)})})}async function f(e,r){try{let{data:t,error:o}=await l.emails.send({from:"Legalock <<EMAIL>>",to:[e],subject:"Verify your Legalock account",html:(0,a.C)(e,r)});if(o)return console.error("Error sending verification email:",o),!1;return!0}catch(e){return console.error("Exception sending verification email:",e),!1}}async function g(e,r,t=24){let o=new Date;o.setHours(o.getHours()+t);let{data:s,error:n}=await u.from("verification_codes").insert({email:e,code:r,expires_at:o.toISOString()}).select();if(n)throw console.error("Error storing verification code:",n),Error("Failed to store verification code");return s}async function x(e,r){let{data:t,error:o}=await u.from("verification_codes").select("*").eq("email",e).eq("code",r).eq("used",!1).gte("expires_at",new Date().toISOString()).order("created_at",{ascending:!1}).limit(1);if(o)return console.error("Error verifying code:",o),!1;if(!t||0===t.length)return!1;let{error:s}=await u.from("verification_codes").update({used:!0}).eq("id",t[0].id);return s&&console.error("Error marking code as used:",s),!0}async function y(e,r,t,o){try{let{error:s}=await u.from("profiles").insert({id:e,email:r,first_name:t,last_name:o,subscription_tier:"free",subscription_status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});s&&console.error("Error creating user profile:",s)}catch(e){console.error("Exception creating user profile:",e)}}async function h(e,r,t,o){let s=await d(r),{data:n,error:i}=await u.from("custom_users").insert({email:e,password_hash:s,first_name:t,last_name:o}).select();if(i)throw console.error("Error creating user:",i),Error("Failed to create user");return await y(n[0].id,e,t,o),n[0]}async function w(e){let{data:r,error:t}=await u.from("custom_users").select("*").eq("email",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by email:",t),Error("Failed to get user")}return r}async function _(e){let{data:r,error:t}=await u.from("custom_users").select("*").eq("id",e).single();if(t){if("PGRST116"===t.code)return null;throw console.error("Error getting user by ID:",t),Error("Failed to get user")}return r}async function v(e,r=parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS||"7")){let t=s().randomBytes(32).toString("hex"),o=new Date;o.setDate(o.getDate()+r);let{data:n,error:i}=await u.from("user_sessions").insert({user_id:e,session_token:t,expires_at:o.toISOString()}).select();if(i)throw console.error("Error creating session:",i),Error("Failed to create session");return{sessionToken:t,expiresAt:o,userId:e}}async function I(e){try{let{data:r,error:t}=await u.from("user_sessions").select("*").eq("session_token",e).gte("expires_at",new Date().toISOString()).single();if(t){if("PGRST116"===t.code)return null;return console.error("Error getting session by token:",t),null}return r}catch(e){return console.error("Exception getting session by token:",e),null}}async function E(e){let{error:r}=await u.from("user_sessions").delete().eq("session_token",e);if(r)throw console.error("Error deleting session:",r),Error("Failed to delete session");return!0}async function S(e){let{error:r}=await u.from("custom_users").update({email_verified:!0}).eq("email",e);if(r)throw console.error("Error marking email as verified:",r),Error("Failed to mark email as verified");return!0}async function b(e,r,t){let{error:o}=await u.from("custom_users").update({first_name:r,last_name:t}).eq("id",e);if(o)throw console.error("Error updating user profile:",o),Error("Failed to update user profile");return!0}async function k(e,r){let{error:t}=await u.from("custom_users").update({email:r,email_verified:!1}).eq("id",e);if(t)throw console.error("Error updating user email:",t),Error("Failed to update user email");return!0}async function q(e,r){let t=await d(r),{error:o}=await u.from("custom_users").update({password_hash:t}).eq("id",e);if(o)throw console.error("Error updating user password:",o),Error("Failed to update user password");return!0}},1926:(e,r,t)=>{t.d(r,{S6:()=>a});var o=t(31518);let s="https://ccwvtcudztphwwzzgwvg.supabase.co",n=null,i=null;function a(){if(i)return i;let e=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";return i=(0,o.eI)(s,e,{auth:{autoRefreshToken:!1,persistSession:!1}})}(0,o.eI)(s,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}}),a()}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,135,9322],()=>t(27850));module.exports=o})();