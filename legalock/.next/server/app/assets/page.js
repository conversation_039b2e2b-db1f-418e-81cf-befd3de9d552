(()=>{var t={};t.id=3650,t.ids=[3650,2777],t.modules={72934:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:t=>{"use strict";t.exports=require("buffer")},84770:t=>{"use strict";t.exports=require("crypto")},17702:t=>{"use strict";t.exports=require("events")},32615:t=>{"use strict";t.exports=require("http")},35240:t=>{"use strict";t.exports=require("https")},98216:t=>{"use strict";t.exports=require("net")},68621:t=>{"use strict";t.exports=require("punycode")},76162:t=>{"use strict";t.exports=require("stream")},82452:t=>{"use strict";t.exports=require("tls")},17360:t=>{"use strict";t.exports=require("url")},71568:t=>{"use strict";t.exports=require("zlib")},413:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>r.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>h}),i(31164),i(56752),i(35866);var s=i(23191),a=i(88716),n=i(37922),r=i.n(n),o=i(95231),l={};for(let t in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h=["",{children:["assets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,31164)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/assets/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/assets/page.tsx"],d="/assets/page",u={require:i,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/assets/page",pathname:"/assets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},46863:(t,e,i)=>{Promise.resolve().then(i.bind(i,39352))},37358:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41291:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},28916:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},39572:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},71709:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},71810:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},48705:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},44389:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},88378:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},98091:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},94019:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});let s=(0,i(62881).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},39352:(t,e,i)=>{"use strict";let s;i.r(e),i.d(e,{default:()=>ns});var a=i(10326),n=i(17577),r=i(68136);let o=[{value:"real_estate",label:"Real Estate",icon:"Home",description:"Properties, land, and buildings"},{value:"financial_account",label:"Financial Account",icon:"DollarSign",description:"Bank accounts, investment accounts, retirement accounts"},{value:"vehicle",label:"Vehicle",icon:"Car",description:"Cars, boats, motorcycles, and other vehicles"},{value:"jewelry",label:"Jewelry",icon:"Diamond",description:"Valuable jewelry and watches"},{value:"art",label:"Art & Collectibles",icon:"Image",description:"Artwork, paintings, sculptures, and collectibles"},{value:"antique",label:"Antiques",icon:"Clock",description:"Antiques and vintage items"},{value:"financial_instrument",label:"Financial Instrument",icon:"FileText",description:"Stocks, bonds, certificates, insurance policies"},{value:"business",label:"Business",icon:"Briefcase",description:"Business assets and ownership interests"},{value:"heirloom",label:"Family Heirloom",icon:"Gift",description:"Family heirlooms and items of sentimental value"},{value:"other",label:"Other Asset",icon:"Package",description:"Other assets not listed above"}];var l=i(48705),h=i(44389),c=i(98091),d=i(91664),u=i(34793);!function(){var t=Error("Cannot find module '@/components/ui/sidebar'");throw t.code="MODULE_NOT_FOUND",t}();let f=({children:t})=>a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/sidebar'");throw t.code="MODULE_NOT_FOUND",t}()),{children:a.jsx("div",{className:"min-h-screen flex w-full",children:(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[a.jsx(u.Z,{}),a.jsx("main",{className:"flex-1 bg-gray-50",children:a.jsx("div",{className:"page-container",children:t})}),a.jsx("footer",{className:"py-4 px-6 border-t border-gray-200 text-center text-sm text-gray-500",children:(0,a.jsxs)("p",{children:["Legalock \xa9 ",new Date().getFullYear()," - Your digital legacy secured"]})})]})})});var p=i(88270),g=i(41291),m=i(71709),x=i(39572),b=i(94019),_=i(51223);let y=({onImageSelected:t,onImageUploaded:e,maxSizeMB:i=5})=>{let[s,r]=(0,n.useState)(null),[o,l]=(0,n.useState)(null),[h,c]=(0,n.useState)(null),[u,f]=(0,n.useState)(!1),p=(0,n.useRef)(null),y=1048576*i,v=["image/jpeg","image/png","image/gif","image/webp"],w=t=>t.size>y?(c(`File size exceeds the maximum limit of ${i}MB`),!1):v.includes(t.type)?(c(null),!0):(c("File type not supported. Please upload an image (JPEG, PNG, GIF, WEBP)"),!1),M=async t=>{f(!0);try{let i=new FormData;i.append("file",t),i.append("bucket","asset-images"),i.append("path",`${Date.now()}_${t.name}`);let s=await fetch("/api/storage/upload",{method:"POST",body:i});if(!s.ok)throw Error("Failed to upload image");let a=await s.json();e(a.fullPath)}catch(t){console.error("Error uploading image:",t),c("Failed to upload image. Please try again.")}finally{f(!1)}};return a.jsx("div",{className:"w-full",children:s?(0,a.jsxs)("div",{className:"border rounded-lg p-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(x.Z,{className:"h-4 w-4 text-primary mr-2"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium truncate max-w-[200px] md:max-w-xs",children:s.name}),a.jsx("p",{className:"text-xs text-gray-500",children:(0,_.formatFileSize)(s.size)})]})]}),(0,a.jsxs)(d.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 text-gray-500 hover:text-gray-700",onClick:()=>{o&&URL.revokeObjectURL(o),r(null),l(null),p.current&&(p.current.value="")},disabled:u,children:[a.jsx(b.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Remove file"})]})]}),o&&(0,a.jsxs)("div",{className:"relative h-32 w-full bg-gray-100 overflow-hidden rounded",children:[a.jsx("img",{src:o,alt:"Preview",className:"w-full h-full object-cover"}),u&&a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:a.jsx("div",{className:"animate-spin w-6 h-6 border-2 border-white border-t-transparent rounded-full"})})]})]}):(0,a.jsxs)("div",{className:`border-2 border-dashed ${h?"border-red-300 bg-red-50":"border-gray-300 hover:bg-gray-50"} rounded-lg p-4 text-center cursor-pointer transition-colors`,onClick:()=>p.current?.click(),onDragOver:t=>{t.preventDefault(),t.stopPropagation()},onDrop:e=>{e.preventDefault(),e.stopPropagation();let i=e.dataTransfer.files;if(i&&i.length>0){let e=i[0];w(e)&&(r(e),l(URL.createObjectURL(e)),t(e),M(e))}},children:[h?a.jsx(g.Z,{className:"mx-auto h-8 w-8 text-red-400"}):a.jsx(m.Z,{className:"mx-auto h-8 w-8 text-gray-400"}),a.jsx("p",{className:`mt-2 text-sm ${h?"text-red-600 font-medium":"text-gray-600"}`,children:h||"Drag and drop an image, or click to select"}),(0,a.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["JPEG, PNG, GIF, WEBP up to ",i,"MB"]}),a.jsx("input",{type:"file",className:"hidden",onChange:e=>{let i=e.target.files;if(i&&i.length>0){let e=i[0];w(e)?(r(e),l(URL.createObjectURL(e)),t(e),M(e)):p.current&&(p.current.value="")}},ref:p,accept:v.join(",")})]})})};var v=i(74723),w=i(74064),M=i(27256),O=i(41190),k=i(37358),D=i(65720),j=i(9969);(function(){var t=Error("Cannot find module '@/components/ui/textarea'");throw t.code="MODULE_NOT_FOUND",t})(),function(){var t=Error("Cannot find module '@/components/ui/calendar'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/components/ui/popover'");throw t.code="MODULE_NOT_FOUND",t}();let N=M.Ry({name:M.Z_().min(2,{message:"Asset name must be at least 2 characters"}),type:M.Km(["physical","digital"]),category:M.Z_().min(1,{message:"Please select a category"}),description:M.Z_().optional(),location:M.Z_().optional(),value:M.Rx().optional().nullable(),currency:M.Z_().default("USD"),acquisition_date:M.hT().optional().nullable(),account_details:M.Z_().optional(),image_url:M.Z_().optional(),notes:M.Z_().optional()});function C({onSubmit:t,onCancel:e,defaultValues:i}){let[s,r]=(0,n.useState)(i?.image_url),l=(0,v.cI)({resolver:(0,w.F)(N),defaultValues:{name:i?.name||"",type:"physical",category:i?.category||"",description:i?.description||"",location:i?.location||"",value:i?.value||void 0,currency:i?.currency||"USD",acquisition_date:i?.acquisition_date?new Date(i.acquisition_date):null,account_details:i?.account_details||"",image_url:i?.image_url||"",notes:i?.notes||""}}),h=()=>o;return a.jsx(j.l0,{...l,children:(0,a.jsxs)("form",{onSubmit:l.handleSubmit(e=>{t({...e,image_url:s}),l.reset({name:"",type:"physical",category:"",description:"",location:"",value:void 0,currency:"USD",acquisition_date:null,account_details:"",image_url:"",notes:""}),r(void 0)}),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsx(j.Wi,{control:l.control,name:"name",render:({field:t})=>(0,a.jsxs)(j.xJ,{children:[a.jsx(j.lX,{children:"Asset Name*"}),a.jsx(j.NI,{children:a.jsx(O.I,{placeholder:"Enter asset name",...t})}),a.jsx(j.zG,{})]})}),a.jsx("input",{type:"hidden",...l.register("type"),value:"physical"}),a.jsx(j.Wi,{control:l.control,name:"category",render:({field:t})=>(0,a.jsxs)(j.xJ,{children:[a.jsx(j.lX,{children:"Category*"}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{onValueChange:t.onChange,defaultValue:t.value,children:[a.jsx(j.NI,{children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{placeholder:"Select a category"})})}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:h().map(t=>a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:t.value,children:t.label},t.value))})]}),a.jsx(j.zG,{})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(j.Wi,{control:l.control,name:"value",render:({field:t})=>(0,a.jsxs)(j.xJ,{className:"flex-1",children:[a.jsx(j.lX,{children:"Estimated Value"}),a.jsx(j.NI,{children:a.jsx(O.I,{type:"text",placeholder:"0.00",...t,value:void 0===t.value||null===t.value?"":t.value,onChange:e=>{let i=e.target.value.replace(/[^0-9.]/g,"");t.onChange(""===i?void 0:parseFloat(i))}})}),a.jsx(j.zG,{})]})}),a.jsx(j.Wi,{control:l.control,name:"currency",render:({field:t})=>(0,a.jsxs)(j.xJ,{className:"w-32",children:[a.jsx(j.lX,{children:"Currency"}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{onValueChange:t.onChange,defaultValue:t.value,children:[a.jsx(j.NI,{children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{placeholder:"USD"})})}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:"Common Currencies"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"USD",children:"$ USD - US Dollar"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"EUR",children:"€ EUR - Euro"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"GBP",children:"\xa3 GBP - British Pound"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"JPY",children:"\xa5 JPY - Japanese Yen"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"CAD",children:"C$ CAD - Canadian Dollar"})]}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:"Other Currencies"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"AUD",children:"A$ AUD - Australian Dollar"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"CHF",children:"Fr CHF - Swiss Franc"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"CNY",children:"\xa5 CNY - Chinese Yuan"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"INR",children:"₹ INR - Indian Rupee"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"BRL",children:"R$ BRL - Brazilian Real"})]})]})]}),a.jsx(j.zG,{})]})})]}),a.jsx(j.Wi,{control:l.control,name:"acquisition_date",render:({field:t})=>(0,a.jsxs)(j.xJ,{className:"flex flex-col",children:[a.jsx(j.lX,{children:"Acquisition Date"}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/popover'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/popover'");throw t.code="MODULE_NOT_FOUND",t}()),{asChild:!0,children:a.jsx(j.NI,{children:(0,a.jsxs)(d.z,{variant:"outline",className:`w-full pl-3 text-left font-normal ${t.value?"":"text-muted-foreground"}`,children:[t.value?(0,D.WU)(t.value,"PPP"):a.jsx("span",{children:"Pick a date"}),a.jsx(k.Z,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/popover'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"w-auto p-0",align:"start",children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/calendar'");throw t.code="MODULE_NOT_FOUND",t}()),{mode:"single",selected:t.value||void 0,onSelect:t.onChange,initialFocus:!0})})]}),a.jsx(j.pf,{children:"When you acquired this asset"}),a.jsx(j.zG,{})]})}),a.jsx(j.Wi,{control:l.control,name:"location",render:({field:t})=>(0,a.jsxs)(j.xJ,{children:[a.jsx(j.lX,{children:"Location"}),a.jsx(j.NI,{children:a.jsx(O.I,{placeholder:"Where this asset is stored or located",...t})}),a.jsx(j.zG,{})]})}),a.jsx(j.Wi,{control:l.control,name:"account_details",render:({field:t})=>(0,a.jsxs)(j.xJ,{children:[a.jsx(j.lX,{children:"Account Details"}),a.jsx(j.NI,{children:a.jsx(O.I,{placeholder:"Account number or other relevant information",...t})}),a.jsx(j.pf,{children:"Include any relevant account information. This will be securely stored."}),a.jsx(j.zG,{})]})})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx(j.lX,{children:"Asset Image"}),a.jsx("div",{className:"mt-2",children:a.jsx(y,{onImageSelected:t=>console.log("Image selected:",t.name),onImageUploaded:t=>{r(t),l.setValue("image_url",t)}})}),s&&a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Image will be saved with your asset"})]}),a.jsx(j.Wi,{control:l.control,name:"description",render:({field:t})=>(0,a.jsxs)(j.xJ,{children:[a.jsx(j.lX,{children:"Description"}),a.jsx(j.NI,{children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/textarea'");throw t.code="MODULE_NOT_FOUND",t}()),{placeholder:"Detailed description of the asset",className:"resize-none h-20",...t})}),a.jsx(j.zG,{})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[a.jsx(d.z,{type:"button",variant:"outline",onClick:e,children:"Cancel"}),a.jsx(d.z,{type:"submit",children:"Save Asset"})]})]})})}function E(t){return t+.5|0}let S=(t,e,i)=>Math.max(Math.min(t,i),e);function P(t){return S(E(2.55*t),0,255)}function T(t){return S(E(255*t),0,255)}function L(t){return S(E(t/2.55)/100,0,1)}function A(t){return S(E(100*t),0,100)}let F={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},I=[..."0123456789ABCDEF"],R=t=>I[15&t],U=t=>I[(240&t)>>4]+I[15&t],z=t=>(240&t)>>4==(15&t),V=t=>z(t.r)&&z(t.g)&&z(t.b)&&z(t.a),B=(t,e)=>t<255?e(t):"",W=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function H(t,e,i){let s=e*Math.min(i,1-i),a=(e,a=(e+t/30)%12)=>i-s*Math.max(Math.min(a-3,9-a,1),-1);return[a(0),a(8),a(4)]}function $(t,e,i){let s=(s,a=(s+t/60)%6)=>i-i*e*Math.max(Math.min(a,4-a,1),0);return[s(5),s(3),s(1)]}function Y(t,e,i){let s;let a=H(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)a[s]*=1-e-i,a[s]+=e;return a}function Z(t){let e,i,s;let a=t.r/255,n=t.g/255,r=t.b/255,o=Math.max(a,n,r),l=Math.min(a,n,r),h=(o+l)/2;return o!==l&&(s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=a===o?(n-r)/s+(n<r?6:0):n===o?(r-a)/s+2:(a-n)/s+4)+.5),[0|e,i||0,h]}function q(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(T)}function X(t){return(t%360+360)%360}let J={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},G={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},K=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,Q=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,tt=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function te(t,e,i){if(t){let s=Z(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),s=q(H,s,void 0,void 0),t.r=s[0],t.g=s[1],t.b=s[2]}}function ti(t,e){return t?Object.assign(e||{},t):t}function ts(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=T(t[3]))):(e=ti(t,{r:0,g:0,b:0,a:1})).a=T(e.a),e}class ta{constructor(t){let e;if(t instanceof ta)return t;let i=typeof t;"object"===i?e=ts(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*F[t[1]],g:255&17*F[t[2]],b:255&17*F[t[3]],a:5===i?17*F[t[4]]:255}:(7===i||9===i)&&(e={r:F[t[1]]<<4|F[t[2]],g:F[t[3]]<<4|F[t[4]],b:F[t[5]]<<4|F[t[6]],a:9===i?F[t[7]]<<4|F[t[8]]:255})),e}(t)||function(t){s||((s=function(){let t,e,i,s,a;let n={},r=Object.keys(G),o=Object.keys(J);for(t=0;t<r.length;t++){for(e=0,s=a=r[t];e<o.length;e++)i=o[e],a=a.replace(i,J[i]);i=parseInt(G[s],16),n[a]=[i>>16&255,i>>8&255,255&i]}return n}()).transparent=[0,0,0,0]);let e=s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s;let a=K.exec(t),n=255;if(a){if(a[7]!==e){let t=+a[7];n=a[8]?P(t):S(255*t,0,255)}return e=+a[1],i=+a[3],s=+a[5],{r:e=255&(a[2]?P(e):S(e,0,255)),g:i=255&(a[4]?P(i):S(i,0,255)),b:s=255&(a[6]?P(s):S(s,0,255)),a:n}}}(t):function(t){let e;let i=W.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?P(+i[5]):T(+i[5]));let a=X(+i[2]),n=+i[3]/100,r=+i[4]/100;return{r:(e="hwb"===i[1]?q(Y,a,n,r):"hsv"===i[1]?q($,a,n,r):q(H,a,n,r))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=ti(this._rgb);return t&&(t.a=L(t.a)),t}set rgb(t){this._rgb=ts(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${L(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=V(t=this._rgb)?R:U,t?"#"+e(t.r)+e(t.g)+e(t.b)+B(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=Z(t),i=e[0],s=A(e[1]),a=A(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${a}%, ${L(t.a)})`:`hsl(${i}, ${s}%, ${a}%)`}(this._rgb):void 0}mix(t,e){if(t){let i;let s=this.rgb,a=t.rgb,n=e===i?.5:e,r=2*n-1,o=s.a-a.a,l=((r*o==-1?r:(r+o)/(1+r*o))+1)/2;i=1-l,s.r=255&l*s.r+i*a.r+.5,s.g=255&l*s.g+i*a.g+.5,s.b=255&l*s.b+i*a.b+.5,s.a=n*s.a+(1-n)*a.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=tt(L(t.r)),a=tt(L(t.g)),n=tt(L(t.b));return{r:T(Q(s+i*(tt(L(e.r))-s))),g:T(Q(a+i*(tt(L(e.g))-a))),b:T(Q(n+i*(tt(L(e.b))-n))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new ta(this.rgb)}alpha(t){return this._rgb.a=T(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=E(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return te(this._rgb,2,t),this}darken(t){return te(this._rgb,2,-t),this}saturate(t){return te(this._rgb,1,t),this}desaturate(t){return te(this._rgb,1,-t),this}rotate(t){var e,i;return(i=Z(e=this._rgb))[0]=X(i[0]+t),i=q(H,i,void 0,void 0),e.r=i[0],e.g=i[1],e.b=i[2],this}}function tn(){}let tr=(()=>{let t=0;return()=>t++})();function to(t){return null==t}function tl(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function th(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function tc(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function td(t,e){return tc(t)?t:e}function tu(t,e){return void 0===t?e:t}let tf=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:+t/e,tp=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function tg(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function tm(t,e,i,s){let a,n,r;if(tl(t)){if(n=t.length,s)for(a=n-1;a>=0;a--)e.call(i,t[a],a);else for(a=0;a<n;a++)e.call(i,t[a],a)}else if(th(t))for(a=0,n=(r=Object.keys(t)).length;a<n;a++)e.call(i,t[r[a]],r[a])}function tx(t,e){let i,s,a,n;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(a=t[i],n=e[i],a.datasetIndex!==n.datasetIndex||a.index!==n.index)return!1;return!0}function tb(t){if(tl(t))return t.map(tb);if(th(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,a=0;for(;a<s;++a)e[i[a]]=tb(t[i[a]]);return e}return t}function t_(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function ty(t,e,i,s){if(!t_(t))return;let a=e[t],n=i[t];th(a)&&th(n)?tv(a,n,s):e[t]=tb(n)}function tv(t,e,i){let s;let a=tl(e)?e:[e],n=a.length;if(!th(t))return t;let r=(i=i||{}).merger||ty;for(let e=0;e<n;++e){if(!th(s=a[e]))continue;let n=Object.keys(s);for(let e=0,a=n.length;e<a;++e)r(n[e],t,s,i)}return t}function tw(t,e){return tv(t,e,{merger:tM})}function tM(t,e,i){if(!t_(t))return;let s=e[t],a=i[t];th(s)&&th(a)?tw(s,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=tb(a))}let tO={"":t=>t,x:t=>t.x,y:t=>t.y};function tk(t,e){return(tO[e]||(tO[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function tD(t){return t.charAt(0).toUpperCase()+t.slice(1)}let tj=t=>void 0!==t,tN=t=>"function"==typeof t,tC=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},tE=Math.PI,tS=2*tE,tP=tS+tE,tT=Number.POSITIVE_INFINITY,tL=tE/180,tA=tE/2,tF=tE/4,tI=2*tE/3,tR=Math.log10,tU=Math.sign;function tz(t,e,i){return Math.abs(t-e)<i}function tV(t){let e=Math.round(t),i=Math.pow(10,Math.floor(tR(t=tz(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function tB(t){return!("symbol"==typeof t||"object"==typeof t&&null!==t&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tW(t,e,i){let s,a,n;for(s=0,a=t.length;s<a;s++)isNaN(n=t[s][i])||(e.min=Math.min(e.min,n),e.max=Math.max(e.max,n))}function tH(t){return tE/180*t}function t$(t){if(!tc(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function tY(t,e){let i=e.x-t.x,s=e.y-t.y,a=Math.atan2(s,i);return a<-.5*tE&&(a+=tS),{angle:a,distance:Math.sqrt(i*i+s*s)}}function tZ(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tq(t,e){return(t-e+tP)%tS-tE}function tX(t){return(t%tS+tS)%tS}function tJ(t,e,i,s){let a=tX(t),n=tX(e),r=tX(i),o=tX(n-a),l=tX(r-a),h=tX(a-n),c=tX(a-r);return a===n||a===r||s&&n===r||o>l&&h<c}function tG(t,e,i){return Math.max(e,Math.min(i,t))}function tK(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function tQ(t,e,i){let s;i=i||(i=>t[i]<e);let a=t.length-1,n=0;for(;a-n>1;)i(s=n+a>>1)?n=s:a=s;return{lo:n,hi:a}}let t0=(t,e,i,s)=>tQ(t,i,s?s=>{let a=t[s][e];return a<i||a===i&&t[s+1][e]===i}:s=>t[s][e]<i),t1=(t,e,i)=>tQ(t,i,s=>t[s][e]>=i),t2=["push","pop","shift","splice","unshift"];function t5(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,a=s.indexOf(e);-1!==a&&s.splice(a,1),s.length>0||(t2.forEach(e=>{delete t[e]}),delete t._chartjs)}function t3(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let t4="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function t6(t,e){let i=[],s=!1;return function(...a){i=a,s||(s=!0,t4.call(window,()=>{s=!1,t.apply(e,i)}))}}let t8=t=>"start"===t?"left":"end"===t?"right":"center",t7=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,t9=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function et(t,e,i){let s=e.length,a=0,n=s;if(t._sorted){let{iScale:r,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=r.axis,{min:d,max:u,minDefined:f,maxDefined:p}=r.getUserBounds();if(f){if(a=Math.min(t0(l,c,d).lo,i?s:t0(e,c,r.getPixelForValue(d)).lo),h){let t=l.slice(0,a+1).reverse().findIndex(t=>!to(t[o.axis]));a-=Math.max(0,t)}a=tG(a,0,s-1)}if(p){let t=Math.max(t0(l,r.axis,u,!0).hi+1,i?0:t0(e,c,r.getPixelForValue(u),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!to(t[o.axis]));t+=Math.max(0,e)}n=tG(t,a,s)-a}else n=s-a}return{start:a,count:n}}function ee(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,a={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=a,!0;let n=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,a),n}let ei=t=>0===t||1===t,es=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*tS/i)),ea=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*tS/i)+1,en={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*tA)+1,easeOutSine:t=>Math.sin(t*tA),easeInOutSine:t=>-.5*(Math.cos(tE*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>ei(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>ei(t)?t:es(t,.075,.3),easeOutElastic:t=>ei(t)?t:ea(t,.075,.3),easeInOutElastic:t=>ei(t)?t:t<.5?.5*es(2*t,.1125,.45):.5+.5*ea(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*(((e*=1.525)+1)*t-e)*.5:.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-en.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*en.easeInBounce(2*t):.5*en.easeOutBounce(2*t-1)+.5};function er(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function eo(t){return er(t)?t:new ta(t)}function el(t){return er(t)?t:new ta(t).saturate(.5).darken(.1).hexString()}let eh=["x","y","borderWidth","radius","tension"],ec=["color","borderColor","backgroundColor"],ed=new Map;function eu(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=ed.get(i);return s||(s=new Intl.NumberFormat(t,e),ed.set(i,s)),s})(e,i).format(t)}let ef={values:t=>tl(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let a=this.chart.options.locale,n=t;if(i.length>1){let e;let a=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(a<1e-4||a>1e15)&&(s="scientific"),Math.abs(e=i.length>3?i[2].value-i[1].value:i[1].value-i[0].value)>=1&&t!==Math.floor(t)&&(e=t-Math.floor(t)),n=e}let r=tR(Math.abs(n)),o=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(l,this.options.ticks.format),eu(t,a,l)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(tR(t))))||e>.8*i.length?ef.numeric.call(this,t,e,i):""}};var ep={formatters:ef};let eg=Object.create(null),em=Object.create(null);function ex(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function eb(t,e,i){return"string"==typeof e?tv(ex(t,e),i):tv(ex(t,""),e)}class e_{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>el(e.backgroundColor),this.hoverBorderColor=(t,e)=>el(e.borderColor),this.hoverColor=(t,e)=>el(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return eb(this,t,e)}get(t){return ex(this,t)}describe(t,e){return eb(em,t,e)}override(t,e){return eb(eg,t,e)}route(t,e,i,s){let a=ex(this,t),n=ex(this,i),r="_"+e;Object.defineProperties(a,{[r]:{value:a[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[r],e=n[s];return th(t)?Object.assign({},e,t):tu(t,e)},set(t){this[r]=t}}})}apply(t){t.forEach(t=>t(this))}}var ey=new e_({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:ec},numbers:{type:"number",properties:eh}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ep.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function ev(t,e,i,s,a){let n=e[a];return n||(n=e[a]=t.measureText(a).width,i.push(a)),n>s&&(s=n),s}function ew(t,e,i){let s=t.currentDevicePixelRatio,a=0!==i?Math.max(i/2,.5):0;return Math.round((e-a)*s)/s+a}function eM(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function eO(t,e,i,s){ek(t,e,i,s,null)}function ek(t,e,i,s,a){let n,r,o,l,h,c,d,u;let f=e.pointStyle,p=e.rotation,g=e.radius,m=(p||0)*tL;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(n=f.toString())||"[object HTMLCanvasElement]"===n)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(g)&&!(g<=0)){switch(t.beginPath(),f){default:a?t.ellipse(i,s,a/2,g,0,0,tS):t.arc(i,s,g,0,tS),t.closePath();break;case"triangle":c=a?a/2:g,t.moveTo(i+Math.sin(m)*c,s-Math.cos(m)*g),m+=tI,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*g),m+=tI,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*g),t.closePath();break;case"rectRounded":h=.516*g,r=Math.cos(m+tF)*(l=g-h),d=Math.cos(m+tF)*(a?a/2-h:l),o=Math.sin(m+tF)*l,u=Math.sin(m+tF)*(a?a/2-h:l),t.arc(i-d,s-o,h,m-tE,m-tA),t.arc(i+u,s-r,h,m-tA,m),t.arc(i+d,s+o,h,m,m+tA),t.arc(i-u,s+r,h,m+tA,m+tE),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,c=a?a/2:l,t.rect(i-c,s-l,2*c,2*l);break}m+=tF;case"rectRot":d=Math.cos(m)*(a?a/2:g),r=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(a?a/2:g),t.moveTo(i-d,s-o),t.lineTo(i+u,s-r),t.lineTo(i+d,s+o),t.lineTo(i-u,s+r),t.closePath();break;case"crossRot":m+=tF;case"cross":d=Math.cos(m)*(a?a/2:g),r=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(a?a/2:g),t.moveTo(i-d,s-o),t.lineTo(i+d,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"star":d=Math.cos(m)*(a?a/2:g),r=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(a?a/2:g),t.moveTo(i-d,s-o),t.lineTo(i+d,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r),m+=tF,d=Math.cos(m)*(a?a/2:g),r=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(a?a/2:g),t.moveTo(i-d,s-o),t.lineTo(i+d,s+o),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"line":r=a?a/2:Math.cos(m)*g,o=Math.sin(m)*g,t.moveTo(i-r,s-o),t.lineTo(i+r,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(a?a/2:g),s+Math.sin(m)*g);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function eD(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function ej(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function eN(t){t.restore()}function eC(t,e,i,s,a){if(!e)return t.lineTo(i.x,i.y);if("middle"===a){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===a!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function eE(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function eS(t,e,i,s,a,n={}){let r,o;let l=tl(e)?e:[e],h=n.strokeWidth>0&&""!==n.strokeColor;for(t.save(),t.font=a.string,n.translation&&t.translate(n.translation[0],n.translation[1]),to(n.rotation)||t.rotate(n.rotation),n.color&&(t.fillStyle=n.color),n.textAlign&&(t.textAlign=n.textAlign),n.textBaseline&&(t.textBaseline=n.textBaseline),r=0;r<l.length;++r)o=l[r],n.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,n.backdrop),h&&(n.strokeColor&&(t.strokeStyle=n.strokeColor),to(n.strokeWidth)||(t.lineWidth=n.strokeWidth),t.strokeText(o,i,s,n.maxWidth)),t.fillText(o,i,s,n.maxWidth),function(t,e,i,s,a){if(a.strikethrough||a.underline){let n=t.measureText(s),r=e-n.actualBoundingBoxLeft,o=e+n.actualBoundingBoxRight,l=i-n.actualBoundingBoxAscent,h=i+n.actualBoundingBoxDescent,c=a.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=a.decorationWidth||2,t.moveTo(r,c),t.lineTo(o,c),t.stroke()}}(t,i,s,o,n),s+=Number(a.lineHeight);t.restore()}function eP(t,e){let{x:i,y:s,w:a,h:n,radius:r}=e;t.arc(i+r.topLeft,s+r.topLeft,r.topLeft,1.5*tE,tE,!0),t.lineTo(i,s+n-r.bottomLeft),t.arc(i+r.bottomLeft,s+n-r.bottomLeft,r.bottomLeft,tE,tA,!0),t.lineTo(i+a-r.bottomRight,s+n),t.arc(i+a-r.bottomRight,s+n-r.bottomRight,r.bottomRight,tA,0,!0),t.lineTo(i+a,s+r.topRight),t.arc(i+a-r.topRight,s+r.topRight,r.topRight,0,-tA,!0),t.lineTo(i+r.topLeft,s)}let eT=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,eL=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,eA=t=>+t||0;function eF(t,e){let i={},s=th(e),a=s?Object.keys(e):e,n=th(t)?s?i=>tu(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of a)i[t]=eA(n(t));return i}function eI(t){return eF(t,{top:"y",right:"x",bottom:"y",left:"x"})}function eR(t){return eF(t,["topLeft","topRight","bottomLeft","bottomRight"])}function eU(t){let e=eI(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ez(t,e){t=t||{},e=e||ey.font;let i=tu(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=tu(t.style,e.style);s&&!(""+s).match(eL)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let a={family:tu(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(eT);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(tu(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:tu(t.weight,e.weight),string:""};return a.string=!a||to(a.size)||to(a.family)?null:(a.style?a.style+" ":"")+(a.weight?a.weight+" ":"")+a.size+"px "+a.family,a}function eV(t,e,i,s){let a,n,r,o=!0;for(a=0,n=t.length;a<n;++a)if(void 0!==(r=t[a])&&(void 0!==e&&"function"==typeof r&&(r=r(e),o=!1),void 0!==i&&tl(r)&&(r=r[i%r.length],o=!1),void 0!==r))return s&&!o&&(s.cacheable=!1),r}function eB(t,e){return Object.assign(Object.create(t),e)}function eW(t,e=[""],i,s,a=()=>t[0]){let n=i||t;return void 0===s&&(s=eK("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:s,_getTarget:a,override:i=>eW([i,...t],e,n,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>eq(i,s,()=>(function(t,e,i,s){let a;for(let n of e)if(void 0!==(a=eK(eY(n,t),i)))return eZ(t,a)?eJ(i,s,t,a):a})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eQ(t).includes(e),ownKeys:t=>eQ(t),set(t,e,i){let s=t._storage||(t._storage=a());return t[e]=s[e]=i,delete t._keys,!0}})}function eH(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:e$(t,s),setContext:e=>eH(t,e,i,s),override:a=>eH(t.override(a),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>eq(t,e,()=>(function(t,e,i){let{_proxy:s,_context:a,_subProxy:n,_descriptors:r}=t,o=s[e];return tN(o)&&r.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:a,_context:n,_subProxy:r,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(n,r||s);return o.delete(t),eZ(t,l)&&(l=eJ(a._scopes,a,t,l)),l}(e,o,t,i)),tl(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:a,_context:n,_subProxy:r,_descriptors:o}=i;if(void 0!==n.index&&s(t))return e[n.index%e.length];if(th(e[0])){let i=e,s=a._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=eJ(s,a,t,l);e.push(eH(i,n,r&&r[t],o))}}return e}(e,o,t,r.isIndexable)),eZ(e,o)&&(o=eH(o,a,n&&n[e],r)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function e$(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:a=e.allKeys}=t;return{allKeys:a,scriptable:i,indexable:s,isScriptable:tN(i)?i:()=>i,isIndexable:tN(s)?s:()=>s}}let eY=(t,e)=>t?t+tD(e):e,eZ=(t,e)=>th(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eq(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let eX=(t,e)=>!0===t?e:"string"==typeof t?tk(e,t):void 0;function eJ(t,e,i,s){var a;let n=e._rootScopes,r=tN(a=e._fallback)?a(i,s):a,o=[...t,...n],l=new Set;l.add(s);let h=eG(l,o,i,r||i,s);return null!==h&&(void 0===r||r===i||null!==(h=eG(l,o,r,h,s)))&&eW(Array.from(l),[""],n,r,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let a=s[e];return tl(a)&&th(i)?i:a||{}})(e,i,s))}function eG(t,e,i,s,a){for(;i;)i=function(t,e,i,s,a){for(let r of e){let e=eX(i,r);if(e){var n;t.add(e);let r=tN(n=e._fallback)?n(i,a):n;if(void 0!==r&&r!==i&&r!==s)return r}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,a);return i}function eK(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function eQ(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function e0(t,e,i,s){let a,n,r;let{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(a=0;a<s;++a)r=e[n=a+i],h[a]={r:o.parse(tk(r,l),n)};return h}let e1=Number.EPSILON||1e-14,e2=(t,e)=>e<t.length&&!t[e].skip&&t[e],e5=t=>"x"===t?"y":"x";function e3(t,e,i){return Math.max(Math.min(t,i),e)}function e4(){return"undefined"!=typeof window&&"undefined"!=typeof document}function e6(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function e8(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let e7=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),e9=["top","right","bottom","left"];function it(t,e,i){let s={};i=i?"-"+i:"";for(let a=0;a<4;a++){let n=e9[a];s[n]=parseFloat(t[e+"-"+n+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let ie=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function ii(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,a=e7(i),n="border-box"===a.boxSizing,r=it(a,"padding"),o=it(a,"border","width"),{x:l,y:h,box:c}=function(t,e){let i,s;let a=t.touches,n=a&&a.length?a[0]:t,{offsetX:r,offsetY:o}=n,l=!1;if(ie(r,o,t.target))i=r,s=o;else{let t=e.getBoundingClientRect();i=n.clientX-t.left,s=n.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),d=r.left+(c&&o.left),u=r.top+(c&&o.top),{width:f,height:p}=e;return n&&(f-=r.width+o.width,p-=r.height+o.height),{x:Math.round((l-d)/f*i.width/s),y:Math.round((h-u)/p*i.height/s)}}let is=t=>Math.round(10*t)/10;function ia(t,e,i){let s=e||1,a=Math.floor(t.height*s),n=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let r=t.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${t.height}px`,r.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||r.height!==a||r.width!==n)&&(t.currentDevicePixelRatio=s,r.height=a,r.width=n,t.ctx.setTransform(s,0,0,s,0,0),!0)}let ir=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};e4()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function io(t,e){let i=e7(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function il(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function ih(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function ic(t,e,i,s){let a={x:t.cp2x,y:t.cp2y},n={x:e.cp1x,y:e.cp1y},r=il(t,a,i),o=il(a,n,i),l=il(n,e,i),h=il(r,o,i),c=il(o,l,i);return il(h,c,i)}function id(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function iu(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function ip(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function ig(t){return"angle"===t?{between:tJ,compare:tq,normalize:tX}:{between:tK,compare:(t,e)=>t-e,normalize:t=>t}}function im({start:t,end:e,count:i,loop:s,style:a}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:a}}function ix(t,e,i){let s,a,n;if(!i)return[t];let{property:r,start:o,end:l}=i,h=e.length,{compare:c,between:d,normalize:u}=ig(r),{start:f,end:p,loop:g,style:m}=function(t,e,i){let s;let{property:a,start:n,end:r}=i,{between:o,normalize:l}=ig(a),h=e.length,{start:c,end:d,loop:u}=t;if(u){for(c+=h,d+=h,s=0;s<h&&o(l(e[c%h][a]),n,r);++s)c--,d--;c%=h,d%=h}return d<c&&(d+=h),{start:c,end:d,loop:u,style:t.style}}(t,e,i),x=[],b=!1,_=null,y=()=>d(o,n,s)&&0!==c(o,n),v=()=>0===c(l,s)||d(l,n,s),w=()=>b||y(),M=()=>!b||v();for(let t=f,i=f;t<=p;++t)(a=e[t%h]).skip||(s=u(a[r]))===n||(b=d(s,o,l),null===_&&w()&&(_=0===c(s,o)?t:i),null!==_&&M()&&(x.push(im({start:_,end:t,loop:g,count:h,style:m})),_=null),i=t,n=s);return null!==_&&x.push(im({start:_,end:p,loop:g,count:h,style:m})),x}function ib(t,e){let i=[],s=t.segments;for(let a=0;a<s.length;a++){let n=ix(s[a],t.points,e);n.length&&i.push(...n)}return i}function i_(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let a=t._chart.getContext(),n=iy(t.options),{_datasetIndex:r,options:{spanGaps:o}}=t,l=i.length,h=[],c=n,d=e[0].start,u=d;function f(t,e,s,a){let n=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=n;for(;i[e%l].skip;)e+=n;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:a}),c=a,d=e%l)}}for(let t of e){let e;let n=i[(d=o?d:t.start)%l];for(u=d+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return er(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=iy(s.setContext(eB(a,{type:"segment",p0:n,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),c)&&f(d,u-1,t.loop,c),n=o,c=e}d<u-1&&f(d,u-1,t.loop,c)}return h}(t,e,i,s):e}function iy(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}class iv{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let a=e.listeners[s],n=e.duration;a.forEach(s=>s({chart:t,initial:e.initial,numSteps:n,currentStep:Math.min(i-e.start,n)}))}_refresh(){this._request||(this._running=!0,this._request=t4.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let a;if(!i.running||!i.items.length)return;let n=i.items,r=n.length-1,o=!1;for(;r>=0;--r)(a=n[r])._active?(a._total>i.duration&&(i.duration=a._total),a.tick(t),o=!0):(n[r]=n[n.length-1],n.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var iw=new iv;let iM="transparent",iO={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=eo(t||iM),a=s.valid&&eo(e||iM);return a&&a.valid?a.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class ik{constructor(t,e,i,s){let a=e[i];s=eV([t.to,s,a,t.from]);let n=eV([t.from,a,s]);this._active=!0,this._fn=t.fn||iO[t.type||typeof n],this._easing=en[t.easing]||en.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=n,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],a=i-this._start,n=this._duration-a;this._start=i,this._duration=Math.floor(Math.max(n,t.duration)),this._total+=a,this._loop=!!t.loop,this._to=eV([t.to,e,s,t.from]),this._from=eV([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e;let i=t-this._start,s=this._duration,a=this._prop,n=this._from,r=this._loop,o=this._to;if(this._active=n!==o&&(r||i<s),!this._active){this._target[a]=o,this._notify(!0);return}if(i<0){this._target[a]=n;return}e=i/s%2,e=r&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[a]=this._fn(n,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class iD{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!th(t))return;let e=Object.keys(ey.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let a=t[s];if(!th(a))return;let n={};for(let t of e)n[t]=a[t];(tl(a.properties)&&a.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,n)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let a=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let a=t[s[e]];a&&a.active()&&i.push(a.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),a}_createAnimations(t,e){let i;let s=this._properties,a=[],n=t.$animations||(t.$animations={}),r=Object.keys(e),o=Date.now();for(i=r.length-1;i>=0;--i){let l=r[i];if("$"===l.charAt(0))continue;if("options"===l){a.push(...this._animateOptions(t,e));continue}let h=e[l],c=n[l],d=s.get(l);if(c){if(d&&c.active()){c.update(d,h,o);continue}c.cancel()}if(!d||!d.duration){t[l]=h;continue}n[l]=c=new ik(d,t,l,h),a.push(c)}return a}update(t,e){if(0===this._properties.size){Object.assign(t,e);return}let i=this._createAnimations(t,e);if(i.length)return iw.add(this._chart,i),!0}}function ij(t,e){let i=t&&t.options||{},s=i.reverse,a=void 0===i.min?e:0,n=void 0===i.max?e:0;return{start:s?n:a,end:s?a:n}}function iN(t,e){let i,s;let a=[],n=t._getSortedDatasetMetas(e);for(i=0,s=n.length;i<s;++i)a.push(n[i].index);return a}function iC(t,e,i,s={}){let a,n,r,o;let l=t.keys,h="single"===s.mode;if(null===e)return;let c=!1;for(a=0,n=l.length;a<n;++a){if((r=+l[a])===i){if(c=!0,s.all)continue;break}tc(o=t.values[r])&&(h||0===e||tU(e)===tU(o))&&(e+=o)}return c||s.all?e:0}function iE(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function iS(t,e,i,s){for(let a of e.getMatchingVisibleMetas(s).reverse()){let e=t[a.index];if(i&&e>0||!i&&e<0)return a.index}return null}function iP(t,e){let i;let{chart:s,_cachedMeta:a}=t,n=s._stacks||(s._stacks={}),{iScale:r,vScale:o,index:l}=a,h=r.axis,c=o.axis,d=`${r.id}.${o.id}.${a.stack||a.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:r,[c]:u}=s;(i=(s._stacks||(s._stacks={}))[c]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(n,d,r))[l]=u,i._top=iS(i,o,!0,a.type),i._bottom=iS(i,o,!1,a.type),(i._visualValues||(i._visualValues={}))[l]=u}}function iT(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function iL(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let a of e=e||t._parsed){let t=a._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let iA=t=>"reset"===t||"none"===t,iF=(t,e)=>e?t:Object.assign({},t),iI=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:iN(i,!0),values:null};class iR{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=iE(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&iL(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,a=e.xAxisID=tu(i.xAxisID,iT(t,"x")),n=e.yAxisID=tu(i.yAxisID,iT(t,"y")),r=e.rAxisID=tu(i.rAxisID,iT(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,a,n,r),h=e.vAxisID=s(o,n,a,r);e.xScale=this.getScaleForId(a),e.yScale=this.getScaleForId(n),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&t5(this._data,this),t._stacked&&iL(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(th(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,a;let{iScale:n,vScale:r}=e,o="x"===n.axis?"x":"y",l="x"===r.axis?"x":"y",h=Object.keys(t),c=Array(h.length);for(i=0,s=h.length;i<s;++i)a=h[i],c[i]={[o]:a,[l]:t[a]};return c}(e,t)}else if(i!==e){if(i){t5(i,this);let t=this._cachedMeta;iL(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),t2.forEach(e=>{let i="_onData"+tD(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let a=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),a}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let a=e._stacked;e._stacked=iE(e.vScale,e),e.stack!==i.stack&&(s=!0,iL(e),e.stack=i.stack),this._resyncElements(t),(s||a!==e._stacked)&&(iP(this,e._parsed),e._stacked=iE(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,a;let{_cachedMeta:n,_data:r}=this,{iScale:o,_stacked:l}=n,h=o.axis,c=0===t&&e===r.length||n._sorted,d=t>0&&n._parsed[t-1];if(!1===this._parsing)n._parsed=r,n._sorted=!0,a=r;else{a=tl(r[t])?this.parseArrayData(n,r,t,e):th(r[t])?this.parseObjectData(n,r,t,e):this.parsePrimitiveData(n,r,t,e);let o=()=>null===s[h]||d&&s[h]<d[h];for(i=0;i<e;++i)n._parsed[i+t]=s=a[i],c&&(o()&&(c=!1),d=s);n._sorted=c}l&&iP(this,a)}parsePrimitiveData(t,e,i,s){let a,n;let{iScale:r,vScale:o}=t,l=r.axis,h=o.axis,c=r.getLabels(),d=r===o,u=Array(s);for(a=0;a<s;++a)n=a+i,u[a]={[l]:d||r.parse(c[n],n),[h]:o.parse(e[n],n)};return u}parseArrayData(t,e,i,s){let a,n,r;let{xScale:o,yScale:l}=t,h=Array(s);for(a=0;a<s;++a)r=e[n=a+i],h[a]={x:o.parse(r[0],n),y:l.parse(r[1],n)};return h}parseObjectData(t,e,i,s){let a,n,r;let{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:c="y"}=this._parsing,d=Array(s);for(a=0;a<s;++a)r=e[n=a+i],d[a]={x:o.parse(tk(r,h),n),y:l.parse(tk(r,c),n)};return d}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,a=this._cachedMeta,n=e[t.axis];return iC({keys:iN(s,!0),values:e._stacks[t.axis]._visualValues},n,a.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let a=i[e.axis],n=null===a?NaN:a,r=s&&i._stacks[e.axis];s&&r&&(s.values=r,n=iC(s,a,this._cachedMeta.index)),t.min=Math.min(t.min,n),t.max=Math.max(t.max,n)}getMinMax(t,e){let i,s;let a=this._cachedMeta,n=a._parsed,r=a._sorted&&t===a.iScale,o=n.length,l=this._getOtherScale(t),h=iI(e,a,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:u}=function(t){let{min:e,max:i,minDefined:s,maxDefined:a}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}(l);function f(){let e=(s=n[i])[l.axis];return!tc(s[t.axis])||d>e||u<e}for(i=0;i<o&&(f()||(this.updateRangeFromParsed(c,t,s,h),!r));++i);if(r){for(i=o-1;i>=0;--i)if(!f()){this.updateRangeFromParsed(c,t,s,h);break}}return c}getAllParsedValues(t){let e,i,s;let a=this._cachedMeta._parsed,n=[];for(e=0,i=a.length;e<i;++e)tc(s=a[e][t.axis])&&n.push(s);return n}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:s?""+s.getLabelForValue(a[s.axis]):""}}_update(t){var e;let i,s,a,n;let r=this._cachedMeta;this.update(t||"default"),r._clip=(th(e=tu(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=ij(t,i),a=ij(e,i);return{top:a.end,right:s.end,bottom:a.start,left:s.start}}(r.xScale,r.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,a=e.bottom,n=e.left):i=s=a=n=e,{top:i,right:s,bottom:a,left:n,disabled:!1===e})}update(t){}draw(){let t;let e=this._ctx,i=this.chart,s=this._cachedMeta,a=s.data||[],n=i.chartArea,r=[],o=this._drawStart||0,l=this._drawCount||a.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,n,o,l),t=o;t<o+l;++t){let i=a[t];i.hidden||(i.active&&h?r.push(i):i.draw(e,n))}for(t=0;t<r.length;++t)r[t].draw(e,n)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s;let a;let n=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(a=e.$context||(e.$context=eB(this.getContext(),{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),a.raw=n.data[t],a.index=a.dataIndex=t}else(a=this.$context||(this.$context=eB(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:s=this.index,index:s,mode:"default",type:"dataset"}))).dataset=n,a.index=a.datasetIndex=this.index;return a.active=!!e,a.mode=i,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,a=this._cachedDataOpts,n=t+"-"+e,r=a[n],o=this.enableOptionSharing&&tj(i);if(r)return iF(r,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),c=s?[`${t}Hover`,"hover",t,""]:[t,""],d=l.getOptionScopes(this.getDataset(),h),u=Object.keys(ey.elements[t]),f=l.resolveNamedOptions(d,u,()=>this.getContext(i,s,e),c);return f.$shared&&(f.$shared=o,a[n]=Object.freeze(iF(f,o))),f}_resolveAnimations(t,e,i){let s;let a=this.chart,n=this._cachedDataOpts,r=`animation-${e}`,o=n[r];if(o)return o;if(!1!==a.options.animation){let a=this.chart.config,n=a.datasetAnimationScopeKeys(this._type,e),r=a.getOptionScopes(this.getDataset(),n);s=a.createResolver(r,this.getContext(t,i,e))}let l=new iD(a,s&&s.animations);return s&&s._cacheable&&(n[r]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||iA(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,a=this.getSharedOptions(i),n=this.includeOptions(e,a)||a!==s;return this.updateSharedOptions(a,e,i),{sharedOptions:a,includeOptions:n}}updateElement(t,e,i,s){iA(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!iA(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let a=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(a)||a})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,a=e.length,n=Math.min(a,s);n&&this.parse(0,n),a>s?this._insertElements(s,a-s,t):a<s&&this._removeElements(a,s-a)}_insertElements(t,e,i=!0){let s;let a=this._cachedMeta,n=a.data,r=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=r;s--)t[s]=t[s-e]};for(o(n),s=t;s<r;++s)n[s]=new this.dataElementType;this._parsing&&o(a._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&iL(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function iU(t,e,i,s){return tl(t)?function(t,e,i,s){let a=i.parse(t[0],s),n=i.parse(t[1],s),r=Math.min(a,n),o=Math.max(a,n),l=r,h=o;Math.abs(r)>Math.abs(o)&&(l=o,h=r),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:a,end:n,min:r,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function iz(t,e,i,s){let a,n,r,o;let l=t.iScale,h=t.vScale,c=l.getLabels(),d=l===h,u=[];for(a=i,n=i+s;a<n;++a)o=e[a],(r={})[l.axis]=d||l.parse(c[a],a),u.push(iU(o,r,h,a));return u}function iV(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function iB(t,e,i,s){var a;return t=s?iW(t=(a=t)===e?i:a===i?e:a,i,e):iW(t,e,i)}function iW(t,e,i){return"start"===t?e:"end"===t?i:t}class iH extends iR{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return iz(t,e,i,s)}parseArrayData(t,e,i,s){return iz(t,e,i,s)}parseObjectData(t,e,i,s){let a,n,r,o;let{iScale:l,vScale:h}=t,{xAxisKey:c="x",yAxisKey:d="y"}=this._parsing,u="x"===l.axis?c:d,f="x"===h.axis?c:d,p=[];for(a=i,n=i+s;a<n;++a)o=e[a],(r={})[l.axis]=l.parse(tk(o,u),a),p.push(iU(tk(o,f),r,h,a));return p}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),a=s._custom,n=iV(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:n}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let a="reset"===s,{index:n,_cachedMeta:{vScale:r}}=this,o=r.getBasePixel(),l=r.isHorizontal(),h=this._getRuler(),{sharedOptions:c,includeOptions:d}=this._getSharedOptions(e,s);for(let u=e;u<e+i;u++){let e=this.getParsed(u),i=a||to(e[r.axis])?{base:o,head:o}:this._calculateBarValuePixels(u),f=this._calculateBarIndexPixels(u,h),p=(e._stacks||{})[r.axis],g={horizontal:l,base:i.base,enableBorderRadius:!p||iV(e._custom)||n===p._top||n===p._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};d&&(g.options=c||this.resolveDataElementOptions(u,t[u].active?"active":s));let m=g.options||t[u].options;(function(t,e,i,s){let a,n,r,o,l,h=e.borderSkipped,c={};if(!h){t.borderSkipped=c;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:d,end:u,reverse:f,top:p,bottom:g}=(t.horizontal?(a=t.base>t.x,n="left",r="right"):(a=t.base<t.y,n="bottom",r="top"),a?(o="end",l="start"):(o="start",l="end"),{start:n,end:r,reverse:a,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=p:(i._bottom||0)===s?h=g:(c[iB(g,d,u,f)]=!0,h=p)),c[iB(h,d,u,f)]=!0,t.borderSkipped=c})(g,m,p,n),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?1===i?.33:0:e}(g,m,h.ratio),this.updateElement(t[u],u,g,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),a=i.options.stacked,n=[],r=this._cachedMeta.controller.getParsed(e),o=r&&r[i.axis],l=t=>{let e=t._parsed.find(t=>t[i.axis]===o),s=e&&e[t.vScale.axis];if(to(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&l(i))&&((!1===a||-1===n.indexOf(i.stack)||void 0===a&&void 0===i.stack)&&n.push(i.stack),i.index===t))break;return n.length||n.push(void 0),n}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),a=void 0!==e?s.indexOf(e):-1;return -1===a?s.length-1:a}_getRuler(){let t,e;let i=this.options,s=this._cachedMeta,a=s.iScale,n=[];for(t=0,e=s.data.length;t<e;++t)n.push(a.getPixelForValue(this.getParsed(t)[a.axis],t));let r=i.barThickness;return{min:r||function(t){let e,i,s,a;let n=t.iScale,r=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,a=i.length;e<a;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=t3(s.sort((t,e)=>t-e))}return t._cache.$bar}(n,t.type),o=n._length,l=()=>{32767!==s&&-32768!==s&&(tj(a)&&(o=Math.min(o,Math.abs(s-a)||o)),a=s)};for(e=0,i=r.length;e<i;++e)s=n.getPixelForValue(r[e]),l();for(e=0,a=void 0,i=n.ticks.length;e<i;++e)s=n.getPixelForTick(e),l();return o}(s),pixels:n,start:a._startPixel,end:a._endPixel,stackCount:this._getStackCount(),scale:a,grouped:i.grouped,ratio:r?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i;let{_cachedMeta:{vScale:s,_stacked:a,index:n},options:{base:r,minBarLength:o}}=this,l=r||0,h=this.getParsed(t),c=h._custom,d=iV(c),u=h[s.axis],f=0,p=a?this.applyStack(s,h,a):u;p!==u&&(f=p-u,p=u),d&&(u=c.barStart,p=c.barEnd-c.barStart,0!==u&&tU(u)!==tU(c.barEnd)&&(f=0),f+=u);let g=to(r)||d?f:r,m=s.getPixelForValue(g);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(f+p):m)-m)<o){var x;i=(0!==(x=i)?tU(x):(s.isHorizontal()?1:-1)*(s.min>=l?1:-1))*o,u===l&&(m-=i/2);let t=s.getPixelForDecimal(0),r=s.getPixelForDecimal(1);e=(m=Math.max(Math.min(m,Math.max(t,r)),Math.min(t,r)))+i,a&&!d&&(h._stacks[s.axis]._visualValues[n]=s.getValueForPixel(e)-s.getValueForPixel(m))}if(m===s.getPixelForValue(l)){let t=tU(i)*s.getLineWidthForValue(l)/2;m+=t,i-=t}return{size:i,base:m,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s;let a=e.scale,n=this.options,r=n.skipNull,o=tu(n.maxBarThickness,1/0);if(e.grouped){let a=r?this._getStackCount(t):e.stackCount,l="flex"===n.barThickness?function(t,e,i,s){let a=e.pixels,n=a[t],r=t>0?a[t-1]:null,o=t<a.length-1?a[t+1]:null,l=i.categoryPercentage;null===r&&(r=n-(null===o?e.end-e.start:o-n)),null===o&&(o=n+n-r);let h=n-(n-Math.min(r,o))/2*l;return{chunk:Math.abs(o-r)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,n,a):function(t,e,i,s){let a,n;let r=i.barThickness;return to(r)?(a=e.min*i.categoryPercentage,n=i.barPercentage):(a=r*s,n=1),{chunk:a/s,ratio:n,start:e.pixels[t]-a/2}}(t,e,n,a),h=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0);i=l.start+l.chunk*h+l.chunk/2,s=Math.min(o,l.chunk*l.ratio)}else i=a.getPixelForValue(this.getParsed(t)[a.axis],t),s=Math.min(o,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,a=0;for(;a<s;++a)null===this.getParsed(a)[e.axis]||i[a].hidden||i[a].draw(this._ctx)}}class i$ extends iR{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let a=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<a.length;t++)a[t]._custom=this.resolveDataElementOptions(t+i).radius;return a}parseArrayData(t,e,i,s){let a=super.parseArrayData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=tu(s[2],this.resolveDataElementOptions(t+i).radius)}return a}parseObjectData(t,e,i,s){let a=super.parseObjectData(t,e,i,s);for(let t=0;t<a.length;t++){let s=e[i+t];a[t]._custom=tu(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return a}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),r=s.getLabelForValue(n.x),o=a.getLabelForValue(n.y),l=n._custom;return{label:i[t]||"",value:"("+r+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=n.axis,c=r.axis;for(let d=e;d<e+i;d++){let e=t[d],i=!a&&this.getParsed(d),u={},f=u[h]=a?n.getPixelForDecimal(.5):n.getPixelForValue(i[h]),p=u[c]=a?r.getBasePixel():r.getPixelForValue(i[c]);u.skip=isNaN(f)||isNaN(p),l&&(u.options=o||this.resolveDataElementOptions(d,e.active?"active":s),a&&(u.options.radius=0)),this.updateElement(e,d,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let a=s.radius;return"active"!==e&&(s.radius=0),s.radius+=tu(i&&i._custom,a),s}}class iY extends iR{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let a,n,r=t=>+i[t];if(th(i[t])){let{key:t="value"}=this._parsing;r=e=>+tk(i[e],t)}for(a=t,n=t+e;a<n;++a)s._parsed[a]=r(a)}}_getRotation(){return tH(this.options.rotation-90)}_getCircumference(){return tH(this.options.circumference)}_getRotationExtents(){let t=tS,e=-tS;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,a=s._getRotation(),n=s._getCircumference();t=Math.min(t,a),e=Math.max(e,a+n)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,n=Math.max((Math.min(e.width,e.height)-a)/2,0),r=Math.min(tf(this.options.cutout,n),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:c,ratioY:d,offsetX:u,offsetY:f}=function(t,e,i){let s=1,a=1,n=0,r=0;if(e<tS){let o=t+e,l=Math.cos(t),h=Math.sin(t),c=Math.cos(o),d=Math.sin(o),u=(e,s,a)=>tJ(e,t,o,!0)?1:Math.max(s,s*i,a,a*i),f=(e,s,a)=>tJ(e,t,o,!0)?-1:Math.min(s,s*i,a,a*i),p=u(0,l,c),g=u(tA,h,d),m=f(tE,l,c),x=f(tE+tA,h,d);s=(p-m)/2,a=(g-x)/2,n=-(p+m)/2,r=-(g+x)/2}return{ratioX:s,ratioY:a,offsetX:n,offsetY:r}}(h,l,r),p=(e.width-a)/c,g=(e.height-a)/d,m=tp(this.options.radius,Math.max(Math.min(p,g)/2,0)),x=Math.max(m*r,0),b=(m-x)/this._getVisibleDatasetWeightTotal();this.offsetX=u*m,this.offsetY=f*m,i.total=this.calculateTotal(),this.outerRadius=m-b*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-b*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,a=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*a/tS)}updateElements(t,e,i,s){let a;let n="reset"===s,r=this.chart,o=r.chartArea,l=r.options.animation,h=(o.left+o.right)/2,c=(o.top+o.bottom)/2,d=n&&l.animateScale,u=d?0:this.innerRadius,f=d?0:this.outerRadius,{sharedOptions:p,includeOptions:g}=this._getSharedOptions(e,s),m=this._getRotation();for(a=0;a<e;++a)m+=this._circumference(a,n);for(a=e;a<e+i;++a){let e=this._circumference(a,n),i=t[a],r={x:h+this.offsetX,y:c+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:f,innerRadius:u};g&&(r.options=p||this.resolveDataElementOptions(a,i.active?"active":s)),m+=e,this.updateElement(i,a,r,s)}}calculateTotal(){let t;let e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let a=e._parsed[t];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(a))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*tS:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=eu(e._parsed[t],i.options.locale);return{label:s[t]||"",value:a}}getMaxBorderWidth(t){let e,i,s,a,n,r=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,a=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(n=a.resolveDataElementOptions(e)).borderAlign&&(r=Math.max(r,n.borderWidth||0,n.hoverBorderWidth||0));return r}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(tu(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class iZ extends iR{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:a}=e,n=this.chart._animationsDisabled,{start:r,count:o}=et(e,s,n);this._drawStart=r,this._drawCount=o,ee(e)&&(r=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!a._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!n,options:l},t),this.updateElements(s,r,o,t)}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:c}=this._getSharedOptions(e,s),d=n.axis,u=r.axis,{spanGaps:f,segment:p}=this.options,g=tB(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||a||"none"===s,x=e+i,b=t.length,_=e>0&&this.getParsed(e-1);for(let i=0;i<b;++i){let f=t[i],b=m?f:{};if(i<e||i>=x){b.skip=!0;continue}let y=this.getParsed(i),v=to(y[u]),w=b[d]=n.getPixelForValue(y[d],i),M=b[u]=a||v?r.getBasePixel():r.getPixelForValue(o?this.applyStack(r,y,o):y[u],i);b.skip=isNaN(w)||isNaN(M)||v,b.stop=i>0&&Math.abs(y[d]-_[d])>g,p&&(b.parsed=y,b.raw=l.data[i]),c&&(b.options=h||this.resolveDataElementOptions(i,f.active?"active":s)),m||this.updateElement(f,i,b,s),_=y}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class iq extends iR{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,a)=>{let n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=eu(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:a}}parseObjectData(t,e,i,s){return e0.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),a=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),n=(s-a)/t.getVisibleDatasetCount();this.outerRadius=s-n*this.index,this.innerRadius=this.outerRadius-n}updateElements(t,e,i,s){let a;let n="reset"===s,r=this.chart,o=r.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,c=l.yCenter,d=l.getIndexAngle(0)-.5*tE,u=d,f=360/this.countVisibleElements();for(a=0;a<e;++a)u+=this._computeAngle(a,s,f);for(a=e;a<e+i;a++){let e=t[a],i=u,p=u+this._computeAngle(a,s,f),g=r.getDataVisibility(a)?l.getDistanceFromCenterForValue(this.getParsed(a).r):0;u=p,n&&(o.animateScale&&(g=0),o.animateRotate&&(i=p=d));let m={x:h,y:c,innerRadius:0,outerRadius:g,startAngle:i,endAngle:p,options:this.resolveDataElementOptions(a,e.active?"active":s)};this.updateElement(e,a,m,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?tH(this.resolveDataElementOptions(t,e).angle||i):0}}class iX extends iY{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class iJ extends iR{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return e0.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],a=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let n={_loop:!0,_fullLoop:a.length===s.length,options:e};this.updateElement(i,void 0,n,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let a=this._cachedMeta.rScale,n="reset"===s;for(let r=e;r<e+i;r++){let e=t[r],i=this.resolveDataElementOptions(r,e.active?"active":s),o=a.getPointPositionForValue(r,this.getParsed(r).r),l=n?a.xCenter:o.x,h=n?a.yCenter:o.y,c={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,r,c,s)}}}class iG extends iR{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),r=s.getLabelForValue(n.x),o=a.getLabelForValue(n.y);return{label:i[t]||"",value:"("+r+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:a,count:n}=et(e,i,s);if(this._drawStart=a,this._drawCount=n,ee(e)&&(a=0,n=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:a,_dataset:n}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!n._decimated,a.points=i;let r=this.resolveDatasetElementOptions(t);r.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:r},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,a,n,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let a="reset"===s,{iScale:n,vScale:r,_stacked:o,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),c=this.getSharedOptions(h),d=this.includeOptions(s,c),u=n.axis,f=r.axis,{spanGaps:p,segment:g}=this.options,m=tB(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||a||"none"===s,b=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){let e=t[h],i=this.getParsed(h),p=x?e:{},_=to(i[f]),y=p[u]=n.getPixelForValue(i[u],h),v=p[f]=a||_?r.getBasePixel():r.getPixelForValue(o?this.applyStack(r,i,o):i[f],h);p.skip=isNaN(y)||isNaN(v)||_,p.stop=h>0&&Math.abs(i[u]-b[u])>m,g&&(p.parsed=i,p.raw=l.data[h]),d&&(p.options=c||this.resolveDataElementOptions(h,e.active?"active":s)),x||this.updateElement(e,h,p,s),b=i}this.updateSharedOptions(c,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function iK(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class iQ{static override(t){Object.assign(iQ.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return iK()}parse(){return iK()}format(){return iK()}add(){return iK()}diff(){return iK()}startOf(){return iK()}endOf(){return iK()}}var i0={_date:iQ};function i1(t,e,i,s,a){let n=t.getSortedVisibleDatasetMetas(),r=i[e];for(let t=0,i=n.length;t<i;++t){let{index:i,data:o}=n[t],{lo:l,hi:h}=function(t,e,i,s){let{controller:a,data:n,_sorted:r}=t,o=a._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&"r"!==e&&r&&n.length){let r=o._reversePixels?t1:t0;if(s){if(a._sharedOptions){let t=n[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=r(n,e,i-s),a=r(n,e,i+s);return{lo:t.lo,hi:a.hi}}}}else{let s=r(n,e,i);if(l){let{vScale:e}=a._cachedMeta,{_parsed:i}=t,n=i.slice(0,s.lo+1).reverse().findIndex(t=>!to(t[e.axis]));s.lo-=Math.max(0,n);let r=i.slice(s.hi).findIndex(t=>!to(t[e.axis]));s.hi+=Math.max(0,r)}return s}}return{lo:0,hi:n.length-1}}(n[t],e,r,a);for(let t=l;t<=h;++t){let e=o[t];e.skip||s(e,i,t)}}}function i2(t,e,i,s,a){let n=[];return(a||t.isPointInArea(e))&&i1(t,i,e,function(i,r,o){(a||eD(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&n.push({element:i,datasetIndex:r,index:o})},!0),n}function i5(t,e,i,s,a,n){let r;return n||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,a,n){let r=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return i1(t,i,e,function(i,h,c){let d=i.inRange(e.x,e.y,a);if(s&&!d)return;let u=i.getCenterPoint(a);if(!(n||t.isPointInArea(u))&&!d)return;let f=o(e,u);f<l?(r=[{element:i,datasetIndex:h,index:c}],l=f):f===l&&r.push({element:i,datasetIndex:h,index:c})}),r}(t,e,i,s,a,n):(r=[],i1(t,i,e,function(t,i,s){let{startAngle:n,endAngle:o}=t.getProps(["startAngle","endAngle"],a),{angle:l}=tY(t,{x:e.x,y:e.y});tJ(l,n,o)&&r.push({element:t,datasetIndex:i,index:s})}),r):[]}function i3(t,e,i,s,a){let n=[],r="x"===i?"inXRange":"inYRange",o=!1;return(i1(t,i,e,(t,s,l)=>{t[r]&&t[r](e[i],a)&&(n.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,a))}),s&&!o)?[]:n}var i4={modes:{index(t,e,i,s){let a=ii(e,t),n=i.axis||"x",r=i.includeInvisible||!1,o=i.intersect?i2(t,a,n,s,r):i5(t,a,n,!1,s,r),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let a=ii(e,t),n=i.axis||"xy",r=i.includeInvisible||!1,o=i.intersect?i2(t,a,n,s,r):i5(t,a,n,!1,s,r);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let a=ii(e,t);return i2(t,a,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let a=ii(e,t),n=i.axis||"xy",r=i.includeInvisible||!1;return i5(t,a,n,i.intersect,s,r)},x(t,e,i,s){let a=ii(e,t);return i3(t,a,"x",i.intersect,s)},y(t,e,i,s){let a=ii(e,t);return i3(t,a,"y",i.intersect,s)}}};let i6=["left","top","right","bottom"];function i8(t,e){return t.filter(t=>t.pos===e)}function i7(t,e){return t.filter(t=>-1===i6.indexOf(t.pos)&&t.box.axis===e)}function i9(t,e){return t.sort((t,i)=>{let s=e?i:t,a=e?t:i;return s.weight===a.weight?s.index-a.index:s.weight-a.weight})}function st(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function se(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function si(t,e,i,s){let a,n,r,o,l,h;let c=[];for(a=0,n=t.length,l=0;a<n;++a){(o=(r=t[a]).box).update(r.width||e.w,r.height||e.h,function(t,e){let i=e.maxPadding;return function(t){let s={left:0,top:0,right:0,bottom:0};return t.forEach(t=>{s[t]=Math.max(e[t],i[t])}),s}(t?["left","right"]:["top","bottom"])}(r.horizontal,e));let{same:n,other:d}=function(t,e,i,s){let{pos:a,box:n}=i,r=t.maxPadding;if(!th(a)){i.size&&(t[a]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?n.height:n.width),i.size=e.size/e.count,t[a]+=i.size}n.getPadding&&se(r,n.getPadding());let o=Math.max(0,e.outerWidth-st(r,t,"left","right")),l=Math.max(0,e.outerHeight-st(r,t,"top","bottom")),h=o!==t.w,c=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:h,other:c}:{same:c,other:h}}(e,i,r,s);l|=n&&c.length,h=h||d,o.fullSize||c.push(r)}return l&&si(c,e,i,s)||h}function ss(t,e,i,s,a){t.top=i,t.left=e,t.right=e+s,t.bottom=i+a,t.width=s,t.height=a}function sa(t,e,i,s){let a=i.padding,{x:n,y:r}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,n=l.size||t.height;tj(l.start)&&(r=l.start),t.fullSize?ss(t,a.left,r,i.outerWidth-a.right-a.left,n):ss(t,e.left+l.placed,r,s,n),l.start=r,l.placed+=s,r=t.bottom}else{let s=e.h*h,r=l.size||t.width;tj(l.start)&&(n=l.start),t.fullSize?ss(t,n,a.top,r,i.outerHeight-a.bottom-a.top):ss(t,n,e.top+l.placed,r,s),l.start=n,l.placed+=s,n=t.right}}e.x=n,e.y=r}var sn={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let a=eU(t.options.layout.padding),n=Math.max(e-a.width,0),r=Math.max(i-a.height,0),o=function(t){let e=function(t){let e,i,s,a,n,r;let o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:a,options:{stack:n,stackWeight:r=1}}=s),o.push({index:e,box:s,pos:a,horizontal:s.isHorizontal(),weight:s.weight,stack:n&&a+n,stackWeight:r});return o}(t),i=i9(e.filter(t=>t.box.fullSize),!0),s=i9(i8(e,"left"),!0),a=i9(i8(e,"right")),n=i9(i8(e,"top"),!0),r=i9(i8(e,"bottom")),o=i7(e,"x"),l=i7(e,"y");return{fullSize:i,leftAndTop:s.concat(n),rightAndBottom:a.concat(l).concat(r).concat(o),chartArea:i8(e,"chartArea"),vertical:s.concat(a).concat(l),horizontal:n.concat(r).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;tm(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let c=Object.freeze({outerWidth:e,outerHeight:i,padding:a,availableWidth:n,availableHeight:r,vBoxMaxWidth:n/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:r/2}),d=Object.assign({},a);se(d,eU(s));let u=Object.assign({maxPadding:d,w:n,h:r,x:a.left,y:a.top},a),f=function(t,e){let i,s,a;let n=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:a}=i;if(!t||!i6.includes(s))continue;let n=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});n.count++,n.weight+=a}return e}(t),{vBoxMaxWidth:r,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(a=t[i]).box,l=n[a.stack],h=l&&a.stackWeight/l.weight;a.horizontal?(a.width=h?h*r:s&&e.availableWidth,a.height=o):(a.width=r,a.height=h?h*o:s&&e.availableHeight)}return n}(l.concat(h),c);si(o.fullSize,u,c,f),si(l,u,c,f),si(h,u,c,f)&&si(l,u,c,f),function(t){let e=t.maxPadding;function i(i){let s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(u),sa(o.leftAndTop,u,c,f),u.x+=u.w,u.y+=u.h,sa(o.rightAndBottom,u,c,f),t.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},tm(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class sr{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class so extends sr{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let sl="$chartjs",sh={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},sc=t=>null===t||""===t,sd=!!ir&&{passive:!0};function su(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function sf(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||su(i.addedNodes,s))&&!su(i.removedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}function sp(t,e,i){let s=t.canvas,a=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||su(i.removedNodes,s))&&!su(i.addedNodes,s);e&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}let sg=new Map,sm=0;function sx(){let t=window.devicePixelRatio;t!==sm&&(sm=t,sg.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function sb(t,e,i){let s=t.canvas,a=s&&e6(s);if(!a)return;let n=t6((t,e)=>{let s=a.clientWidth;i(t,e),s<a.clientWidth&&i()},window),r=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&n(i,s)});return r.observe(a),sg.size||window.addEventListener("resize",sx),sg.set(t,n),r}function s_(t,e,i){i&&i.disconnect(),"resize"===e&&(sg.delete(t),sg.size||window.removeEventListener("resize",sx))}function sy(t,e,i){let s=t.canvas,a=t6(e=>{null!==t.ctx&&i(function(t,e){let i=sh[t.type]||t.type,{x:s,y:a}=ii(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==a?a:null}}(e,t))},t);return s&&s.addEventListener(e,a,sd),a}class sv extends sr{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){let i=t.style,s=t.getAttribute("height"),a=t.getAttribute("width");if(t[sl]={initial:{height:s,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",sc(a)){let e=io(t,"width");void 0!==e&&(t.width=e)}if(sc(s)){if(""===t.style.height)t.height=t.width/(e||2);else{let e=io(t,"height");void 0!==e&&(t.height=e)}}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[sl])return!1;let i=e[sl].initial;["height","width"].forEach(t=>{let s=i[t];to(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[sl],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),a={attach:sf,detach:sp,resize:sb}[e]||sy;s[e]=a(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:s_,detach:s_,resize:s_})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,sd)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let a=e7(t),n=it(a,"margin"),r=e8(a.maxWidth,t,"clientWidth")||tT,o=e8(a.maxHeight,t,"clientHeight")||tT,l=function(t,e,i){let s,a;if(void 0===e||void 0===i){let n=t&&e6(t);if(n){let t=n.getBoundingClientRect(),r=e7(n),o=it(r,"border","width"),l=it(r,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=e8(r.maxWidth,n,"clientWidth"),a=e8(r.maxHeight,n,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||tT,maxHeight:a||tT}}(t,e,i),{width:h,height:c}=l;if("content-box"===a.boxSizing){let t=it(a,"border","width"),e=it(a,"padding");h-=e.width+t.width,c-=e.height+t.height}return h=Math.max(0,h-n.width),c=Math.max(0,s?h/s:c-n.height),h=is(Math.min(h,r,l.maxWidth)),c=is(Math.min(c,o,l.maxHeight)),h&&!c&&(c=is(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&c>l.height&&(h=is(Math.floor((c=l.height)*s))),{width:h,height:c}}(t,e,i,s)}isAttached(t){let e=t&&e6(t);return!!(e&&e.isConnected)}}class sw{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return tB(this.x)&&tB(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function sM(t,e,i,s,a){let n,r,o;let l=tu(s,0),h=Math.min(tu(a,t.length),t.length),c=0;for(i=Math.ceil(i),a&&(i=(n=a-s)/Math.floor(n/i)),o=l;o<0;)o=Math.round(l+ ++c*i);for(r=Math.max(l,0);r<h;r++)r===o&&(e.push(t[r]),o=Math.round(l+ ++c*i))}let sO=t=>"left"===t?"right":"right"===t?"left":t,sk=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,sD=(t,e)=>Math.min(e||t,t);function sj(t,e){let i=[],s=t.length/e,a=t.length,n=0;for(;n<a;n+=s)i.push(t[Math.floor(n)]);return i}function sN(t){return t.drawTicks?t.tickLength:0}function sC(t,e){if(!t.display)return 0;let i=ez(t.font,e),s=eU(t.padding);return(tl(t.text)?t.text.length:1)*i.lineHeight+s.height}class sE extends sw{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=td(t,Number.POSITIVE_INFINITY),e=td(e,Number.NEGATIVE_INFINITY),i=td(i,Number.POSITIVE_INFINITY),s=td(s,Number.NEGATIVE_INFINITY),{min:td(t,i),max:td(e,s),minDefined:tc(t),maxDefined:tc(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:a,maxDefined:n}=this.getUserBounds();if(a&&n)return{min:i,max:s};let r=this.getMatchingVisibleMetas();for(let o=0,l=r.length;o<l;++o)e=r[o].controller.getMinMax(this,t),a||(i=Math.min(i,e.min)),n||(s=Math.max(s,e.max));return i=n&&i>s?s:i,s=a&&i>s?i:s,{min:td(i,td(s,i)),max:td(s,td(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){tg(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:a,ticks:n}=this.options,r=n.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:a}=t,n=tp(e,(a-s)/2),r=(t,e)=>i&&0===t?0:t+e;return{min:r(s,-Math.abs(n)),max:r(a,n)}}(this,a,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let o=r<this.ticks.length;this._convertTicksToLabels(o?sj(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),n.display&&(n.autoSkip||"auto"===n.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+(e?0:1),t._maxLength/i))}(t),a=Math.min(i.maxTicksLimit||s,s),n=i.major.enabled?function(t){let e,i;let s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],r=n.length,o=n[0],l=n[r-1],h=[];if(r>a)return function(t,e,i,s){let a,n=0,r=i[0];for(a=0,s=Math.ceil(s);a<t.length;a++)a===r&&(e.push(t[a]),r=i[++n*s])}(e,h,n,r/a),h;let c=function(t,e,i){let s=function(t){let e,i;let s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),a=e.length/i;if(!s)return Math.max(a,1);let n=function(t){let e;let i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=n.length-1;t<e;t++){let e=n[t];if(e>a)return e}return Math.max(a,1)}(n,e,a);if(r>0){let t,i;let s=r>1?Math.round((l-o)/(r-1)):null;for(sM(e,h,c,to(s)?0:o-s,o),t=0,i=r-1;t<i;t++)sM(e,h,c,n[t],n[t+1]);return sM(e,h,c,l,to(s)?e.length:l+s),h}return sM(e,h,c),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){tg(this.options.afterUpdate,[this])}beforeSetDimensions(){tg(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){tg(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),tg(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){tg(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s;let a=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=tg(a.callback,[s.value,e,t],this)}afterTickToLabelConversion(){tg(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){tg(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i;let s=this.options,a=s.ticks,n=sD(this.ticks.length,s.ticks.maxTicksLimit),r=a.minRotation||0,o=a.maxRotation,l=r;if(!this._isVisible()||!a.display||r>=o||n<=1||!this.isHorizontal()){this.labelRotation=r;return}let h=this._getLabelSizes(),c=h.widest.width,d=h.highest.height,u=tG(this.chart.width-c,0,this.maxWidth);c+6>(t=s.offset?this.maxWidth/n:u/(n-1))&&(t=u/(n-(s.offset?.5:1)),e=this.maxHeight-sN(s.grid)-a.padding-sC(s.title,this.chart.options.font),i=Math.sqrt(c*c+d*d),l=Math.max(r,Math.min(o,l=180/tE*Math.min(Math.asin(tG((h.highest.height+6)/t,-1,1)),Math.asin(tG(e/i,-1,1))-Math.asin(tG(d/i,-1,1)))))),this.labelRotation=l}afterCalculateLabelRotation(){tg(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){tg(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:a}}=this,n=this._isVisible(),r=this.isHorizontal();if(n){let n=sC(s,e.options.font);if(r?(t.width=this.maxWidth,t.height=sN(a)+n):(t.height=this.maxHeight,t.width=sN(a)+n),i.display&&this.ticks.length){let{first:e,last:s,widest:a,highest:n}=this._getLabelSizes(),o=2*i.padding,l=tH(this.labelRotation),h=Math.cos(l),c=Math.sin(l);if(r){let e=i.mirror?0:c*a.width+h*n.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*a.width+c*n.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,c,h)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:a,padding:n},position:r}=this.options,o=0!==this.labelRotation,l="top"!==r&&"x"===this.axis;if(this.isHorizontal()){let r=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),c=0,d=0;o?l?(c=s*t.width,d=i*e.height):(c=i*t.height,d=s*e.width):"start"===a?d=e.width:"end"===a?c=t.width:"inner"!==a&&(c=t.width/2,d=e.width/2),this.paddingLeft=Math.max((c-r+n)*this.width/(this.width-r),0),this.paddingRight=Math.max((d-h+n)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===a?(i=0,s=t.height):"end"===a&&(i=e.height,s=0),this.paddingTop=i+n,this.paddingBottom=s+n}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){tg(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)to(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=sj(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,a,n,r,o,l,h,c,d,u,f;let{ctx:p,_longestTextCache:g}=this,m=[],x=[],b=Math.floor(e/sD(e,i)),_=0,y=0;for(s=0;s<e;s+=b){if(r=t[s].label,o=this._resolveTickFontOptions(s),p.font=l=o.string,h=g[l]=g[l]||{data:{},gc:[]},c=o.lineHeight,d=u=0,to(r)||tl(r)){if(tl(r))for(a=0,n=r.length;a<n;++a)to(f=r[a])||tl(f)||(d=ev(p,h.data,h.gc,d,f),u+=c)}else d=ev(p,h.data,h.gc,d,r),u=c;m.push(d),x.push(u),_=Math.max(d,_),y=Math.max(u,y)}tm(g,t=>{let i;let s=t.gc,a=s.length/2;if(a>e){for(i=0;i<a;++i)delete t.data[s[i]];s.splice(0,a)}});let v=m.indexOf(_),w=x.indexOf(y),M=t=>({width:m[t]||0,height:x[t]||0});return{first:M(0),last:M(e-1),widest:M(v),highest:M(w),widths:m,heights:x}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return tG(this._alignToPixels?ew(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let i=e[t];return i.$context||(i.$context=eB(this.getContext(),{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=eB(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){let t=this.options.ticks,e=tH(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),a=this._getLabelSizes(),n=t.autoSkipPadding||0,r=a?a.widest.width+n:0,o=a?a.highest.height+n:0;return this.isHorizontal()?o*i>r*s?r/i:o/s:o*s<r*i?o/i:r/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,a,n,r,o,l,h,c,d,u;let f=this.axis,p=this.chart,g=this.options,{grid:m,position:x,border:b}=g,_=m.offset,y=this.isHorizontal(),v=this.ticks.length+(_?1:0),w=sN(m),M=[],O=b.setContext(this.getContext()),k=O.display?O.width:0,D=k/2,j=function(t){return ew(p,t,k)};if("top"===x)e=j(this.bottom),r=this.bottom-w,l=e-D,c=j(t.top)+D,u=t.bottom;else if("bottom"===x)e=j(this.top),c=t.top,u=j(t.bottom)-D,r=e+D,l=this.top+w;else if("left"===x)e=j(this.right),n=this.right-w,o=e-D,h=j(t.left)+D,d=t.right;else if("right"===x)e=j(this.left),h=t.left,d=j(t.right)-D,n=e+D,o=this.left+w;else if("x"===f){if("center"===x)e=j((t.top+t.bottom)/2+.5);else if(th(x)){let t=Object.keys(x)[0],i=x[t];e=j(this.chart.scales[t].getPixelForValue(i))}c=t.top,u=t.bottom,l=(r=e+D)+w}else if("y"===f){if("center"===x)e=j((t.left+t.right)/2);else if(th(x)){let t=Object.keys(x)[0],i=x[t];e=j(this.chart.scales[t].getPixelForValue(i))}o=(n=e-D)-w,h=t.left,d=t.right}let N=tu(g.ticks.maxTicksLimit,v),C=Math.max(1,Math.ceil(v/N));for(i=0;i<v;i+=C){let t=this.getContext(i),e=m.setContext(t),f=b.setContext(t),g=e.lineWidth,x=e.color,v=f.dash||[],w=f.dashOffset,O=e.tickWidth,k=e.tickColor,D=e.tickBorderDash||[],j=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s;let a=t.ticks.length,n=Math.min(e,a-1),r=t._startPixel,o=t._endPixel,l=t.getPixelForTick(n);if(!i||(s=1===a?Math.max(l-r,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,!((l+=n<e?s:-s)<r-1e-6)&&!(l>o+1e-6)))return l}(this,i,_))&&(a=ew(p,s,g),y?n=o=h=d=a:r=l=c=u=a,M.push({tx1:n,ty1:r,tx2:o,ty2:l,x1:h,y1:c,x2:d,y2:u,width:g,color:x,borderDash:v,borderDashOffset:w,tickWidth:O,tickColor:k,tickBorderDash:D,tickBorderDashOffset:j}))}return this._ticksLength=v,this._borderValue=e,M}_computeLabelItems(t){let e,i,s,a,n,r,o,l,h,c,d;let u=this.axis,f=this.options,{position:p,ticks:g}=f,m=this.isHorizontal(),x=this.ticks,{align:b,crossAlign:_,padding:y,mirror:v}=g,w=sN(f.grid),M=w+y,O=v?-y:M,k=-tH(this.labelRotation),D=[],j="middle";if("top"===p)n=this.bottom-O,r=this._getXAxisLabelAlignment();else if("bottom"===p)n=this.top+O,r=this._getXAxisLabelAlignment();else if("left"===p){let t=this._getYAxisLabelAlignment(w);r=t.textAlign,a=t.x}else if("right"===p){let t=this._getYAxisLabelAlignment(w);r=t.textAlign,a=t.x}else if("x"===u){if("center"===p)n=(t.top+t.bottom)/2+M;else if(th(p)){let t=Object.keys(p)[0],e=p[t];n=this.chart.scales[t].getPixelForValue(e)+M}r=this._getXAxisLabelAlignment()}else if("y"===u){if("center"===p)a=(t.left+t.right)/2-M;else if(th(p)){let t=Object.keys(p)[0],e=p[t];a=this.chart.scales[t].getPixelForValue(e)}r=this._getYAxisLabelAlignment(w).textAlign}"y"===u&&("start"===b?j="top":"end"===b&&(j="bottom"));let N=this._getLabelSizes();for(e=0,i=x.length;e<i;++e){let t;s=x[e].label;let u=g.setContext(this.getContext(e));o=this.getPixelForTick(e)+g.labelOffset,h=(l=this._resolveTickFontOptions(e)).lineHeight;let f=(c=tl(s)?s.length:1)/2,b=u.color,y=u.textStrokeColor,w=u.textStrokeWidth,M=r;if(m?(a=o,"inner"===r&&(M=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),d="top"===p?"near"===_||0!==k?-c*h+h/2:"center"===_?-N.highest.height/2-f*h+h:-N.highest.height+h/2:"near"===_||0!==k?h/2:"center"===_?N.highest.height/2-f*h:N.highest.height-c*h,v&&(d*=-1),0===k||u.showLabelBackdrop||(a+=h/2*Math.sin(k))):(n=o,d=(1-c)*h/2),u.showLabelBackdrop){let s=eU(u.backdropPadding),a=N.heights[e],n=N.widths[e],o=d-s.top,l=0-s.left;switch(j){case"middle":o-=a/2;break;case"bottom":o-=a}switch(r){case"center":l-=n/2;break;case"right":l-=n;break;case"inner":e===i-1?l-=n:e>0&&(l-=n/2)}t={left:l,top:o,width:n+s.width,height:a+s.height,color:u.backdropColor}}D.push({label:s,font:l,textOffset:d,options:{rotation:k,color:b,strokeColor:y,strokeWidth:w,textAlign:M,textBaseline:j,translation:[a,n],backdrop:t}})}return D}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-tH(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i;let{position:s,ticks:{crossAlign:a,mirror:n,padding:r}}=this.options,o=this._getLabelSizes(),l=t+r,h=o.widest.width;return"left"===s?n?(i=this.right+r,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?n?(i=this.left+r,"near"===a?e="right":"center"===a?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===a?e="left":"center"===a?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:a,height:n}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,a,n),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i;let s=this.options.grid,a=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),r=(t,e,i)=>{i.width&&i.color&&(a.save(),a.lineWidth=i.width,a.strokeStyle=i.color,a.setLineDash(i.borderDash||[]),a.lineDashOffset=i.borderDashOffset,a.beginPath(),a.moveTo(t.x,t.y),a.lineTo(e.x,e.y),a.stroke(),a.restore())};if(s.display)for(e=0,i=n.length;e<i;++e){let t=n[e];s.drawOnChartArea&&r({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&r({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s;let{chart:a,ctx:n,options:{border:r,grid:o}}=this,l=r.setContext(this.getContext()),h=r.display?l.width:0;if(!h)return;let c=o.setContext(this.getContext(0)).lineWidth,d=this._borderValue;this.isHorizontal()?(t=ew(a,this.left,h)-h/2,e=ew(a,this.right,c)+c/2,i=s=d):(i=ew(a,this.top,h)-h/2,s=ew(a,this.bottom,c)+c/2,t=e=d),n.save(),n.lineWidth=l.width,n.strokeStyle=l.color,n.beginPath(),n.moveTo(t,i),n.lineTo(e,s),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&ej(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;eS(e,s.label,0,s.textOffset,i,t)}i&&eN(e)}drawTitle(){let t;let{ctx:e,options:{position:i,title:s,reverse:a}}=this;if(!s.display)return;let n=ez(s.font),r=eU(s.padding),o=s.align,l=n.lineHeight/2;"bottom"===i||"center"===i||th(i)?(l+=r.bottom,tl(s.text)&&(l+=n.lineHeight*(s.text.length-1))):l+=r.top;let{titleX:h,titleY:c,maxWidth:d,rotation:u}=function(t,e,i,s){let a,n,r;let{top:o,left:l,bottom:h,right:c,chart:d}=t,{chartArea:u,scales:f}=d,p=0,g=h-o,m=c-l;if(t.isHorizontal()){if(n=t7(s,l,c),th(i)){let t=Object.keys(i)[0],s=i[t];r=f[t].getPixelForValue(s)+g-e}else r="center"===i?(u.bottom+u.top)/2+g-e:sk(t,i,e);a=c-l}else{if(th(i)){let t=Object.keys(i)[0],s=i[t];n=f[t].getPixelForValue(s)-m+e}else n="center"===i?(u.left+u.right)/2-m+e:sk(t,i,e);r=t7(s,h,o),p="left"===i?-tA:tA}return{titleX:n,titleY:r,maxWidth:a,rotation:p}}(this,l,i,o);eS(e,s.text,0,0,n,{color:s.color,maxWidth:d,rotation:u,textAlign:(t=t8(o),(a&&"right"!==i||!a&&"right"===i)&&(t=sO(t)),t),textBaseline:"middle",translation:[h,c]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=tu(t.grid&&t.grid.z,-1),s=tu(t.border&&t.border.z,0);return this._isVisible()&&this.draw===sE.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i;let s=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",n=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[a]!==this.id||t&&i.type!==t||n.push(i)}return n}_resolveTickFontOptions(t){return ez(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class sS{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){let e;let i=Object.getPrototypeOf(t);"id"in i&&"defaults"in i&&(e=this.register(i));let s=this.items,a=t.id,n=this.scope+"."+a;if(!a)throw Error("class does not have id: "+t);return a in s||(s[a]=t,function(t,e,i){let s=tv(Object.create(null),[i?ey.get(i):{},ey.get(e),t.defaults]);ey.set(e,s),t.defaultRoutes&&function(t,e){Object.keys(e).forEach(i=>{let s=i.split("."),a=s.pop(),n=[t].concat(s).join("."),r=e[i].split("."),o=r.pop(),l=r.join(".");ey.route(n,a,l,o)})}(e,t.defaultRoutes),t.descriptors&&ey.describe(e,t.descriptors)}(t,n,e),this.override&&ey.override(t.id,t.overrides)),n}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in ey[s]&&(delete ey[s][i],this.override&&delete eg[i])}}class sP{constructor(){this.controllers=new sS(iR,"datasets",!0),this.elements=new sS(sw,"elements"),this.plugins=new sS(Object,"plugins"),this.scales=new sS(sE,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):tm(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=tD(t);tg(i["before"+s],[],i),e[t](i),tg(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var sT=new sP;class sL{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let a=s?this._descriptors(t).filter(s):this._descriptors(t),n=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),n}_notify(t,e,i,s){for(let a of(s=s||{},t)){let t=a.plugin;if(!1===tg(t[i],[e,s,a.options],t)&&s.cancelable)return!1}return!0}invalidate(){to(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=tu(i.options&&i.options.plugins,{}),a=function(t){let e={},i=[],s=Object.keys(sT.plugins.items);for(let t=0;t<s.length;t++)i.push(sT.getPlugin(s[t]));let a=t.plugins||[];for(let t=0;t<a.length;t++){let s=a[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,a){let n=[],r=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],a||!1!==o?!0===o?{}:o:null);null!==h&&n.push({plugin:l,options:function(t,{plugin:e,local:i},s,a){let n=t.pluginScopeKeys(e),r=t.getOptionScopes(s,n);return i&&e.defaults&&r.push(e.defaults),t.createResolver(r,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,r)})}return n}(t,a,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function sA(t,e){let i=ey.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function sF(t){if("x"===t||"y"===t||"r"===t)return t}function sI(t,...e){if(sF(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&sF(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function sR(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function sU(t){let e=t.options||(t.options={});e.plugins=tu(e.plugins,{}),e.scales=function(t,e){let i=eg[t.type]||{scales:{}},s=e.scales||{},a=sA(t.type,e),n=Object.create(null);return Object.keys(s).forEach(e=>{let r=s[e];if(!th(r))return console.error(`Invalid scale configuration for scale: ${e}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=sI(e,r,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return sR(t,"x",i[0])||sR(t,"y",i[0])}return{}}(e,t),ey.scales[r.type]),l=o===a?"_index_":"_value_",h=i.scales||{};n[e]=tw(Object.create(null),[{axis:o},r,h[o],h[l]])}),t.data.datasets.forEach(i=>{let a=i.type||t.type,r=i.indexAxis||sA(a,e),o=(eg[a]||{}).scales||{};Object.keys(o).forEach(t=>{let e;let a=(e=t,"_index_"===t?e=r:"_value_"===t&&(e="x"===r?"y":"x"),e),l=i[a+"AxisID"]||a;n[l]=n[l]||Object.create(null),tw(n[l],[{axis:a},s[l],o[t]])})}),Object.keys(n).forEach(t=>{let e=n[t];tw(e,[ey.scales[e.type],ey.scale])}),n}(t,e)}function sz(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let sV=new Map,sB=new Set;function sW(t,e){let i=sV.get(t);return i||(i=e(),sV.set(t,i),sB.add(i)),i}let sH=(t,e,i)=>{let s=tk(e,i);void 0!==s&&t.add(s)};class s${constructor(t){this._config=function(t){return(t=t||{}).data=sz(t.data),sU(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=sz(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),sU(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return sW(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return sW(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return sW(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return sW(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:a}=this,n=this._cachedScopes(t,i),r=n.get(e);if(r)return r;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>sH(o,t,e))),e.forEach(t=>sH(o,s,t)),e.forEach(t=>sH(o,eg[a]||{},t)),e.forEach(t=>sH(o,ey,t)),e.forEach(t=>sH(o,em,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),sB.has(e)&&n.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,eg[e]||{},ey.datasets[e]||{},{type:e},ey,em]}resolveNamedOptions(t,e,i,s=[""]){let a={$shared:!0},{resolver:n,subPrefixes:r}=sY(this._resolverCache,t,s),o=n;if(function(t,e){let{isScriptable:i,isIndexable:s}=e$(t);for(let a of e){let e=i(a),n=s(a),r=(n||e)&&t[a];if(e&&(tN(r)||sZ(r))||n&&tl(r))return!0}return!1}(n,e)){a.$shared=!1,i=tN(i)?i():i;let e=this.createResolver(t,i,r);o=eH(n,i,e)}for(let t of e)a[t]=o[t];return a}createResolver(t,e,i=[""],s){let{resolver:a}=sY(this._resolverCache,t,i);return th(e)?eH(a,e,void 0,s):a}}function sY(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let a=i.join(),n=s.get(a);return n||(n={resolver:eW(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(a,n)),n}let sZ=t=>th(t)&&Object.getOwnPropertyNames(t).some(e=>tN(t[e])),sq=["top","bottom","left","right","chartArea"];function sX(t,e){return"top"===t||"bottom"===t||-1===sq.indexOf(t)&&"x"===e}function sJ(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function sG(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),tg(i&&i.onComplete,[t],e)}function sK(t){let e=t.chart,i=e.options.animation;tg(i&&i.onProgress,[t],e)}function sQ(t){return e4()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let s0={},s1=t=>{let e=sQ(t);return Object.values(s0).filter(t=>t.canvas===e).pop()};function s2(t,e,i){return t.options.clip?t[i]:e[i]}class s5{static defaults=ey;static instances=s0;static overrides=eg;static registry=sT;static version="4.4.8";static getChart=s1;static register(...t){sT.add(...t),s3()}static unregister(...t){sT.remove(...t),s3()}constructor(t,e){let i=this.config=new s$(e),s=sQ(t),a=s1(s);if(a)throw Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");let n=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!e4()||"undefined"!=typeof OffscreenCanvas&&s instanceof OffscreenCanvas?so:sv)),this.platform.updateConfig(i);let r=this.platform.acquireContext(s,n.aspectRatio),o=r&&r.canvas,l=o&&o.height,h=o&&o.width;if(this.id=tr(),this.ctx=r,this.canvas=o,this.width=h,this.height=l,this._options=n,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new sL,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}(t=>this.update(t),n.resizeDelay||0),this._dataChanges=[],s0[this.id]=this,!r||!o){console.error("Failed to create chart: can't acquire context from the given item");return}iw.listen(this,"complete",sG),iw.listen(this,"progress",sK),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:a}=this;return to(t)?e&&a?a:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return sT}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():ia(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return eM(this.canvas,this.ctx),this}stop(){return iw.stop(this),this}resize(t,e){iw.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,a=i.maintainAspectRatio&&this.aspectRatio,n=this.platform.getMaximumSize(s,t,e,a),r=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=n.width,this.height=n.height,this._aspectRatio=this.aspectRatio,ia(this,r,!0)&&(this.notifyPlugins("resize",{size:n}),tg(i.onResize,[this,n],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){tm(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),a=[];e&&(a=a.concat(Object.keys(e).map(t=>{let i=e[t],s=sI(t,i),a="r"===s,n="x"===s;return{options:i,dposition:a?"chartArea":n?"bottom":"left",dtype:a?"radialLinear":n?"category":"linear"}}))),tm(a,e=>{let a=e.options,n=a.id,r=sI(n,a),o=tu(a.type,e.dtype);(void 0===a.position||sX(a.position,r)!==sX(e.dposition))&&(a.position=e.dposition),s[n]=!0;let l=null;n in i&&i[n].type===o?l=i[n]:i[(l=new(sT.getScale(o))({id:n,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(a,t)}),tm(s,(t,e)=>{t||delete i[e]}),tm(i,t=>{sn.configure(this,t,t.options),sn.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(sJ("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e;let i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],a=this.getDatasetMeta(t),n=e.type||this.config.type;if(a.type&&a.type!==n&&(this._destroyDatasetMeta(t),a=this.getDatasetMeta(t)),a.type=n,a.indexAxis=e.indexAxis||sA(n,this.options),a.order=e.order||0,a.index=t,a.label=""+e.label,a.visible=this.isDatasetVisible(t),a.controller)a.controller.updateIndex(t),a.controller.linkScales();else{let e=sT.getController(n),{datasetElementType:s,dataElementType:r}=ey.datasets[n];Object.assign(e,{dataElementType:sT.getElement(r),datasetElementType:s&&sT.getElement(s)}),a.controller=new e(this,t),i.push(a.controller)}}return this._updateMetasets(),i}_resetElements(){tm(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let n=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===a.indexOf(e);e.buildOrUpdateElements(i),n=Math.max(+e.getMaxOverflow(),n)}n=this._minPadding=i.layout.autoPadding?n:0,this._updateLayout(n),s||tm(a,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(sJ("z","_idx"));let{_active:r,_lastEvent:o}=this;o?this._eventHandler(o,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){tm(this.scales,t=>{sn.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;tC(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:e,start:i,count:s}of this._getUniformDataChanges()||[])!function(t,e,i){for(let s of Object.keys(t)){let a=+s;if(a>=e){let n=t[s];delete t[s],(i>0||a>e)&&(t[a+i]=n)}}}(t,i,"_removeElements"===e?-s:s)}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!tC(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;sn.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],tm(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,tN(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(iw.has(this)?this.attached&&!iw.running(this)&&iw.start(this):(this.draw(),sG({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i;let s=this._sortedMetasets,a=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&a.push(i)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i=t._clip,s=!i.disabled,a=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:s2(i,e,"left"),right:s2(i,e,"right"),top:s2(s,e,"top"),bottom:s2(s,e,"bottom")}:e}(t,this.chartArea),n={meta:t,index:t.index,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetDraw",n)&&(s&&ej(e,{left:!1===i.left?0:a.left-i.left,right:!1===i.right?this.width:a.right+i.right,top:!1===i.top?0:a.top-i.top,bottom:!1===i.bottom?this.height:a.bottom+i.bottom}),t.controller.draw(),s&&eN(e),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(t){return eD(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let a=i4.modes[e];return"function"==typeof a?a(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=eB(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",a=this.getDatasetMeta(t),n=a.controller._resolveAnimations(void 0,s);tj(e)?(a.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),n.update(a,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),iw.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),eM(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete s0[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};tm(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},a=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},n=(t,e)=>{this.canvas&&this.resize(t,e)},r=()=>{a("attach",r),this.attached=!0,this.resize(),s("resize",n),s("detach",t)};t=()=>{this.attached=!1,a("resize",n),this._stop(),this._resize(0,0),s("attach",r)},i.isAttached(this.canvas)?r():t()}unbindEvents(){tm(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},tm(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,a,n;let r=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+r+"DatasetHoverStyle"](),a=0,n=t.length;a<n;++a){let e=(s=t[a])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[r+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});tx(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,a=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),n=a(e,t),r=i?t:a(t,e);n.length&&this.updateHoverStyle(n,s.mode,!1),r.length&&s.mode&&this.updateHoverStyle(r,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let a=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(a||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:a=[],options:n}=this,r=this._getActiveElements(t,a,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,tg(n.onHover,[t,r,this],this),o&&tg(n.onClick,[t,r,this],this));let h=!tx(r,a);return(h||e)&&(this._active=r,this._updateHoverStyles(r,a,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,s)}}function s3(){return tm(s5.instances,t=>t._plugins.invalidate())}function s4(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function s6(t,e,i,s,a,n){let{x:r,y:o,startAngle:l,pixelMargin:h,innerRadius:c}=e,d=Math.max(e.outerRadius+s+i-h,0),u=c>0?c+s+i+h:0,f=0,p=a-l;if(s){let t=d>0?d-s:0,e=((c>0?c-s:0)+t)/2;f=(p-(0!==e?p*e/(e+s):p))/2}let g=Math.max(.001,p*d-i/tE)/d,m=(p-g)/2,x=l+m+f,b=a-m-f,{outerStart:_,outerEnd:y,innerStart:v,innerEnd:w}=function(t,e,i,s){let a=eF(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),n=(i-e)/2,r=Math.min(n,s*e/2),o=t=>{let e=(i-Math.min(n,t))*s/2;return tG(t,0,Math.min(n,e))};return{outerStart:o(a.outerStart),outerEnd:o(a.outerEnd),innerStart:tG(a.innerStart,0,r),innerEnd:tG(a.innerEnd,0,r)}}(e,u,d,b-x),M=d-_,O=d-y,k=x+_/M,D=b-y/O,j=u+v,N=u+w,C=x+v/j,E=b-w/N;if(t.beginPath(),n){let e=(k+D)/2;if(t.arc(r,o,d,k,e),t.arc(r,o,d,e,D),y>0){let e=s4(O,D,r,o);t.arc(e.x,e.y,y,D,b+tA)}let i=s4(N,b,r,o);if(t.lineTo(i.x,i.y),w>0){let e=s4(N,E,r,o);t.arc(e.x,e.y,w,b+tA,E+Math.PI)}let s=(b-w/u+(x+v/u))/2;if(t.arc(r,o,u,b-w/u,s,!0),t.arc(r,o,u,s,x+v/u,!0),v>0){let e=s4(j,C,r,o);t.arc(e.x,e.y,v,C+Math.PI,x-tA)}let a=s4(M,x,r,o);if(t.lineTo(a.x,a.y),_>0){let e=s4(M,k,r,o);t.arc(e.x,e.y,_,x-tA,k)}}else{t.moveTo(r,o);let e=Math.cos(k)*d+r,i=Math.sin(k)*d+o;t.lineTo(e,i);let s=Math.cos(D)*d+r,a=Math.sin(D)*d+o;t.lineTo(s,a)}t.closePath()}class s8 extends sw{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:a}=tY(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:n,endAngle:r,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),c=(this.options.spacing+this.options.borderWidth)/2,d=tu(h,r-n),u=tJ(s,n,r)&&n!==r,f=d>=tS||u,p=tK(a,o+c,l+c);return f&&p}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:a,innerRadius:n,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+a)/2,c=(n+r+l+o)/2;return{x:e+Math.cos(h)*c,y:i+Math.sin(h)*c}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,a=(e.spacing||0)/2,n=e.circular;if(this.pixelMargin="inner"===e.borderAlign?.33:0,this.fullCircles=i>tS?Math.floor(i/tS):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let r=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(r)*s,Math.sin(r)*s);let o=s*(1-Math.sin(Math.min(tE,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,a){let{fullCircles:n,startAngle:r,circumference:o}=e,l=e.endAngle;if(n){s6(t,e,i,s,l,a);for(let e=0;e<n;++e)t.fill();isNaN(o)||(l=r+(o%tS||tS))}s6(t,e,i,s,l,a),t.fill()}(t,this,o,a,n),function(t,e,i,s,a){let{fullCircles:n,startAngle:r,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:c,borderDash:d,borderDashOffset:u}=l,f="inner"===l.borderAlign;if(!h)return;t.setLineDash(d||[]),t.lineDashOffset=u,f?(t.lineWidth=2*h,t.lineJoin=c||"round"):(t.lineWidth=h,t.lineJoin=c||"bevel");let p=e.endAngle;if(n){s6(t,e,i,s,p,a);for(let e=0;e<n;++e)t.stroke();isNaN(o)||(p=r+(o%tS||tS))}f&&function(t,e,i){let{startAngle:s,pixelMargin:a,x:n,y:r,outerRadius:o,innerRadius:l}=e,h=a/o;t.beginPath(),t.arc(n,r,o,s-h,i+h),l>a?(h=a/l,t.arc(n,r,l,i+h,s-h,!0)):t.arc(n,r,a,i+tA,s-tA),t.closePath(),t.clip()}(t,e,p),n||(s6(t,e,i,s,p,a),t.stroke())}(t,this,o,a,n),t.restore()}}function s7(t,e,i=e){t.lineCap=tu(i.borderCapStyle,e.borderCapStyle),t.setLineDash(tu(i.borderDash,e.borderDash)),t.lineDashOffset=tu(i.borderDashOffset,e.borderDashOffset),t.lineJoin=tu(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=tu(i.borderWidth,e.borderWidth),t.strokeStyle=tu(i.borderColor,e.borderColor)}function s9(t,e,i){t.lineTo(i.x,i.y)}function at(t,e,i={}){let s=t.length,{start:a=0,end:n=s-1}=i,{start:r,end:o}=e,l=Math.max(a,r),h=Math.min(n,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(a<r&&n<r||a>o&&n>o)?s+h-l:h-l}}function ae(t,e,i,s){let a,n,r;let{points:o,options:l}=e,{count:h,start:c,loop:d,ilen:u}=at(o,i,s),f=l.stepped?eC:l.tension||"monotone"===l.cubicInterpolationMode?eE:s9,{move:p=!0,reverse:g}=s||{};for(a=0;a<=u;++a)(n=o[(c+(g?u-a:a))%h]).skip||(p?(t.moveTo(n.x,n.y),p=!1):f(t,r,n,g,l.stepped),r=n);return d&&f(t,r,n=o[(c+(g?u:0))%h],g,l.stepped),!!d}function ai(t,e,i,s){let a,n,r,o,l,h;let c=e.points,{count:d,start:u,ilen:f}=at(c,i,s),{move:p=!0,reverse:g}=s||{},m=0,x=0,b=t=>(u+(g?f-t:t))%d,_=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(p&&(n=c[b(0)],t.moveTo(n.x,n.y)),a=0;a<=f;++a){if((n=c[b(a)]).skip)continue;let e=n.x,i=n.y,s=0|e;s===r?(i<o?o=i:i>l&&(l=i),m=(x*m+e)/++x):(_(),t.lineTo(e,i),r=s,x=0,o=l=i),h=i}_()}function as(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?ae:ai}let aa="function"==typeof Path2D;class an extends sw{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;(function(t,e,i,s,a){let n,r,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,a;let n=e5(e),r=t.length,o=Array(r).fill(0),l=Array(r),h=e2(t,0);for(i=0;i<r;++i)if(s=a,a=h,h=e2(t,i+1),a){if(h){let t=h[e]-a[e];o[i]=0!==t?(h[n]-a[n])/t:0}l[i]=s?h?tU(o[i-1])!==tU(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}(function(t,e,i){let s,a,n,r,o;let l=t.length,h=e2(t,0);for(let c=0;c<l-1;++c)if(o=h,h=e2(t,c+1),o&&h){if(tz(e[c],0,e1)){i[c]=i[c+1]=0;continue}(r=Math.pow(s=i[c]/e[c],2)+Math.pow(a=i[c+1]/e[c],2))<=9||(n=3/Math.sqrt(r),i[c]=s*n*e[c],i[c+1]=a*n*e[c])}})(t,o,l),function(t,e,i="x"){let s,a,n;let r=e5(i),o=t.length,l=e2(t,0);for(let h=0;h<o;++h){if(a=n,n=l,l=e2(t,h+1),!n)continue;let o=n[i],c=n[r];a&&(s=(o-a[i])/3,n[`cp1${i}`]=o-s,n[`cp1${r}`]=c-s*e[h]),l&&(s=(l[i]-o)/3,n[`cp2${i}`]=o+s,n[`cp2${r}`]=c+s*e[h])}}(t,l,e)}(t,a);else{let i=s?t[t.length-1]:t[0];for(n=0,r=t.length;n<r;++n)l=function(t,e,i,s){let a=t.skip?e:t,n=i.skip?e:i,r=tZ(e,a),o=tZ(n,e),l=r/(r+o),h=o/(r+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let c=s*l,d=s*h;return{previous:{x:e.x-c*(n.x-a.x),y:e.y-c*(n.y-a.y)},next:{x:e.x+d*(n.x-a.x),y:e.y+d*(n.y-a.y)}}}(i,o=t[n],t[Math.min(n+1,r-(s?0:1))%r],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,a,n,r;let o=eD(t[0],e);for(i=0,s=t.length;i<s;++i)r=n,n=o,o=i<s-1&&eD(t[i+1],e),n&&(a=t[i],r&&(a.cp1x=e3(a.cp1x,e.left,e.right),a.cp1y=e3(a.cp1y,e.top,e.bottom)),o&&(a.cp2x=e3(a.cp2x,e.left,e.right),a.cp2y=e3(a.cp2y,e.top,e.bottom)))}(t,i)})(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,a=i.length;if(!a)return[];let n=!!t._loop,{start:r,end:o}=function(t,e,i,s){let a=0,n=e-1;if(i&&!s)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(n+=a);n>a&&t[n%e].skip;)n--;return{start:a,end:n%=e}}(i,a,n,s);if(!0===s)return i_(t,[{start:r,end:o,loop:n}],i,e);let l=o<r?o+a:o,h=!!t._fullLoop&&0===r&&o===a-1;return i_(t,function(t,e,i,s){let a;let n=t.length,r=[],o=e,l=t[e];for(a=e+1;a<=i;++a){let i=t[a%n];i.skip||i.stop?l.skip||(s=!1,r.push({start:e%n,end:(a-1)%n,loop:s}),e=o=i.stop?a:null):(o=a,l.skip&&(e=a)),l=i}return null!==o&&r.push({start:e%n,end:o%n,loop:s}),r}(i,r,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s;let a=this.options,n=t[e],r=this.points,o=ib(this,{property:e,start:n,end:n});if(!o.length)return;let l=[],h=a.stepped?ih:a.tension||"monotone"===a.cubicInterpolationMode?ic:il;for(i=0,s=o.length;i<s;++i){let{start:s,end:c}=o[i],d=r[s],u=r[c];if(d===u){l.push(d);continue}let f=Math.abs((n-d[e])/(u[e]-d[e])),p=h(d,u,f,a.stepped);p[e]=t[e],l.push(p)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return as(this)(t,this,e,i)}path(t,e,i){let s=this.segments,a=as(this),n=this._loop;for(let r of(e=e||0,i=i||this.points.length-e,s))n&=a(t,this,r,{start:e,end:e+i-1});return!!n}draw(t,e,i,s){let a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),function(t,e,i,s){if(aa&&!e.options.segment){let a;(a=e._path)||(a=e._path=new Path2D,e.path(a,i,s)&&a.closePath()),s7(t,e.options),t.stroke(a)}else!function(t,e,i,s){let{segments:a,options:n}=e,r=as(e);for(let o of a)s7(t,n,o.style),t.beginPath(),r(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}(t,e,i,s)}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function ar(t,e,i,s){let a=t.options,{[i]:n}=t.getProps([i],s);return Math.abs(e-n)<a.radius+a.hitRadius}class ao extends sw{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:a,y:n}=this.getProps(["x","y"],i);return Math.pow(t-a,2)+Math.pow(e-n,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return ar(this,t,"x",e)}inYRange(t,e){return ar(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&eD(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,eO(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function al(t,e){let i,s,a,n,r;let{x:o,y:l,base:h,width:c,height:d}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(r=d/2,i=Math.min(o,h),s=Math.max(o,h),a=l-r,n=l+r):(i=o-(r=c/2),s=o+r,a=Math.min(l,h),n=Math.max(l,h)),{left:i,top:a,right:s,bottom:n}}function ah(t,e,i,s){return t?0:tG(e,i,s)}function ac(t,e,i,s){let a=null===e,n=null===i,r=t&&!(a&&n)&&al(t,s);return r&&(a||tK(e,r.left,r.right))&&(n||tK(i,r.top,r.bottom))}function ad(t,e){t.rect(e.x,e.y,e.w,e.h)}function au(t,e,i={}){let s=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,n=(t.x+t.w!==i.x+i.w?e:0)-s,r=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+s,y:t.y+a,w:t.w+n,h:t.h+r,radius:t.radius}}class af extends sw{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:a}}=this,{inner:n,outer:r}=function(t){let e=al(t),i=e.right-e.left,s=e.bottom-e.top,a=function(t,e,i){let s=t.options.borderWidth,a=t.borderSkipped,n=eI(s);return{t:ah(a.top,n.top,0,i),r:ah(a.right,n.right,0,e),b:ah(a.bottom,n.bottom,0,i),l:ah(a.left,n.left,0,e)}}(t,i/2,s/2),n=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),a=t.options.borderRadius,n=eR(a),r=Math.min(e,i),o=t.borderSkipped,l=s||th(a);return{topLeft:ah(!l||o.top||o.left,n.topLeft,0,r),topRight:ah(!l||o.top||o.right,n.topRight,0,r),bottomLeft:ah(!l||o.bottom||o.left,n.bottomLeft,0,r),bottomRight:ah(!l||o.bottom||o.right,n.bottomRight,0,r)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:n},inner:{x:e.left+a.l,y:e.top+a.t,w:i-a.l-a.r,h:s-a.t-a.b,radius:{topLeft:Math.max(0,n.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,n.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,n.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,n.bottomRight-Math.max(a.b,a.r))}}}}(this),o=(e=r.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?eP:ad;t.save(),(r.w!==n.w||r.h!==n.h)&&(t.beginPath(),o(t,au(r,i,n)),t.clip(),o(t,au(n,-i,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,au(n,i)),t.fillStyle=a,t.fill(),t.restore()}inRange(t,e,i){return ac(this,t,e,i)}inXRange(t,e){return ac(this,t,null,e)}inYRange(t,e){return ac(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(e+s)/2:e,y:a?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}function ap(t,e,i,s){if(s)return;let a=e[t],n=i[t];return"angle"===t&&(a=tX(a),n=tX(n)),{property:t,start:a,end:n}}function ag(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function am(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}t=>t.replace("rgb(","rgba(").replace(")",", 0.5)");function ax(t,e,i,s){let a=e.interpolate(i,s);a&&t.lineTo(a.x,a.y)}let ab=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},a_=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class ay extends sw{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=tg(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e;let{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let a=i.labels,n=ez(a.font),r=n.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=ab(a,r);s.font=n.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,r,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,n,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:a,maxWidth:n,options:{labels:{padding:r}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+r,c=t;a.textAlign="left",a.textBaseline="middle";let d=-1,u=-h;return this.legendItems.forEach((t,f)=>{let p=i+e/2+a.measureText(t.text).width;(0===f||l[l.length-1]+p+2*r>n)&&(c+=h,l[l.length-(f>0?0:1)]=0,u+=h,d++),o[f]={left:0,top:u,row:d,width:p,height:s},l[l.length-1]+=p+r}),c}_fitCols(t,e,i,s){let{ctx:a,maxHeight:n,options:{labels:{padding:r}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=n-t,c=r,d=0,u=0,f=0,p=0;return this.legendItems.forEach((t,n)=>{var g;let m,x;let{itemWidth:b,itemHeight:_}={itemWidth:((m=t.text)&&"string"!=typeof m&&(m=m.reduce((t,e)=>t.length>e.length?t:e)),i+e.size/2+a.measureText(m).width),itemHeight:(g=e.lineHeight,x=s,"string"!=typeof t.text&&(x=av(t,g)),x)};n>0&&u+_+2*r>h&&(c+=d+r,l.push({width:d,height:u}),f+=d+r,p++,d=u=0),o[n]={left:f,top:u,col:p,width:b,height:_},d=Math.max(d,b),u+=_+r}),c+=d,l.push({width:d,height:u}),c}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:a}}=this,n=id(a,this.left,this.width);if(this.isHorizontal()){let a=0,r=t7(i,this.left+s,this.right-this.lineWidths[a]);for(let o of e)a!==o.row&&(a=o.row,r=t7(i,this.left+s,this.right-this.lineWidths[a])),o.top+=this.top+t+s,o.left=n.leftForLtr(n.x(r),o.width),r+=o.width+s}else{let a=0,r=t7(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(let o of e)o.col!==a&&(a=o.col,r=t7(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),o.top=r,o.left+=this.left+s,o.left=n.leftForLtr(n.x(o.left),o.width),r+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;ej(t,this),this._draw(),eN(t)}}_draw(){let t;let{options:e,columnSizes:i,lineWidths:s,ctx:a}=this,{align:n,labels:r}=e,o=ey.color,l=id(e.rtl,this.left,this.width),h=ez(r.font),{padding:c}=r,d=h.size,u=d/2;this.drawTitle(),a.textAlign=l.textAlign("left"),a.textBaseline="middle",a.lineWidth=.5,a.font=h.string;let{boxWidth:f,boxHeight:p,itemHeight:g}=ab(r,d),m=function(t,e,i){if(isNaN(f)||f<=0||isNaN(p)||p<0)return;a.save();let s=tu(i.lineWidth,1);if(a.fillStyle=tu(i.fillStyle,o),a.lineCap=tu(i.lineCap,"butt"),a.lineDashOffset=tu(i.lineDashOffset,0),a.lineJoin=tu(i.lineJoin,"miter"),a.lineWidth=s,a.strokeStyle=tu(i.strokeStyle,o),a.setLineDash(tu(i.lineDash,[])),r.usePointStyle)ek(a,{radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s},l.xPlus(t,f/2),e+u,r.pointStyleWidth&&f);else{let n=e+Math.max((d-p)/2,0),r=l.leftForLtr(t,f),o=eR(i.borderRadius);a.beginPath(),Object.values(o).some(t=>0!==t)?eP(a,{x:r,y:n,w:f,h:p,radius:o}):a.rect(r,n,f,p),a.fill(),0!==s&&a.stroke()}a.restore()},x=function(t,e,i){eS(a,i.text,t,e+g/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},b=this.isHorizontal(),_=this._computeTitleHeight();t=b?{x:t7(n,this.left+c,this.right-s[0]),y:this.top+c+_,line:0}:{x:this.left+c,y:t7(n,this.top+_+c,this.bottom-i[0].height),line:0},iu(this.ctx,e.textDirection);let y=g+c;this.legendItems.forEach((o,d)=>{a.strokeStyle=o.fontColor,a.fillStyle=o.fontColor;let p=a.measureText(o.text).width,g=l.textAlign(o.textAlign||(o.textAlign=r.textAlign)),v=f+u+p,w=t.x,M=t.y;if(l.setWidth(this.width),b?d>0&&w+v+c>this.right&&(M=t.y+=y,t.line++,w=t.x=t7(n,this.left+c,this.right-s[t.line])):d>0&&M+y>this.bottom&&(w=t.x=w+i[t.line].width+c,t.line++,M=t.y=t7(n,this.top+_+c,this.bottom-i[t.line].height)),m(l.x(w),M,o),w=t9(g,w+f+u,b?w+v:this.right,e.rtl),x(l.x(w),M,o),b)t.x+=v+c;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=av(o,e)+c}else t.y+=y}),ip(this.ctx,e.textDirection)}drawTitle(){let t;let e=this.options,i=e.title,s=ez(i.font),a=eU(i.padding);if(!i.display)return;let n=id(e.rtl,this.left,this.width),r=this.ctx,o=i.position,l=s.size/2,h=a.top+l,c=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),t=this.top+h,c=t7(e.align,c,this.right-d);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+t7(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let u=t7(o,c,c+d);r.textAlign=n.textAlign(t8(o)),r.textBaseline="middle",r.strokeStyle=i.color,r.fillStyle=i.color,r.font=s.string,eS(r,i.text,u,t,s)}_computeTitleHeight(){let t=this.options.title,e=ez(t.font),i=eU(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,a;if(tK(t,this.left,this.right)&&tK(e,this.top,this.bottom)){for(i=0,a=this.legendHitBoxes;i<a.length;++i)if(tK(t,(s=a[i]).left,s.left+s.width)&&tK(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e;let i=this.options;if(("mousemove"!==(e=t.type)&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let s=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,a=a_(e,s);e&&!a&&tg(i.onLeave,[t,e,this],this),this._hoveredItem=s,s&&!a&&tg(i.onHover,[t,s,this],this)}else s&&tg(i.onClick,[t,s,this],this)}}function av(t,e){return e*(t.text?t.text.length:0)}new WeakMap;let aw={average(t){let e,i;if(!t.length)return!1;let s=new Set,a=0,n=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),a+=t.y,++n}}return 0!==n&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:a/n}},nearest(t,e){let i,s,a;if(!t.length)return!1;let n=e.x,r=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=tZ(e,s.getCenterPoint());t<o&&(o=t,a=s)}}if(a){let t=a.tooltipPosition();n=t.x,r=t.y}return{x:n,y:r}}};function aM(t,e){return e&&(tl(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function aO(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function ak(t,e){let i=t.chart.ctx,{body:s,footer:a,title:n}=t,{boxWidth:r,boxHeight:o}=e,l=ez(e.bodyFont),h=ez(e.titleFont),c=ez(e.footerFont),d=n.length,u=a.length,f=s.length,p=eU(e.padding),g=p.height,m=0,x=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);x+=t.beforeBody.length+t.afterBody.length,d&&(g+=d*h.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),x&&(g+=f*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(x-f)*l.lineHeight+(x-1)*e.bodySpacing),u&&(g+=e.footerMarginTop+u*c.lineHeight+(u-1)*e.footerSpacing);let b=0,_=function(t){m=Math.max(m,i.measureText(t).width+b)};return i.save(),i.font=h.string,tm(t.title,_),i.font=l.string,tm(t.beforeBody.concat(t.afterBody),_),b=e.displayColors?r+2+e.boxPadding:0,tm(s,t=>{tm(t.before,_),tm(t.lines,_),tm(t.after,_)}),b=0,i.font=c.string,tm(t.footer,_),i.restore(),{width:m+=p.width,height:g}}function aD(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:a,width:n}=i,{width:r,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=a<=(o+l)/2?"left":"right":a<=n/2?h="left":a>=r-n/2&&(h="right"),function(t,e,i,s){let{x:a,width:n}=s,r=i.caretSize+i.caretPadding;if("left"===t&&a+n+r>e.width||"right"===t&&a-n-r<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function aj(t,e,i,s){let{caretSize:a,caretPadding:n,cornerRadius:r}=t,{xAlign:o,yAlign:l}=i,h=a+n,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=eR(r),p=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),g=function(t,e,i){let{y:s,height:a}=t;return"top"===e?s+=i:"bottom"===e?s-=a+i:s-=a/2,s}(e,l,h);return"center"===l?"left"===o?p+=h:"right"===o&&(p-=h):"left"===o?p-=Math.max(c,u)+a:"right"===o&&(p+=Math.max(d,f)+a),{x:tG(p,0,s.width-e.width),y:tG(g,0,s.height-e.height)}}function aN(t,e,i){let s=eU(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function aC(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let aE={beforeTitle:tn,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:tn,beforeBody:tn,beforeLabel:tn,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return to(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:tn,afterBody:tn,beforeFooter:tn,footer:tn,afterFooter:tn};function aS(t,e,i,s){let a=t[e].call(i,s);return void 0===a?aE[e].call(i,s):a}class aP extends sw{static positioners=aw;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,a=new iD(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=eB(this.chart.getContext(),{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"}))}getTitle(t,e){let{callbacks:i}=e,s=aS(i,"beforeTitle",this,t),a=aS(i,"title",this,t),n=aS(i,"afterTitle",this,t),r=[];return r=aM(r,aO(s)),r=aM(r,aO(a)),r=aM(r,aO(n))}getBeforeBody(t,e){return aM([],aO(aS(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return tm(t,t=>{let e={before:[],lines:[],after:[]},a=aC(i,t);aM(e.before,aO(aS(a,"beforeLabel",this,t))),aM(e.lines,aS(a,"label",this,t)),aM(e.after,aO(aS(a,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return aM([],aO(aS(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=aS(i,"beforeFooter",this,t),a=aS(i,"footer",this,t),n=aS(i,"afterFooter",this,t),r=[];return r=aM(r,aO(s)),r=aM(r,aO(a)),r=aM(r,aO(n))}_createItems(t){let e,i;let s=this._active,a=this.chart.data,n=[],r=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:a}=e,n=t.getDatasetMeta(s).controller,{label:r,value:o}=n.getLabelAndValue(a);return{chart:t,label:r,parsed:n.getParsed(a),raw:t.data.datasets[s].data[a],formattedValue:o,dataset:n.getDataset(),dataIndex:a,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,a))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,a))),tm(l,e=>{let i=aC(t.callbacks,e);n.push(aS(i,"labelColor",this,e)),r.push(aS(i,"labelPointStyle",this,e)),o.push(aS(i,"labelTextColor",this,e))}),this.labelColors=n,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i;let s=this.options.setContext(this.getContext()),a=this._active,n=[];if(a.length){let t=aw[s.position].call(this,a,this._eventPosition);n=this._createItems(s),this.title=this.getTitle(n,s),this.beforeBody=this.getBeforeBody(n,s),this.body=this.getBody(n,s),this.afterBody=this.getAfterBody(n,s),this.footer=this.getFooter(n,s);let e=this._size=ak(this,s),r=Object.assign({},t,e),o=aD(this.chart,s,r),l=aj(s,r,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=n,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let a=this.getCaretPosition(t,i,s);e.lineTo(a.x1,a.y1),e.lineTo(a.x2,a.y2),e.lineTo(a.x3,a.y3)}getCaretPosition(t,e,i){let s,a,n,r,o,l;let{xAlign:h,yAlign:c}=this,{caretSize:d,cornerRadius:u}=i,{topLeft:f,topRight:p,bottomLeft:g,bottomRight:m}=eR(u),{x:x,y:b}=t,{width:_,height:y}=e;return"center"===c?(o=b+y/2,"left"===h?(a=(s=x)-d,r=o+d,l=o-d):(a=(s=x+_)+d,r=o-d,l=o+d),n=s):(a="left"===h?x+Math.max(f,g)+d:"right"===h?x+_-Math.max(p,m)-d:this.caretX,"top"===c?(o=(r=b)-d,s=a-d,n=a+d):(o=(r=b+y)+d,s=a+d,n=a-d),l=r),{x1:s,x2:a,x3:n,y1:r,y2:o,y3:l}}drawTitle(t,e,i){let s,a,n;let r=this.title,o=r.length;if(o){let l=id(i.rtl,this.x,this.width);for(n=0,t.x=aN(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=ez(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;n<o;++n)e.fillText(r[n],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+a,n+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,a){let n=this.labelColors[i],r=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=a,h=ez(a.bodyFont),c=aN(this,"left",a),d=s.x(c),u=o<h.lineHeight?(h.lineHeight-o)/2:0,f=e.y+u;if(a.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},i=s.leftForLtr(d,l)+l/2,h=f+o/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,eO(t,e,i,h),t.strokeStyle=n.borderColor,t.fillStyle=n.backgroundColor,eO(t,e,i,h)}else{t.lineWidth=th(n.borderWidth)?Math.max(...Object.values(n.borderWidth)):n.borderWidth||1,t.strokeStyle=n.borderColor,t.setLineDash(n.borderDash||[]),t.lineDashOffset=n.borderDashOffset||0;let e=s.leftForLtr(d,l),i=s.leftForLtr(s.xPlus(d,1),l-2),r=eR(n.borderRadius);Object.values(r).some(t=>0!==t)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,eP(t,{x:e,y:f,w:l,h:o,radius:r}),t.fill(),t.stroke(),t.fillStyle=n.backgroundColor,t.beginPath(),eP(t,{x:i,y:f+1,w:l-2,h:o-2,radius:r}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(e,f,l,o),t.strokeRect(e,f,l,o),t.fillStyle=n.backgroundColor,t.fillRect(i,f+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,a,n,r,o,l,h;let{body:c}=this,{bodySpacing:d,bodyAlign:u,displayColors:f,boxHeight:p,boxWidth:g,boxPadding:m}=i,x=ez(i.bodyFont),b=x.lineHeight,_=0,y=id(i.rtl,this.x,this.width),v=function(i){e.fillText(i,y.x(t.x+_),t.y+b/2),t.y+=b+d},w=y.textAlign(u);for(e.textAlign=u,e.textBaseline="middle",e.font=x.string,t.x=aN(this,w,i),e.fillStyle=i.bodyColor,tm(this.beforeBody,v),_=f&&"right"!==w?"center"===u?g/2+m:g+2+m:0,r=0,l=c.length;r<l;++r){for(s=c[r],a=this.labelTextColors[r],e.fillStyle=a,tm(s.before,v),n=s.lines,f&&n.length&&(this._drawColorBox(e,t,r,y,i),b=Math.max(x.lineHeight,p)),o=0,h=n.length;o<h;++o)v(n[o]),b=x.lineHeight;tm(s.after,v)}_=0,b=x.lineHeight,tm(this.afterBody,v),t.y-=d}drawFooter(t,e,i){let s,a;let n=this.footer,r=n.length;if(r){let o=id(i.rtl,this.x,this.width);for(t.x=aN(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=ez(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,a=0;a<r;++a)e.fillText(n[a],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:a,yAlign:n}=this,{x:r,y:o}=t,{width:l,height:h}=i,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=eR(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(r+c,o),"top"===n&&this.drawCaret(t,e,i,s),e.lineTo(r+l-d,o),e.quadraticCurveTo(r+l,o,r+l,o+d),"center"===n&&"right"===a&&this.drawCaret(t,e,i,s),e.lineTo(r+l,o+h-f),e.quadraticCurveTo(r+l,o+h,r+l-f,o+h),"bottom"===n&&this.drawCaret(t,e,i,s),e.lineTo(r+u,o+h),e.quadraticCurveTo(r,o+h,r,o+h-u),"center"===n&&"left"===a&&this.drawCaret(t,e,i,s),e.lineTo(r,o+c),e.quadraticCurveTo(r,o,r+c,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,a=i&&i.y;if(s||a){let i=aw[t.position].call(this,this._active,this._eventPosition);if(!i)return;let n=this._size=ak(this,t),r=Object.assign({},i,this._size),o=aD(e,t,r),l=aj(t,r,o,e);(s._to!==l.x||a._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=n.width,this.height=n.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},a={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let n=eU(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=i,this.drawBackground(a,t,s,e),iu(t,e.textDirection),a.y+=n.top,this.drawTitle(a,t,e),this.drawBody(a,t,e),this.drawFooter(a,t,e),ip(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),a=!tx(i,s),n=this._positionChanged(s,e);(a||n)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,a=this._active||[],n=this._getActiveElements(t,a,e,i),r=this._positionChanged(n,t),o=e||!tx(n,a)||r;return o&&(this._active=n,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let a=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let n=this.chart.getElementsAtEventForMode(t,a.mode,a,i);return a.reverse&&n.reverse(),n}_positionChanged(t,e){let{caretX:i,caretY:s,options:a}=this,n=aw[a.position].call(this,t,e);return!1!==n&&(i!==n.x||s!==n.y)}}let aT=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),aL=(t,e)=>null===t?null:tG(Math.round(t),0,e);function aA(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class aF extends sE{static id="category";static defaults={ticks:{callback:aA}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(to(t))return null;let i=this.getLabels();return aL(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let a=t.indexOf(e);return -1===a?aT(t,e,i,s):a!==t.lastIndexOf(e)?i:a}(i,t,tu(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"!==this.options.bounds||(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],a=this.getLabels();a=0===t&&e===a.length-1?a:a.slice(t,e+1),this._valueRange=Math.max(a.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return aA.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function aI(t,e,{horizontal:i,minRotation:s}){let a=tH(s),n=.75*e*(""+t).length;return Math.min(e/((i?Math.sin(a):Math.cos(a))||.001),n)}class aR extends sE{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return to(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:a}=this,n=t=>s=e?s:t,r=t=>a=i?a:t;if(t){let t=tU(s),e=tU(a);t<0&&e<0?r(0):t>0&&e>0&&n(0)}if(s===a){let e=0===a?1:Math.abs(.05*a);r(a+e),t||n(s-e)}this.min=s,this.max=a}getTickLimit(){let t;let{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,a,n;let r=[],{bounds:o,step:l,min:h,max:c,precision:d,count:u,maxTicks:f,maxDigits:p,includeBounds:g}=t,m=l||1,x=f-1,{min:b,max:_}=e,y=!to(h),v=!to(c),w=!to(u),M=(_-b)/(p+1),O=tV((_-b)/x/m)*m;if(O<1e-14&&!y&&!v)return[{value:b},{value:_}];(n=Math.ceil(_/O)-Math.floor(b/O))>x&&(O=tV(n*O/x/m)*m),to(d)||(O=Math.ceil(O*(i=Math.pow(10,d)))/i),"ticks"===o?(s=Math.floor(b/O)*O,a=Math.ceil(_/O)*O):(s=b,a=_),y&&v&&l&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((c-h)/l,O/1e3)?(n=Math.round(Math.min((c-h)/O,f)),O=(c-h)/n,s=h,a=c):w?(s=y?h:s,O=((a=v?c:a)-s)/(n=u-1)):n=tz(n=(a-s)/O,Math.round(n),O/1e3)?Math.round(n):Math.ceil(n);let k=Math.max(t$(O),t$(s));s=Math.round(s*(i=Math.pow(10,to(d)?k:d)))/i,a=Math.round(a*i)/i;let D=0;for(y&&(g&&s!==h?(r.push({value:h}),s<h&&D++,tz(Math.round((s+D*O)*i)/i,h,aI(h,M,t))&&D++):s<h&&D++);D<n;++D){let t=Math.round((s+D*O)*i)/i;if(v&&t>c)break;r.push({value:t})}return v&&g&&a!==c?r.length&&tz(r[r.length-1].value,c,aI(c,M,t))?r[r.length-1].value=c:r.push({value:c}):v&&a!==c||r.push({value:a}),r}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&tW(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return eu(t,this.chart.options.locale,this.options.ticks.format)}}class aU extends aR{static id="linear";static defaults={ticks:{callback:ep.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=tc(t)?t:0,this.max=tc(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=tH(this.options.ticks.minRotation);return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/((t?Math.sin(i):Math.cos(i))||.001)))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let az=t=>Math.floor(tR(t)),aV=(t,e)=>Math.pow(10,az(t)+e);function aB(t){return 1==t/Math.pow(10,az(t))}function aW(t,e,i){let s=Math.pow(10,i);return Math.ceil(e/s)-Math.floor(t/s)}class aH extends sE{static id="logarithmic";static defaults={ticks:{callback:ep.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=aR.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return tc(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=tc(t)?Math.max(0,t):null,this.max=tc(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!tc(this._userMin)&&(this.min=t===aV(this.min,0)?aV(this.min,-1):aV(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,a=e=>i=t?i:e,n=t=>s=e?s:t;i===s&&(i<=0?(a(1),n(10)):(a(aV(i,-1)),n(aV(s,1)))),i<=0&&a(aV(s,-1)),s<=0&&n(aV(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=td(t.min,e);let s=[],a=az(e),n=function(t,e){let i=az(e-t);for(;aW(t,e,i)>10;)i++;for(;10>aW(t,e,i);)i--;return Math.min(i,az(t))}(e,i),r=n<0?Math.pow(10,Math.abs(n)):1,o=Math.pow(10,n),l=a>n?Math.pow(10,a):0,h=Math.round((e-l)*r)/r,c=Math.floor((e-l)/o/10)*o*10,d=Math.floor((h-c)/Math.pow(10,n)),u=td(t.min,Math.round((l+c+d*Math.pow(10,n))*r)/r);for(;u<i;)s.push({value:u,major:aB(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(d=2,r=++n>=0?1:r),u=Math.round((l+c+d*Math.pow(10,n))*r)/r;let f=td(t.max,u);return s.push({value:f,major:aB(f),significand:d}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&tW(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":eu(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=tR(t),this._valueRange=tR(this.max)-tR(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(tR(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function a$(t){let e=t.ticks;if(e.display&&t.display){let t=eU(e.backdropPadding);return tu(e.font&&e.font.size,ey.font.size)+t.height}return 0}function aY(t,e,i,s,a){return t===s||t===a?{start:e-i/2,end:e+i/2}:t<s||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function aZ(t,e,i,s){let{ctx:a}=t;if(i)a.arc(t.xCenter,t.yCenter,e,0,tS);else{let i=t.getPointPosition(0,e);a.moveTo(i.x,i.y);for(let n=1;n<s;n++)i=t.getPointPosition(n,e),a.lineTo(i.x,i.y)}}class aq extends aR{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:ep.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=eU(a$(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=tc(t)&&!isNaN(t)?t:0,this.max=tc(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/a$(this.options))}generateTickLabels(t){aR.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=tg(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],a=[],n=t._pointLabels.length,r=t.options.pointLabels,o=r.centerPointLabels?tE/n:0;for(let c=0;c<n;c++){var l,h;let n=r.setContext(t.getPointLabelContext(c));a[c]=n.padding;let d=t.getPointPosition(c,t.drawingArea+a[c],o),u=ez(n.font),f=(l=t.ctx,h=tl(h=t._pointLabels[c])?h:[h],{w:function(t,e,i,s){let a,n,r,o,l;let h=(s=s||{}).data=s.data||{},c=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},c=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let d=0,u=i.length;for(a=0;a<u;a++)if(null==(o=i[a])||tl(o)){if(tl(o))for(n=0,r=o.length;n<r;n++)null==(l=o[n])||tl(l)||(d=ev(t,h,c,d,l))}else d=ev(t,h,c,d,o);t.restore();let f=c.length/2;if(f>i.length){for(a=0;a<f;a++)delete h[c[a]];c.splice(0,f)}return d}(l,u.string,h),h:h.length*u.lineHeight});s[c]=f;let p=tX(t.getIndexAngle(c)+o),g=Math.round(180/tE*p);(function(t,e,i,s,a){let n=Math.abs(Math.sin(i)),r=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/n,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/n,t.r=Math.max(t.r,e.r+o)),a.start<e.t?(l=(e.t-a.start)/r,t.t=Math.min(t.t,e.t-l)):a.end>e.b&&(l=(a.end-e.b)/r,t.b=Math.max(t.b,e.b+l))})(i,e,p,aY(g,d.x,f.w,0,180),aY(g,d.y,f.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s;let a=[],n=t._pointLabels.length,r=t.options,{centerPointLabels:o,display:l}=r.pointLabels,h={extra:a$(r)/2,additionalAngle:o?tE/n:0};for(let r=0;r<n;r++){h.padding=i[r],h.size=e[r];let n=function(t,e,i){var s,a,n,r;let o=t.drawingArea,{extra:l,additionalAngle:h,padding:c,size:d}=i,u=t.getPointPosition(e,o+l+c,h),f=Math.round(180/tE*tX(u.angle+tA)),p=(s=u.y,a=d.h,90===f||270===f?s-=a/2:(f>270||f<90)&&(s-=a),s),g=0===f||180===f?"center":f<180?"left":"right",m=(n=u.x,r=d.w,"right"===g?n-=r:"center"===g&&(n-=r/2),n);return{visible:!0,x:u.x,y:p,textAlign:g,left:m,top:p,right:m+d.w,bottom:p+d.h}}(t,r,h);a.push(n),"auto"===l&&(n.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:a,bottom:n}=t;return!(eD({x:i,y:s},e)||eD({x:i,y:n},e)||eD({x:a,y:s},e)||eD({x:a,y:n},e))}(n,s),n.visible&&(s=n))}return a}(t,s,a)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return tX(tS/(this._pointLabels.length||1)*t+tH(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(to(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(to(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let i=e[t];return eB(this.getContext(),{label:i,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-tA+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:a}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:a}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),aZ(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i;let s=this.ctx,a=this.options,{angleLines:n,grid:r,border:o}=a,l=this._pointLabels.length;if(a.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let a=e-1;a>=0;a--){let e=t._pointLabelItems[a];if(!e.visible)continue;let n=s.setContext(t.getPointLabelContext(a));!function(t,e,i){let{left:s,top:a,right:n,bottom:r}=i,{backdropColor:o}=e;if(!to(o)){let i=eR(e.borderRadius),l=eU(e.backdropPadding);t.fillStyle=o;let h=s-l.left,c=a-l.top,d=n-s+l.width,u=r-a+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),eP(t,{x:h,y:c,w:d,h:u,radius:i}),t.fill()):t.fillRect(h,c,d,u)}}(i,n,e);let r=ez(n.font),{x:o,y:l,textAlign:h}=e;eS(i,t._pointLabels[a],o,l+r.lineHeight/2,r,{color:n.color,textAlign:h,textBaseline:"middle"})}}(this,l),r.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),a=r.setContext(s),n=o.setContext(s);!function(t,e,i,s,a){let n=t.ctx,r=e.circular,{color:o,lineWidth:l}=e;(r||s)&&o&&l&&!(i<0)&&(n.save(),n.strokeStyle=o,n.lineWidth=l,n.setLineDash(a.dash||[]),n.lineDashOffset=a.dashOffset,n.beginPath(),aZ(t,i,r,s),n.closePath(),n.stroke(),n.restore())}(this,a,e,l,n)}}),n.display){for(s.save(),t=l-1;t>=0;t--){let r=n.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=r;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(r.borderDash),s.lineDashOffset=r.borderDashOffset,e=this.getDistanceFromCenterForValue(a.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e;let i=this.ctx,s=this.options,a=s.ticks;if(!a.display)return;let n=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((n,r)=>{if(0===r&&this.min>=0&&!s.reverse)return;let o=a.setContext(this.getContext(r)),l=ez(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[r].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(n.label).width,i.fillStyle=o.backdropColor;let s=eU(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}eS(i,n.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let aX={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},aJ=Object.keys(aX);function aG(t,e){return t-e}function aK(t,e){if(to(e))return null;let i=t._adapter,{parser:s,round:a,isoWeekday:n}=t._parseOpts,r=e;return("function"==typeof s&&(r=s(r)),tc(r)||(r="string"==typeof s?i.parse(r,s):i.parse(r)),null===r)?null:(a&&(r="week"===a&&(tB(n)||!0===n)?i.startOf(r,"isoWeek",n):i.startOf(r,a)),+r)}function aQ(t,e,i,s){let a=aJ.length;for(let n=aJ.indexOf(t);n<a-1;++n){let t=aX[aJ[n]],a=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(a*t.size))<=s)return aJ[n]}return aJ[a-1]}function a0(t,e,i){if(i){if(i.length){let{lo:s,hi:a}=tQ(i,e);t[i[s]>=e?i[s]:i[a]]=!0}}else t[e]=!0}function a1(t,e,i){let s,a;let n=[],r={},o=e.length;for(s=0;s<o;++s)r[a=e[s]]=s,n.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){let a,n;let r=t._adapter,o=+r.startOf(e[0].value,s),l=e[e.length-1].value;for(a=o;a<=l;a=+r.add(a,1,s))(n=i[a])>=0&&(e[n].major=!0);return e}(t,n,r,i):n}class a2 extends sE{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new i0._date(t.adapters.date);s.init(e),tw(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:aK(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:a,minDefined:n,maxDefined:r}=this.getUserBounds();function o(t){n||isNaN(t.min)||(s=Math.min(s,t.min)),r||isNaN(t.max)||(a=Math.max(a,t.max))}n&&r||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=tc(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),a=tc(a)&&!isNaN(a)?a:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,a-1),this.max=Math.max(s+1,a)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let a=this.min,n=function(t,e,i){let s=0,a=t.length;for(;s<a&&t[s]<e;)s++;for(;a>s&&t[a-1]>i;)a--;return s>0||a<t.length?t.slice(s,a):t}(s,a,this.max);return this._unit=e.unit||(i.autoSkip?aQ(e.minUnit,this.min,this.max,this._getLabelCapacity(a)):function(t,e,i,s,a){for(let n=aJ.length-1;n>=aJ.indexOf(i);n--){let i=aJ[n];if(aX[i].common&&t._adapter.diff(a,s,i)>=e-1)return i}return aJ[i?aJ.indexOf(i):0]}(this,n.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=aJ.indexOf(t)+1,i=aJ.length;e<i;++e)if(aX[aJ[e]].common)return aJ[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&n.reverse(),a1(this,n,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,a=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),a=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let n=t.length<3?.5:.25;s=tG(s,0,n),a=tG(a,0,n),this._offsets={start:s,end:a,factor:1/(s+1+a)}}_generate(){let t,e;let i=this._adapter,s=this.min,a=this.max,n=this.options,r=n.time,o=r.unit||aQ(r.minUnit,s,a,this._getLabelCapacity(s)),l=tu(n.ticks.stepSize,1),h="week"===o&&r.isoWeekday,c=tB(h)||!0===h,d={},u=s;if(c&&(u=+i.startOf(u,"isoWeek",h)),u=+i.startOf(u,c?"day":o),i.diff(a,s,o)>1e5*l)throw Error(s+" and "+a+" are too far apart with stepSize of "+l+" "+o);let f="data"===n.ticks.source&&this.getDataTimestamps();for(t=u,e=0;t<a;t=+i.add(t,l,o),e++)a0(d,t,f);return(t===a||"ticks"===n.bounds||1===e)&&a0(d,t,f),Object.keys(d).sort(aG).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,a=e||i[s];return this._adapter.format(t,a)}_tickFormatFunction(t,e,i,s){let a=this.options,n=a.ticks.callback;if(n)return tg(n,[t,e,i],this);let r=a.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&r[o],c=l&&r[l],d=i[e],u=l&&c&&d&&d.major;return this._adapter.format(t,s||(u?c:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=tH(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(s),n=Math.sin(s),r=this._resolveTickFontOptions(0).size;return{w:i*a+r*n,h:i*n+r*a}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,a=this._tickFormatFunction(t,0,a1(this,[t],this._majorUnit),s),n=this._getLabelSize(a),r=Math.floor(this.isHorizontal()?this.width/n.w:this.height/n.h)-1;return r>0?r:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e;let i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(aK(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return t3(t.sort(aG))}}function a5(t,e,i){let s,a,n,r,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=t0(t,"pos",e)),{pos:s,time:n}=t[o],{pos:a,time:r}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=t0(t,"time",e)),{time:s,pos:n}=t[o],{time:a,pos:r}=t[l]);let h=a-s;return h?n+(r-n)*(e-s)/h:n}class a3 extends a2{static id="timeseries";static defaults=a2.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=a5(e,this.min),this._tableRange=a5(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s;let{min:a,max:n}=this,r=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=a&&s<=n&&r.push(s);if(r.length<2)return[{time:a,pos:0},{time:n,pos:1}];for(e=0,i=r.length;e<i;++e)Math.round((r[e+1]+r[e-1])/2)!==(s=r[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(a5(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return a5(this._table,i*this._tableRange+this._minPos,!0)}}let a4="label";function a6(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function a8(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a4,s=[];t.datasets=e.map(e=>{let a=t.datasets.find(t=>t[i]===e[i]);return!a||!e.data||s.includes(a)?{...e}:(s.push(a),Object.assign(a,e),a)})}let a7=(0,n.forwardRef)(function(t,e){let{height:i=150,width:s=300,redraw:a=!1,datasetIdKey:r,type:o,data:l,options:h,plugins:c=[],fallbackContent:d,updateMode:u,...f}=t,p=(0,n.useRef)(null),g=(0,n.useRef)(null),m=()=>{p.current&&(g.current=new s5(p.current,{type:o,data:function(t){var e;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a4,s={labels:[],datasets:[]};return e=t.labels,s.labels=e,a8(s,t.datasets,i),s}(l,r),options:h&&{...h},plugins:c}),a6(e,g.current))},x=()=>{a6(e,null),g.current&&(g.current.destroy(),g.current=null)};return(0,n.useEffect)(()=>{!a&&g.current&&h&&function(t,e){let i=t.options;i&&e&&Object.assign(i,e)}(g.current,h)},[a,h]),(0,n.useEffect)(()=>{if(!a&&g.current){var t,e;t=g.current.config.data,e=l.labels,t.labels=e}},[a,l.labels]),(0,n.useEffect)(()=>{!a&&g.current&&l.datasets&&a8(g.current.config.data,l.datasets,r)},[a,l.datasets]),(0,n.useEffect)(()=>{g.current&&(a?(x(),setTimeout(m)):g.current.update(u))},[a,h,l.labels,l.datasets,u]),(0,n.useEffect)(()=>{g.current&&(x(),setTimeout(m))},[o]),(0,n.useEffect)(()=>(m(),()=>x()),[]),n.createElement("canvas",{ref:p,role:"img",height:i,width:s,...f},d)}),a9=(s5.register(iX),(0,n.forwardRef)((t,e)=>n.createElement(a7,{...t,ref:e,type:"pie"}))),nt=(0,i(62881).Z)("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);function ne({assets:t,selectedCurrency:e,currencySymbol:i,convertCurrency:s,onCategorySelect:r}){let[l,h]=(0,n.useState)([]),[c,d]=(0,n.useState)(null),[u,f]=(0,n.useState)({labels:[],datasets:[{data:[],backgroundColor:[],borderColor:[],borderWidth:1}]}),p=["rgba(54, 162, 235, 0.6)","rgba(255, 99, 132, 0.6)","rgba(255, 206, 86, 0.6)","rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)","rgba(255, 159, 64, 0.6)","rgba(199, 199, 199, 0.6)","rgba(83, 102, 255, 0.6)","rgba(78, 205, 196, 0.6)","rgba(255, 99, 71, 0.6)"],g=t.reduce((t,i)=>t+s(i.value||0,i.currency||"USD",e),0);return(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-5",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"feature-card-icon bg-blue-100",children:a.jsx(nt,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"feature-card-title",children:"Asset Distribution"}),(0,a.jsxs)("p",{className:"feature-card-subtitle",children:["By category in ",e]})]})]}),c&&a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/badge'");throw t.code="MODULE_NOT_FOUND",t}()),{variant:"outline",className:"bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer",onClick:()=>{d(null),r&&r("all")},children:"Clear Filter"})]}),a.jsx("div",{className:"h-[250px] flex items-center justify-center",children:0===t.length?a.jsx("p",{className:"text-gray-500 text-center",children:"Add assets to see your distribution chart"}):a.jsx(a9,{data:u,options:{responsive:!0,onClick:(t,e)=>{if(e&&e.length>0){let t=e[0].index,i=u.labels?.[t];if(i&&(d(i),r)){let t=o.find(t=>t.label===i)?.value;t&&r(t)}}},plugins:{legend:{position:"right",labels:{boxWidth:15,padding:15,generateLabels:t=>{let e=t.data.datasets;return t.data.labels?.map((t,i)=>({text:t,fillStyle:e[0].backgroundColor?.[i],strokeStyle:e[0].borderColor?.[i],lineWidth:1,hidden:!1,index:i}))||[]}},onClick:(t,e,i)=>{let s=e.index,a=u.labels?.[s];if(a&&(d(a),r)){let t=o.find(t=>t.label===a)?.value;t&&r(t)}}},tooltip:{callbacks:{label:function(t){let e=t.label||"",s=t.raw,a=l[t.dataIndex]?.count||0;return[`${e}: ${i}${s.toLocaleString()}`,`Items: ${a}`]}}}}}})}),t.length>0&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[a.jsx("p",{className:"text-sm font-medium",children:"Total Value"}),(0,a.jsxs)("p",{className:"text-xl font-bold text-blue-700",children:[i,g.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-3",children:[l.slice(0,5).map((t,e)=>{let s=g>0?t.value/g*100:0;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:p[e%p.length]}}),a.jsx("span",{className:"text-sm font-medium",children:t.category})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("span",{className:"font-medium",children:[i,t.value.toLocaleString()]}),(0,a.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",t.count," items)"]})]})]}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/progress'");throw t.code="MODULE_NOT_FOUND",t}()),{value:s,className:"h-1.5"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[s.toFixed(1),"%"]}),a.jsx("span",{onClick:()=>{if(r){let e=o.find(e=>e.label===t.category)?.value;e&&r(e)}},className:"text-blue-600 cursor-pointer hover:underline",children:"View Assets"})]})]},e)}),l.length>5&&a.jsx("div",{className:"text-center mt-2",children:(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["+ ",l.length-5," more categories"]})})]})]})]})}(function(){var t=Error("Cannot find module '@/components/ui/badge'");throw t.code="MODULE_NOT_FOUND",t})(),function(){var t=Error("Cannot find module '@/components/ui/progress'");throw t.code="MODULE_NOT_FOUND",t}(),s5.register(s8,{id:"tooltip",_element:aP,positioners:aw,afterInit(t,e,i){i&&(t.tooltip=new aP({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:aE},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},{id:"legend",_element:ay,start(t,e,i){let s=t.legend=new ay({ctx:t.ctx,options:i,chart:t});sn.configure(t,s,i),sn.addBox(t,s)},stop(t){sn.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;sn.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,a=i.chart;a.isDatasetVisible(s)?(a.hide(s),e.hidden=!0):(a.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:a,color:n,useBorderRadius:r,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=eU(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:n,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:a||l.textAlign,borderRadius:r&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}});var ni=i(85999);function ns(){let{user:t}=(0,r.a)(),[e,i]=(0,n.useState)([]),[s,u]=(0,n.useState)("all"),[g,m]=(0,n.useState)(!0),[x,b]=(0,n.useState)(null),[_,y]=(0,n.useState)(!1),[v,w]=(0,n.useState)("USD"),[M,O]=(0,n.useState)({}),k=async s=>{try{if(!t){ni.Am.error("You must be logged in to add assets");return}let a={...s,name:s.name.charAt(0).toUpperCase()+s.name.slice(1)},n=await fetch("/api/assets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...a,acquisition_date:a.acquisition_date?a.acquisition_date.toISOString():null})});if(!n.ok){let t=await n.json();throw Error(t.error||"Failed to add asset")}let r=await n.json();ni.Am.success("Asset added successfully"),i([...e,r])}catch(t){console.error("Error adding asset:",t),ni.Am.error(`Failed to add asset: ${t.message}`)}},D=async t=>{try{if(!x)return;let s=await fetch("/api/assets",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:x.id,...t,acquisition_date:t.acquisition_date?t.acquisition_date.toISOString():null})});if(!s.ok){let t=await s.json();throw Error(t.error||"Failed to update asset")}let a=await s.json();ni.Am.success("Asset updated successfully"),i(e.map(t=>t.id===x.id?a:t)),y(!1),b(null)}catch(t){console.error("Error updating asset:",t),ni.Am.error(`Failed to update asset: ${t.message}`)}},j=async t=>{try{let s=await fetch(`/api/assets?id=${t}`,{method:"DELETE"});if(!s.ok){let t=await s.json();throw Error(t.error||"Failed to delete asset")}ni.Am.success("Asset deleted successfully"),i(e.filter(e=>e.id!==t))}catch(t){console.error("Error deleting asset:",t),ni.Am.error(`Failed to delete asset: ${t.message}`)}},N=t=>{let e=o.find(e=>e.value===t);return e?e.label:t},E=[{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"\xa3"},{code:"JPY",name:"Japanese Yen",symbol:"\xa5"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"Fr"},{code:"CNY",name:"Chinese Yuan",symbol:"\xa5"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"}],S=(t,e,i)=>t&&e!==i&&M[e]&&M[i]?("USD"===e?t:t/M[e])*M[i]:t,P=t=>{let e=E.find(e=>e.code===t);return e?e.symbol:t};return a.jsx(f,{children:(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[a.jsx(p.Z,{}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/PageHeading'");throw t.code="MODULE_NOT_FOUND",t}()),{title:"Asset Management",description:"Manage your physical and financial assets. This list will be shared with your trustee when needed.",icon:a.jsx(l.Z,{className:"h-6 w-6 text-blue-600"})}),a.jsx("div",{className:"mb-8",children:a.jsx(ne,{assets:e,selectedCurrency:v,currencySymbol:P(v),convertCurrency:S,onCategorySelect:u})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between gap-4 mb-6",children:[a.jsx("div",{className:"flex-grow",children:(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:s,onValueChange:u,children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"w-full md:w-[220px] border-blue-200 hover:border-blue-400 focus:ring-blue-200",children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{placeholder:"All Categories"})}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:"all",children:"All Categories"}),o.map(t=>a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:t.value,children:t.label},t.value))]})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Currency"}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:v,onValueChange:w,children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"w-[120px] h-8 border-blue-200 hover:border-blue-400 focus:ring-blue-200",children:a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{placeholder:"Select Currency"})}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{children:"All Currencies"}),E.map(t=>(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()),{value:t.code,children:[t.symbol," ",t.code]},t.code))]})})]})]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:E.slice(0,5).map(t=>(0,a.jsxs)(d.z,{size:"sm",variant:v===t.code?"default":"outline",className:`px-2 py-1 h-8 ${v===t.code?"bg-blue-600 hover:bg-blue-700":"border-blue-200 hover:bg-blue-50 hover:border-blue-400"}`,onClick:()=>w(t.code),children:[t.symbol," ",t.code]},t.code))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[a.jsx("div",{className:"lg:col-span-5 xl:col-span-4",children:(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/card'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"shadow-sm border-t-4 border-t-blue-500",children:[(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/card'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"bg-gradient-to-r from-blue-50 to-white",children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/card'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"text-blue-700",children:"Add New Asset"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Add a physical asset to your inventory"})]}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/card'");throw t.code="MODULE_NOT_FOUND",t}()),{children:a.jsx(C,{onSubmit:k,onCancel:()=>{}})})]})}),(0,a.jsxs)("div",{className:"lg:col-span-7 xl:col-span-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-4",children:[a.jsx("h2",{className:"text-lg font-semibold text-blue-700 mb-2",children:"Your Assets"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Manage and track all your valuable assets in one place"})]}),g?(0,a.jsxs)("div",{className:"text-center py-10 bg-white rounded-xl shadow-sm border border-gray-100",children:[a.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Loading assets..."})]}):0===e.length?(0,a.jsxs)("div",{className:"text-center py-16 bg-gradient-to-b from-blue-50 to-white rounded-lg border border-blue-100",children:[a.jsx(l.Z,{className:"mx-auto h-12 w-12 text-blue-400 mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"No assets found"}),a.jsx("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Add digital or physical assets to your inventory using the form on the left."})]}):a.jsx("div",{className:"space-y-3",children:e.map(t=>{let e=S(t.value||0,t.currency||"USD",v);return a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/card'");throw t.code="MODULE_NOT_FOUND",t}()),{className:`overflow-hidden hover:shadow-md transition-shadow border-l-4 ${e>1e4?"border-l-green-500":e>1e3?"border-l-blue-500":"border-l-gray-300"} hover:border-l-blue-600`,children:(0,a.jsxs)("div",{className:"flex items-center p-4",children:[t.image_url&&a.jsx("div",{className:"relative h-16 w-16 flex-shrink-0 bg-gray-100 overflow-hidden rounded mr-4",children:a.jsx("img",{src:t.image_url,alt:t.name,className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-base",children:t.name}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/badge'");throw t.code="MODULE_NOT_FOUND",t}()),{variant:"outline",className:"bg-blue-50 text-blue-700 hover:bg-blue-50 text-xs py-0 h-5 mt-1",children:N(t.category)})]}),(0,a.jsxs)("div",{className:`font-semibold ${e>1e4?"text-green-600":e>1e3?"text-blue-600":"text-gray-600"}`,children:[P(v),e.toLocaleString()]})]}),t.description&&a.jsx("p",{className:"text-xs text-gray-500 mt-2 line-clamp-2",children:t.description}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-600 mt-2 space-x-4",children:[t.location&&(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("span",{className:"font-medium",children:"Location:"}),a.jsx("span",{className:"ml-1",children:t.location})]}),t.acquisition_date&&(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("span",{className:"font-medium",children:"Acquired:"}),a.jsx("span",{className:"ml-1",children:new Date(t.acquisition_date).toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-1 mt-3",children:[a.jsx(d.z,{variant:"ghost",size:"sm",className:"h-7 w-7 p-0",onClick:()=>{b(t),y(!0)},children:a.jsx(h.Z,{className:"h-3.5 w-3.5 text-gray-500"})}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{asChild:!0,children:a.jsx(d.z,{variant:"ghost",size:"sm",className:"h-7 w-7 p-0 text-red-500 hover:text-red-700 hover:bg-red-50",children:a.jsx(c.Z,{className:"h-3.5 w-3.5"})})}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:"Delete Asset"}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:["Are you sure you want to delete ",t.name," from your assets? This action cannot be undone."]})]}),(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:"Cancel"}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{onClick:()=>j(t.id),className:"bg-red-500 hover:bg-red-600",children:"Delete"})]})]})]})]})]})]})},t.id)})})]})]}),a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{open:_,onOpenChange:y,children:(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"sm:max-w-[800px]",children:[(0,a.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[a.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/dialog'");throw t.code="MODULE_NOT_FOUND",t}()),{children:"Edit Asset"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Update your asset information"})]}),x&&a.jsx(C,{onSubmit:D,onCancel:()=>{y(!1),b(null)},defaultValues:{name:x.name,type:"digital"===x.type?"physical":x.type,category:x.category,description:x.description||"",location:x.location||"",value:x.value||null,currency:x.currency||"USD",acquisition_date:x.acquisition_date?new Date(x.acquisition_date):null,account_details:x.account_details||"",notes:x.notes||""}})]})})]})})}(function(){var t=Error("Cannot find module '@/components/ui/badge'");throw t.code="MODULE_NOT_FOUND",t})(),function(){var t=Error("Cannot find module '@/components/ui/card'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/components/ui/PageHeading'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/components/ui/dialog'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/components/ui/alert-dialog'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/components/ui/select'");throw t.code="MODULE_NOT_FOUND",t}()},88270:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var s=i(10326);i(17577);var a=i(90434),n=i(86333);let r=()=>s.jsx("div",{className:"mb-6",children:(0,s.jsxs)(a.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[s.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},34793:(t,e,i)=>{"use strict";i.d(e,{Z:()=>g});var s=i(10326),a=i(17577),n=i(90434),r=i(46226),o=i(24061),l=i(28916),h=i(88378),c=i(71810),d=i(2777),u=i(91664);(function(){var t=Error("Cannot find module '@/components/ui/avatar'");throw t.code="MODULE_NOT_FOUND",t})(),function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}();var f=i(68136),p=i(85999);let g=()=>{let{user:t,signOut:e}=(0,f.a)(),[i,g]=(0,a.useState)(null);(0,a.useEffect)(()=>{t&&m()},[t]);let m=async()=>{try{if(!t)return;let{data:e,error:i}=await d.supabase.from("trustees").select("id").eq("trustee_user_id",t.id).eq("status","active").limit(1);if(i)throw i;g(e&&e.length>0)}catch(t){console.error("Error checking trustee status:",t)}},x=async()=>{try{await e(),p.Am.success("Successfully signed out")}catch(t){console.error("Sign out error:",t),p.Am.error("Failed to sign out")}};return(0,s.jsxs)("header",{className:"h-20 border-b border-gray-200 bg-white flex items-center px-4 sm:px-6 relative",children:[s.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2",children:s.jsx(n.default,{href:"/dashboard",className:"flex items-center",children:s.jsx(r.default,{src:"/images/Legalock-logo.svg",alt:"Legalock Logo",width:160,height:50,priority:!0})})}),(0,s.jsxs)("div",{className:"ml-auto flex items-center space-x-4",children:[s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:(0,s.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{asChild:!0,children:s.jsx(u.z,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsxs)(n.default,{href:"/trustee/dashboard",className:"flex items-center gap-2",children:[s.jsx(o.Z,{className:"h-4 w-4"}),"Trustee Dashboard"]})})}),s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:s.jsx("p",{children:"Access your trustee responsibilities"})})]})}),s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:(0,s.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{asChild:!0,children:s.jsx(u.z,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsxs)(n.default,{href:"/subscription",className:"flex items-center gap-2",children:[s.jsx(l.Z,{className:"h-4 w-4"}),"Subscription"]})})}),s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:s.jsx("p",{children:"Manage your subscription plan"})})]})}),s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:(0,s.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{asChild:!0,children:s.jsx(u.z,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsxs)(n.default,{href:"/settings",className:"flex items-center gap-2",children:[s.jsx(h.Z,{className:"h-4 w-4"}),"Settings"]})})}),s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/tooltip'");throw t.code="MODULE_NOT_FOUND",t}()),{children:s.jsx("p",{children:"Manage your account settings"})})]})}),(0,s.jsxs)(u.z,{variant:"outline",size:"sm",onClick:x,className:"flex items-center gap-2",children:[s.jsx(c.Z,{className:"h-4 w-4"}),"Sign Out"]}),(0,s.jsxs)(Object(function(){var t=Error("Cannot find module '@/components/ui/avatar'");throw t.code="MODULE_NOT_FOUND",t}()),{children:[s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/avatar'");throw t.code="MODULE_NOT_FOUND",t}()),{src:""}),s.jsx(Object(function(){var t=Error("Cannot find module '@/components/ui/avatar'");throw t.code="MODULE_NOT_FOUND",t}()),{className:"bg-primary text-primary-foreground",children:(()=>{if(!t)return"U";if(t.firstName&&t.lastName)return`${t.firstName.charAt(0)}${t.lastName.charAt(0)}`.toUpperCase();if(t.email){let e=t.email.split("@");return 2===e.length?`${e[0].charAt(0)}${e[1].charAt(0)}`.toUpperCase():t.email.charAt(0).toUpperCase()}return"U"})()})]})]})]})}},91664:(t,e,i)=>{"use strict";i.d(e,{z:()=>h});var s=i(10326),a=i(17577),n=i(34214),r=i(79360),o=i(51223);let l=(0,r.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=a.forwardRef(({className:t,variant:e,size:i,asChild:a=!1,...r},h)=>{let c=a?n.g7:"button";return s.jsx(c,{className:(0,o.cn)(l({variant:e,size:i,className:t})),ref:h,...r})});h.displayName="Button"},9969:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected unicode escape\n    ,-[\x1b[36;1;4m/Users/<USER>/Desktop/Legalock/legalock/src/components/ui/form.tsx\x1b[0m:55:1]\n \x1b[2m55\x1b[0m |   return {\n \x1b[2m56\x1b[0m |     id,\n \x1b[2m57\x1b[0m |     name: fieldContext.name,\n \x1b[2m58\x1b[0m |     formItemId: \\`\\${id}-form-item\\`,\n    : \x1b[31;1m                ^\x1b[0m\n \x1b[2m59\x1b[0m |     formDescriptionId: \\`\\${id}-form-item-description\\`,\n \x1b[2m60\x1b[0m |     formMessageId: \\`\\${id}-form-item-message\\`,\n \x1b[2m61\x1b[0m |     ...fieldState,\n    `----\n\n\nCaused by:\n    Syntax Error")},41190:(t,e,i)=>{"use strict";i.d(e,{I:()=>r});var s=i(10326),a=i(17577),n=i(51223);let r=a.forwardRef(({className:t,type:e,...i},a)=>s.jsx("input",{type:e,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...i}));r.displayName="Input"},2777:(t,e,i)=>{"use strict";i.d(e,{supabase:()=>o});var s=i(56292);let a="https://ccwvtcudztphwwzzgwvg.supabase.co",n=null,r=null,o=(0,s.eI)(a,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}});!function(){if(r)return;let t=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM";r=(0,s.eI)(a,t,{auth:{autoRefreshToken:!1,persistSession:!1}})}()},51223:(t,e,i)=>{"use strict";i.d(e,{cn:()=>n});var s=i(41135),a=i(31009);function n(...t){return(0,a.m6)((0,s.W)(t))}},31164:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/assets/page.tsx#default`)}};var e=require("../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[9276,8987,9168,5981,6292,285,6686,7600,8002],()=>i(413));module.exports=s})();