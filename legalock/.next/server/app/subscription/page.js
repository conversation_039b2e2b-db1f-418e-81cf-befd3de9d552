(()=>{var e={};e.id=1930,e.ids=[1930],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},66403:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c}),t(9797),t(56752),t(35866);var n=t(23191),s=t(88716),i=t(37922),a=t.n(i),o=t(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let c=["",{children:["subscription",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9797)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/subscription/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/subscription/page.tsx"],u="/subscription/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/subscription/page",pathname:"/subscription",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12429:(e,r,t)=>{Promise.resolve().then(t.bind(t,21269))},86333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},32933:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},94019:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(62881).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},21269:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var n=t(10326);t(17577);var s=t(28916),i=t(32933),a=t(94019),o=t(91664);(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var d=t(88270),c=t(35797);!function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();var l=t(68136);function u(){let{user:e}=(0,l.a)();return n.jsx(c.Z,{children:(0,n.jsxs)("div",{className:"container mx-auto py-8",children:[n.jsx(d.Z,{}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Subscription",description:"Manage your subscription plan",icon:n.jsx(s.Z,{className:"h-6 w-6"})}),(0,n.jsxs)("div",{className:"mt-8",children:[n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[{id:"free",name:"Free",price:"$0",period:"forever",description:"Basic digital legacy management",features:[{name:"Up to 5 assets",included:!0},{name:"Up to 3 trustees",included:!0},{name:"Up to 10 documents",included:!0},{name:"Basic last wishes",included:!0},{name:"Email support",included:!1},{name:"Advanced security",included:!1},{name:"Unlimited storage",included:!1}],buttonText:"Current Plan",buttonDisabled:!0},{id:"premium",name:"Premium",price:"$9.99",period:"per month",description:"Complete digital legacy solution",features:[{name:"Unlimited assets",included:!0},{name:"Unlimited trustees",included:!0},{name:"Unlimited documents",included:!0},{name:"Advanced last wishes",included:!0},{name:"Priority email support",included:!0},{name:"Advanced security",included:!0},{name:"Unlimited storage",included:!0}],buttonText:"Upgrade",buttonDisabled:!1,highlight:!0},{id:"family",name:"Family",price:"$19.99",period:"per month",description:"For families up to 5 members",features:[{name:"Everything in Premium",included:!0},{name:"Up to 5 family members",included:!0},{name:"Family document sharing",included:!0},{name:"Family trustee management",included:!0},{name:"Priority phone support",included:!0},{name:"Advanced security",included:!0},{name:"Unlimited storage",included:!0}],buttonText:"Upgrade",buttonDisabled:!1}].map(e=>(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`flex flex-col ${e.highlight?"border-primary shadow-md":""}`,children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl",children:e.name}),(0,n.jsxs)("div",{className:"mt-2 flex items-baseline",children:[n.jsx("span",{className:"text-3xl font-bold",children:e.price}),(0,n.jsxs)("span",{className:"ml-1 text-sm text-gray-500",children:["/",e.period]})]})]}),"free"===e.id&&n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Current"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mt-4",children:e.description})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:n.jsx("ul",{className:"space-y-3",children:e.features.map((e,r)=>(0,n.jsxs)("li",{className:"flex items-start",children:[e.included?n.jsx(i.Z,{className:"h-5 w-5 text-green-500 mr-2 flex-shrink-0"}):n.jsx(a.Z,{className:"h-5 w-5 text-gray-300 mr-2 flex-shrink-0"}),n.jsx("span",{className:e.included?"text-gray-700":"text-gray-400",children:e.name})]},r))})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(o.z,{className:"w-full",variant:e.highlight?"default":"outline",disabled:e.buttonDisabled,children:e.buttonText})})]},e.id))}),n.jsx("div",{className:"mt-12",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Billing History"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"View your past invoices and payment history"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("div",{className:"text-center py-8 text-gray-500",children:n.jsx("p",{children:"No billing history available for free plan."})})})]})})]})]})})}},88270:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var n=t(10326);t(17577);var s=t(90434),i=t(86333);let a=()=>n.jsx("div",{className:"mb-6",children:(0,n.jsxs)(s.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[n.jsx(i.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},9797:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/subscription/page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[9276,8987,9168,5981,6292,285,8002,5797],()=>t(66403));module.exports=n})();