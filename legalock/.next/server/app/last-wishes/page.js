(()=>{var e={};e.id=1814,e.ids=[1814],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},88897:(e,r,o)=>{"use strict";o.r(r),o.d(r,{GlobalError:()=>s.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>d}),o(73892),o(56752),o(35866);var n=o(23191),t=o(88716),a=o(37922),s=o.n(a),i=o(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);o.d(r,c);let d=["",{children:["last-wishes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,73892)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/last-wishes/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,35866,23)),"next/dist/client/components/not-found-error"]}],l=["/Users/<USER>/Desktop/Legalock/legalock/src/app/last-wishes/page.tsx"],u="/last-wishes/page",h={require:o,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/last-wishes/page",pathname:"/last-wishes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},67947:(e,r,o)=>{Promise.resolve().then(o.bind(o,12755))},86333:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},67427:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},18019:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},53080:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},77506:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},76846:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("PawPrint",[["circle",{cx:"11",cy:"4",r:"2",key:"vol9p0"}],["circle",{cx:"18",cy:"8",r:"2",key:"17gozi"}],["circle",{cx:"20",cy:"16",r:"2",key:"1v9bxh"}],["path",{d:"M9 10a5 5 0 0 1 5 5v3.5a3.5 3.5 0 0 1-6.84 1.045Q6.52 17.48 4.46 16.84A3.5 3.5 0 0 1 5.5 10Z",key:"1ydw1z"}]])},31215:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},50949:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},24061:(e,r,o)=>{"use strict";o.d(r,{Z:()=>n});let n=(0,o(62881).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},90434:(e,r,o)=>{"use strict";o.d(r,{default:()=>t.a});var n=o(79404),t=o.n(n)},12755:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>v});var n=o(10326),t=o(17577),a=o(35047),s=o(68136),i=o(67427),c=o(53080),d=o(76846),l=o(24061),u=o(18019),h=o(50949),m=o(77506),p=o(31215),O=o(91664);(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}();var f=o(88270);!function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}();var x=o(44794),_=o(85999);function v(){let e=(0,a.useRouter)(),{user:r,loading:o}=(0,s.a)(),[v,N]=(0,t.useState)({funeral_wishes:"",burial_wishes:"",burial_option:"",personal_messages:"",show_personal_messages:!1,is_organ_donor:!1,organ_donor_country:"USA",organ_donor_state:"",has_pets:!1,pet_care_instructions:"",other_wishes:""}),[j,b]=(0,t.useState)(!0),[w,g]=(0,t.useState)(!1),[D,U]=(0,t.useState)(!1),y=e=>{let{name:r,value:o}=e.target;N(e=>({...e,[r]:o})),U(!0)},E=(e,r)=>{N(o=>({...o,[e]:r})),U(!0)},C=async()=>{try{if(!r)return;if(g(!0),v.id){let e=await fetch("/api/last-wishes",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)});if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to update last wishes")}let r=await e.json();N(r)}else{let e=await fetch("/api/last-wishes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)});if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to create last wishes")}let r=await e.json();N(r)}_.Am.success("Last wishes saved successfully"),U(!1)}catch(e){console.error("Error saving last wishes:",e),_.Am.error(e.message||"Failed to save last wishes")}finally{g(!1)}};return o||j?(0,n.jsxs)("div",{className:"container mx-auto py-8 text-center",children:[n.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"}),n.jsx("p",{className:"mt-4 text-gray-500",children:"Loading your last wishes..."})]}):(0,n.jsxs)("div",{className:"container mx-auto py-8",children:[n.jsx(f.Z,{}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Last Wishes",description:"Document your final wishes to share with your trustees",icon:n.jsx(i.Z,{className:"h-6 w-6"})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8",children:[(0,n.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Funeral & Memorial Preferences"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Document your preferences for funeral services and memorials"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{name:"funeral_wishes",placeholder:"Describe your preferences for funeral services, memorial gatherings, or celebrations of life...",className:"min-h-[150px]",value:v.funeral_wishes||"",onChange:y})})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Burial & Remains Preferences"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Document your preferences for burial, cremation, or other arrangements"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[n.jsx(x._,{htmlFor:"burial-option",children:"Preferred Option"}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:v.burial_option||"",onValueChange:e=>{N(r=>({...r,burial_option:e})),U(!0)},children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"burial-option",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select a burial option"})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[{value:"burial",label:"Traditional Burial"},{value:"cremation",label:"Cremation"},{value:"green_burial",label:"Green/Natural Burial"},{value:"donation",label:"Body Donation to Science"},{value:"other",label:"Other"}].map(e=>n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]})]}),(0,n.jsxs)("div",{children:[n.jsx(x._,{htmlFor:"burial-wishes",children:"Additional Details"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"burial-wishes",name:"burial_wishes",placeholder:"Provide additional details about your burial or remains preferences...",className:"min-h-[150px]",value:v.burial_wishes||"",onChange:y})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Personal Messages"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Leave personal messages for your loved ones"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"space-y-0.5",children:[n.jsx(x._,{htmlFor:"show-personal-messages",children:"Enable Personal Messages"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Toggle to write personal messages for your loved ones"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"show-personal-messages",checked:v.show_personal_messages,onCheckedChange:e=>E("show_personal_messages",e)})]}),v.show_personal_messages&&n.jsx("div",{className:"pt-2",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{name:"personal_messages",placeholder:"Write personal messages, thoughts, or reflections you'd like to share with your loved ones...",className:"min-h-[150px]",value:v.personal_messages||"",onChange:y})})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Other Wishes"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Document any other final wishes or instructions"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{name:"other_wishes",placeholder:"Document any other final wishes, instructions, or preferences not covered in the sections above...",className:"min-h-[150px]",value:v.other_wishes||"",onChange:y})})]})]}),(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[n.jsx(c.Z,{className:"h-5 w-5 text-green-600 mr-2"}),"Organ Donation"]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Indicate your organ donation preferences"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"space-y-0.5",children:[n.jsx(x._,{htmlFor:"organ-donor",children:"I am an organ donor"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Indicate if you are registered as an organ donor"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"organ-donor",checked:v.is_organ_donor,onCheckedChange:e=>E("is_organ_donor",e)})]}),v.is_organ_donor&&(0,n.jsxs)("div",{className:"space-y-4 pt-2",children:[(0,n.jsxs)("div",{children:[n.jsx(x._,{htmlFor:"donor-country",children:"Country"}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:v.organ_donor_country||"USA",onValueChange:e=>{let r="USA"!==e?"":v.organ_donor_state;N(o=>({...o,organ_donor_country:e,organ_donor_state:r})),U(!0)},children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"donor-country",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Select a country"})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[{value:"USA",label:"United States"},{value:"CAN",label:"Canada"},{value:"GBR",label:"United Kingdom"},{value:"AUS",label:"Australia"},{value:"NZL",label:"New Zealand"},{value:"DEU",label:"Germany"},{value:"FRA",label:"France"},{value:"ESP",label:"Spain"},{value:"ITA",label:"Italy"},{value:"JPN",label:"Japan"},{value:"OTHER",label:"Other"}].map(e=>n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.value,children:e.label},e.value))})]}),n.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Select the country where you are registered as an organ donor"})]}),"USA"===v.organ_donor_country&&(0,n.jsxs)("div",{children:[n.jsx(x._,{htmlFor:"donor-state",children:"State"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/USStatesSelect'");throw e.code="MODULE_NOT_FOUND",e}()),{value:v.organ_donor_state||"",onValueChange:e=>{N(r=>({...r,organ_donor_state:e})),U(!0)},placeholder:"Select the state where you're registered"}),n.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Select the state where you are registered as an organ donor"})]})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[n.jsx(d.Z,{className:"h-5 w-5 text-amber-600 mr-2"}),"Pet Care"]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Provide instructions for the care of your pets"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"space-y-0.5",children:[n.jsx(x._,{htmlFor:"has-pets",children:"I have pets"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Indicate if you have pets that will need care"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"has-pets",checked:v.has_pets,onCheckedChange:e=>E("has_pets",e)})]}),v.has_pets&&(0,n.jsxs)("div",{className:"space-y-2 pt-2",children:[n.jsx(x._,{htmlFor:"pet-care",children:"Pet Care Instructions"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"pet-care",name:"pet_care_instructions",placeholder:"Provide details about your pets and instructions for their care...",className:"min-h-[100px]",value:v.pet_care_instructions||"",onChange:y})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center",children:[n.jsx(l.Z,{className:"h-5 w-5 text-blue-600 mr-2"}),"Trustee Access"]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Information about who can access your last wishes"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx("div",{className:"bg-blue-50 p-4 rounded-md",children:(0,n.jsxs)("div",{className:"flex items-start",children:[n.jsx(u.Z,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0"}),n.jsx("div",{children:n.jsx("p",{className:"text-sm text-blue-700",children:"Your last wishes will be shared with your trustees after your passing. Make sure to keep this information up to date."})})]})}),n.jsx("div",{className:"mt-4",children:n.jsx(O.z,{variant:"outline",className:"w-full",onClick:()=>e.push("/trustees"),children:"Manage Trustees"})})]})]}),n.jsx("div",{className:"sticky top-4",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:D?"border-amber-300 shadow-md":"",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6",children:[D&&(0,n.jsxs)("div",{className:"bg-amber-50 p-3 rounded-md mb-4 flex items-start",children:[n.jsx(h.Z,{className:"h-5 w-5 text-amber-600 mr-2 mt-0.5 flex-shrink-0"}),n.jsx("p",{className:"text-sm text-amber-800",children:"You have unsaved changes. Please save your changes before leaving this page."})]}),n.jsx(O.z,{className:"w-full",onClick:C,disabled:w||!D,children:w?(0,n.jsxs)(n.Fragment,{children:[n.jsx(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,n.jsxs)(n.Fragment,{children:[n.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"Save Last Wishes"]})})]})})})]})]})]})}(function(){var e=Error("Cannot find module '@/components/ui/USStatesSelect'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()},88270:(e,r,o)=>{"use strict";o.d(r,{Z:()=>s});var n=o(10326);o(17577);var t=o(90434),a=o(86333);let s=()=>n.jsx("div",{className:"mb-6",children:(0,n.jsxs)(t.default,{href:"/dashboard",className:"flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit",children:[n.jsx(a.Z,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})})},91664:(e,r,o)=>{"use strict";o.d(r,{z:()=>d});var n=o(10326),t=o(17577),a=o(34214),s=o(79360),i=o(51223);let c=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef(({className:e,variant:r,size:o,asChild:t=!1,...s},d)=>{let l=t?a.g7:"button";return n.jsx(l,{className:(0,i.cn)(c({variant:r,size:o,className:e})),ref:d,...s})});d.displayName="Button"},44794:(e,r,o)=>{"use strict";o.d(r,{_:()=>d});var n=o(10326),t=o(17577),a=o(34478),s=o(79360),i=o(51223);let c=(0,s.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef(({className:e,...r},o)=>n.jsx(a.Root,{ref:o,className:(0,i.cn)(c(),e),...r}));d.displayName=a.Root.displayName},51223:(e,r,o)=>{"use strict";o.d(r,{cn:()=>a});var n=o(41135),t=o(31009);function a(...e){return(0,t.m6)((0,n.W)(e))}},73892:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>n});let n=(0,o(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/last-wishes/page.tsx#default`)},34478:(e,r,o)=>{"use strict";o.d(r,{Root:()=>i});var n=o(17577),t=o(45226),a=o(10326),s=n.forwardRef((e,r)=>(0,a.jsx)(t.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));s.displayName="Label";var i=s},45226:(e,r,o)=>{"use strict";o.d(r,{WV:()=>s});var n=o(17577);o(60962);var t=o(34214),a=o(10326),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let o=(0,t.Z8)(`Primitive.${r}`),s=n.forwardRef((e,n)=>{let{asChild:t,...s}=e,i=t?o:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...s,ref:n})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{})}};var r=require("../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),n=r.X(0,[9276,8987,9168,5981,8002],()=>o(88897));module.exports=n})();