(()=>{var e={};e.id=136,e.ids=[136],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17741:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),s(50380),s(56752),s(35866);var t=s(23191),o=s(88716),n=s(37922),a=s.n(n),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d=["",{children:["will-advisor",{children:["advisor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,50380)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/will-advisor/advisor/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/will-advisor/advisor/page.tsx"],u="/will-advisor/advisor/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/will-advisor/advisor/page",pathname:"/will-advisor/advisor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99529:(e,r,s)=>{Promise.resolve().then(s.bind(s,58177))},58177:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var t=s(10326);s(17577);var o=s(69545),n=s(73787),a=s(47207);(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}();var i=s(8600);function l(){let{currentQuestion:e,state:r,visibleQuestions:s}=(0,o.$)(),l=s.length>0?Math.round(r.currentStep/s.length*100):0;return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Will Advisor",description:"Answer a few questions to get personalized recommendations for your will.",icon:t.jsx(i.Z,{className:"h-6 w-6"})}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mb-2",children:[t.jsx("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[l,"%"]})]}),t.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}()),{value:l,className:"h-2"})]}),r.completed?t.jsx(a.Z,{}):e&&t.jsx(n.Z,{question:e})]})}function d(){return t.jsx(o.A,{children:t.jsx(l,{})})}},50380:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/will-advisor/advisor/page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9276,8987,9168,5981,4272,8002,8727],()=>s(17741));module.exports=t})();