(()=>{var e={};e.id=3784,e.ids=[3784],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55310:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(21695),t(56752),t(35866);var s=t(23191),o=t(88716),n=t(37922),a=t.n(n),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["will",{children:["advisor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21695)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/will/advisor/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/will/advisor/page.tsx"],u="/will/advisor/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/will/advisor/page",pathname:"/will/advisor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},22816:(e,r,t)=>{Promise.resolve().then(t.bind(t,90926))},90926:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(10326);t(17577);var o=t(69545),n=t(73787),a=t(47207);(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}();var i=t(8600);function l(){let{currentQuestion:e,state:r,visibleQuestions:t}=(0,o.$)(),l=t.length>0?Math.round(r.currentStep/t.length*100):0;return(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/PageHeading'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Will Advisor",description:"Answer a few questions to get personalized recommendations for your will.",icon:s.jsx(i.Z,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[l,"%"]})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}()),{value:l,className:"h-2"})]}),r.completed?s.jsx(a.Z,{}):e&&s.jsx(n.Z,{question:e})]})}function d(){return s.jsx(o.A,{children:s.jsx(l,{})})}},21695:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/will/advisor/page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,8987,9168,5981,4272,8002,8727],()=>t(55310));module.exports=s})();