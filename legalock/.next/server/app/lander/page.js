(()=>{var e={};e.id=4614,e.ids=[4614],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},90886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c}),r(54621),r(56752),r(35866);var i=r(23191),o=r(88716),s=r(37922),n=r.n(s),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["lander",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54621)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/lander/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],u=["/Users/<USER>/Desktop/Legalock/legalock/src/app/lander/page.tsx"],d="/lander/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/lander/page",pathname:"/lander",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31439:(e,t,r)=>{Promise.resolve().then(r.bind(r,26446))},59813:(e,t,r)=>{"use strict";var i,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>z,createClientComponentClient:()=>u,createMiddlewareClient:()=>b,createMiddlewareSupabaseClient:()=>j,createPagesBrowserClient:()=>d,createPagesServerClient:()=>f,createRouteHandlerClient:()=>S,createServerActionClient:()=>O,createServerComponentClient:()=>y,createServerSupabaseClient:()=>N}),e.exports=((e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))a.call(e,r)||void 0===r||o(e,r,{get:()=>t[r],enumerable:!(i=s(t,r))||i.enumerable});return e})(o({},"__esModule",{value:!0}),l);var c=r(89663);function u({supabaseUrl:e="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:r,cookieOptions:o,isSingleton:s=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let n=()=>{var i;return(0,c.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(i=null==r?void 0:r.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new c.BrowserCookieAuthStorageAdapter(o)}})};if(s){let e=i??n();return"undefined"==typeof window?e:(i||(i=e),i)}return n()}var d=u,p=r(89663),h=r(12715),g=class extends p.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,i;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,p.parseCookies)(t)[e]).find(e=>!!e)??(null==(i=this.context.req)?void 0:i.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var i;let o=(0,h.splitCookiesString)((null==(i=this.context.res.getHeader("set-cookie"))?void 0:i.toString())??"").filter(t=>!(e in(0,p.parseCookies)(t))),s=(0,p.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...o,s])}};function f(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,p.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new g(e,o)}})}var m=r(89663),v=r(12715),C=class extends m.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return(0,v.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,m.parseCookies)(t)[e]).find(e=>!!e)||(0,m.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let i=(0,m.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",i)}};function b(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,m.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new C(e,o)}})}var k=r(89663),I=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function y(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new I(e,o)}})}var w=r(89663),x=class extends w.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function S(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,w.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.9.0"}},auth:{storage:new x(e,o)}})}var O=S;function z({supabaseUrl:e="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:r,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),d({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:i})}function N(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:i,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),f(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:o})}function j(e,{supabaseUrl:t="https://ccwvtcudztphwwzzgwvg.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwNzQ3MjYsImV4cCI6MjA1OTY1MDcyNn0.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps",options:i,cookieOptions:o}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),b(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:o})}},77506:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});let i=(0,r(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},12715:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function i(e,i){var o,s,n,a,l=e.split(";").filter(r),c=(o=l.shift(),s="",n="",(a=o.split("=")).length>1?(s=a.shift(),n=a.join("=")):n=o,{name:s,value:n}),u=c.name,d=c.value;i=i?Object.assign({},t,i):t;try{d=i.decodeValues?decodeURIComponent(d):d}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+d+"'. Set options.decodeValues to false to disable this feature.",e)}var p={name:u,value:d};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),i=t.join("=");"expires"===r?p.expires=new Date(i):"max-age"===r?p.maxAge=parseInt(i,10):"secure"===r?p.secure=!0:"httponly"===r?p.httpOnly=!0:"samesite"===r?p.sameSite=i:"partitioned"===r?p.partitioned=!0:p[r]=i}),p}function o(e,o){if(o=o?Object.assign({},t,o):t,!e)return o.map?{}:[];if(e.headers){if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var s=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];s||!e.headers.cookie||o.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=s}}return(Array.isArray(e)||(e=[e]),o.map)?e.filter(r).reduce(function(e,t){var r=i(t,o);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return i(e,o)})}e.exports=o,e.exports.parse=o,e.exports.parseString=i,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,i,o,s,n=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;l();)if(","===(r=e.charAt(a))){for(i=a,a+=1,l(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=o,n.push(e.substring(t,i)),t=a):a=i+1}else a+=1;(!s||a>=e.length)&&n.push(e.substring(t,e.length))}return n}},26446:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var i=r(10326),o=r(17577),s=r(77506),n=r(91664),a=r(59813);function l(){let[e,t]=(0,o.useState)(!0),[r,l]=(0,o.useState)(!1);return(0,a.createClientComponentClient)(),i.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-50",children:i.jsx("div",{className:"text-center p-8 max-w-md",children:e?(0,i.jsxs)(i.Fragment,{children:[i.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Redirecting to Dashboard"}),i.jsx("p",{className:"mb-6",children:"You've been authenticated successfully. Redirecting you to your local dashboard..."}),i.jsx("div",{className:"flex justify-center",children:i.jsx(s.Z,{className:"h-8 w-8 animate-spin text-primary"})})]}):(0,i.jsxs)(i.Fragment,{children:[i.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Welcome to Legalock"}),i.jsx("p",{className:"mb-6",children:r?"You're authenticated and ready to use Legalock. Go to your dashboard to manage your digital legacy.":"Legalock helps you manage your digital legacy and ensure your wishes are documented and secure."}),r&&i.jsx(n.z,{onClick:()=>{localStorage.getItem("localOrigin"),window.location.href="/dashboard"},className:"w-full",children:"Go to Dashboard"}),!r&&i.jsx(n.z,{onClick:()=>window.location.href="/login",className:"w-full",children:"Sign In"})]})})})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var i=r(10326),o=r(17577),s=r(34214),n=r(79360),a=r(51223);let l=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...n},c)=>{let u=o?s.g7:"button";return i.jsx(u,{className:(0,a.cn)(l({variant:t,size:r,className:e})),ref:c,...n})});c.displayName="Button"},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var i=r(41135),o=r(31009);function s(...e){return(0,o.m6)((0,i.W)(e))}},54621:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/lander/page.tsx#default`)},89663:(e,t,r)=>{"use strict";let i,o;r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>O,CookieAuthStorageAdapter:()=>S,DEFAULT_COOKIE_OPTIONS:()=>w,createSupabaseClient:()=>z,isBrowser:()=>y,parseCookies:()=>N,parseSupabaseCookie:()=>k,serializeCookie:()=>j,stringifySupabaseSession:()=>I});var s=r(78893);new TextEncoder;let n=new TextDecoder;s.Buffer.isEncoding("base64url");let a=e=>s.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=n.decode(t)),t}(e),"base64");var l=r(56292),c=Object.create,u=Object.defineProperty,d=Object.getOwnPropertyDescriptor,p=Object.getOwnPropertyNames,h=Object.getPrototypeOf,g=Object.prototype.hasOwnProperty,f=(e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of p(t))g.call(e,o)||o===r||u(e,o,{get:()=>t[o],enumerable:!(i=d(t,o))||i.enumerable});return e},m=(e,t,r)=>(r=null!=e?c(h(e)):{},f(!t&&e&&e.__esModule?r:u(r,"default",{value:e,enumerable:!0}),e)),v=(i={"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},o=(t||{}).decode||i,s=0;s<e.length;){var n=e.indexOf("=",s);if(-1===n)break;var a=e.indexOf(";",s);if(-1===a)a=e.length;else if(a<n){s=e.lastIndexOf(";",n-1)+1;continue}var l=e.slice(s,n).trim();if(void 0===r[l]){var c=e.slice(n+1,a).trim();34===c.charCodeAt(0)&&(c=c.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(c,o)}s=a+1}return r},e.serialize=function(e,i,s){var n=s||{},a=n.encode||o;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(i);if(l&&!r.test(l))throw TypeError("argument val is invalid");var c=e+"="+l;if(null!=n.maxAge){var u=n.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(u)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");c+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");c+="; Path="+n.path}if(n.expires){var d=n.expires;if("[object Date]"!==t.call(d)&&!(d instanceof Date)||isNaN(d.valueOf()))throw TypeError("option expires is invalid");c+="; Expires="+d.toUTCString()}if(n.httpOnly&&(c+="; HttpOnly"),n.secure&&(c+="; Secure"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function i(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function o(e){return encodeURIComponent(e)}}},function(){return o||(0,i[p(i)[0]])((o={exports:{}}).exports,o),o.exports}),C=m(v()),b=m(v());function k(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,i,o]=t[0].split("."),s=a(i),n=new TextDecoder,{exp:l,sub:c,...u}=JSON.parse(n.decode(s));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:c,factors:t[4],...u}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function I(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function y(){return"undefined"!=typeof window&&void 0!==window.document}var w={path:"/",sameSite:"lax",maxAge:31536e6},x=RegExp(".{1,3180}","g"),S=class{constructor(e){this.cookieOptions={...w,...e,maxAge:w.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(k(t));let r=function(e,t=()=>null){let r=[];for(let i=0;;i++){let o=t(`${e}.${i}`);if(!o)break;r.push(o)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(k(r)):null}setItem(e,t){if(e.endsWith("-code-verifier")){this.setCookie(e,t);return}(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let i=[],o=t.match(x);return null==o||o.forEach((t,r)=>{let o=`${e}.${r}`;i.push({name:o,value:t})}),i})(e,I(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},O=class extends S{constructor(e){super(e)}getCookie(e){return y()?(0,C.parse)(document.cookie)[e]:null}setCookie(e,t){if(!y())return null;document.cookie=(0,C.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!y())return null;document.cookie=(0,C.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function z(e,t,r){var i;let o=y();return(0,l.eI)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:o,detectSessionInUrl:o,persistSession:!0,storage:r.auth.storage,...(null==(i=r.auth)?void 0:i.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var N=b.parse,j=b.serialize}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,8987,5981,6292,8002],()=>r(90886));module.exports=i})();