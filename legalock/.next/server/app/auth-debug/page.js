(()=>{var e={};e.id=2521,e.ids=[2521],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},417:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(67926),r(56752),r(35866);var t=r(23191),i=r(88716),a=r(37922),n=r.n(a),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["auth-debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,67926)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/auth-debug/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56752)),"/Users/<USER>/Desktop/Legalock/legalock/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Desktop/Legalock/legalock/src/app/auth-debug/page.tsx"],u="/auth-debug/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/auth-debug/page",pathname:"/auth-debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5969:(e,s,r)=>{Promise.resolve().then(r.bind(r,27767))},27767:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(10326),i=r(17577),a=r(2777),n=r(68136),l=r(91664),o=r(35797);function d(){let{user:e}=(0,n.a)(),[s,r]=(0,i.useState)(null),[d,c]=(0,i.useState)(null),[u,h]=(0,i.useState)(!0),[x,p]=(0,i.useState)(null),g=async()=>{try{h(!0),p(null);let{data:e,error:s}=await a.supabase.auth.refreshSession();if(s)throw Error(`Failed to refresh session: ${s.message}`);r(e.session),c(e.user),alert("Session refreshed successfully")}catch(e){console.error("Session refresh error:",e),p(e.message||"Failed to refresh session")}finally{h(!1)}};return t.jsx(o.Z,{children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[t.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Authentication Debug"}),u?t.jsx("p",{children:"Loading authentication data..."}):x?t.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mb-6",children:t.jsx("p",{className:"text-red-600",children:x})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[t.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Custom Auth Context"}),e?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"User ID:"})," ",e.id]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Email:"})," ",e.email]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Email Verified:"})," ",e.emailVerified?"Yes":"No"]})]}):t.jsx("p",{className:"text-amber-600",children:"Not authenticated with custom auth"})]}),(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[t.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Supabase Auth"}),d?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"User ID:"})," ",d.id]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Email:"})," ",d.email]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Created At:"})," ",new Date(d.created_at).toLocaleString()]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Last Sign In:"})," ",d.last_sign_in_at?new Date(d.last_sign_in_at).toLocaleString():"N/A"]})]}):t.jsx("p",{className:"text-amber-600",children:"Not authenticated with Supabase"})]})]}),s&&(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[t.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Supabase Session Details"}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Access Token:"})," ",s.access_token?`${s.access_token.substring(0,20)}...`:"None"]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Refresh Token:"})," ",s.refresh_token?`${s.refresh_token.substring(0,10)}...`:"None"]}),(0,t.jsxs)("p",{children:[t.jsx("strong",{children:"Expires At:"})," ",s.expires_at?new Date(1e3*s.expires_at).toLocaleString():"N/A"]})]}),t.jsx("div",{className:"flex gap-4",children:t.jsx(l.z,{onClick:g,disabled:u,children:"Refresh Supabase Session"})})]})]})})}},67926:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Desktop/Legalock/legalock/src/app/auth-debug/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[9276,8987,9168,5981,6292,285,8002,5797],()=>r(417));module.exports=t})();