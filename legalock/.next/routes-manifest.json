{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/documents/[id]", "regex": "^/api/documents/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/documents/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/documents/[id]/download", "regex": "^/api/documents/([^/]+?)/download(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/documents/(?<nxtPid>[^/]+?)/download(?:/)?$"}, {"page": "/api/time-capsules/[id]", "regex": "^/api/time\\-capsules/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/time\\-capsules/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/time-capsule/[id]", "regex": "^/time\\-capsule/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/time\\-capsule/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/trustee/[userId]/last-wishes", "regex": "^/trustee/([^/]+?)/last\\-wishes(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/trustee/(?<nxtPuserId>[^/]+?)/last\\-wishes(?:/)?$"}, {"page": "/trustee/[userId]/services", "regex": "^/trustee/([^/]+?)/services(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/trustee/(?<nxtPuserId>[^/]+?)/services(?:/)?$"}, {"page": "/trustee/[userId]/vault", "regex": "^/trustee/([^/]+?)/vault(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/trustee/(?<nxtPuserId>[^/]+?)/vault(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/About", "regex": "^/About(?:/)?$", "routeKeys": {}, "namedRegex": "^/About(?:/)?$"}, {"page": "/Assets", "regex": "^/Assets(?:/)?$", "routeKeys": {}, "namedRegex": "^/Assets(?:/)?$"}, {"page": "/AuthCallback", "regex": "^/AuthCallback(?:/)?$", "routeKeys": {}, "namedRegex": "^/AuthCallback(?:/)?$"}, {"page": "/Blog", "regex": "^/Blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/Blog(?:/)?$"}, {"page": "/Careers", "regex": "^/Careers(?:/)?$", "routeKeys": {}, "namedRegex": "^/Careers(?:/)?$"}, {"page": "/Contact", "regex": "^/Contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/Contact(?:/)?$"}, {"page": "/DocumentUpload", "regex": "^/DocumentUpload(?:/)?$", "routeKeys": {}, "namedRegex": "^/DocumentUpload(?:/)?$"}, {"page": "/Help", "regex": "^/Help(?:/)?$", "routeKeys": {}, "namedRegex": "^/Help(?:/)?$"}, {"page": "/Index", "regex": "^/Index(?:/)?$", "routeKeys": {}, "namedRegex": "^/Index(?:/)?$"}, {"page": "/Landing", "regex": "^/Landing(?:/)?$", "routeKeys": {}, "namedRegex": "^/Landing(?:/)?$"}, {"page": "/Login", "regex": "^/Login(?:/)?$", "routeKeys": {}, "namedRegex": "^/Login(?:/)?$"}, {"page": "/NotFound", "regex": "^/NotFound(?:/)?$", "routeKeys": {}, "namedRegex": "^/NotFound(?:/)?$"}, {"page": "/Privacy", "regex": "^/Privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/Privacy(?:/)?$"}, {"page": "/Register", "regex": "^/Register(?:/)?$", "routeKeys": {}, "namedRegex": "^/Register(?:/)?$"}, {"page": "/Security", "regex": "^/Security(?:/)?$", "routeKeys": {}, "namedRegex": "^/Security(?:/)?$"}, {"page": "/ServiceSunset", "regex": "^/ServiceSunset(?:/)?$", "routeKeys": {}, "namedRegex": "^/ServiceSunset(?:/)?$"}, {"page": "/Settings", "regex": "^/Settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/Settings(?:/)?$"}, {"page": "/Subscription", "regex": "^/Subscription(?:/)?$", "routeKeys": {}, "namedRegex": "^/Subscription(?:/)?$"}, {"page": "/Terms", "regex": "^/Terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/Terms(?:/)?$"}, {"page": "/TimeCapsule", "regex": "^/TimeCapsule(?:/)?$", "routeKeys": {}, "namedRegex": "^/TimeCapsule(?:/)?$"}, {"page": "/TimeCapsulePublic", "regex": "^/TimeCapsulePublic(?:/)?$", "routeKeys": {}, "namedRegex": "^/TimeCapsulePublic(?:/)?$"}, {"page": "/TrusteeInvite", "regex": "^/TrusteeInvite(?:/)?$", "routeKeys": {}, "namedRegex": "^/TrusteeInvite(?:/)?$"}, {"page": "/Trustees", "regex": "^/Trustees(?:/)?$", "routeKeys": {}, "namedRegex": "^/Trustees(?:/)?$"}, {"page": "/Vault", "regex": "^/Vault(?:/)?$", "routeKeys": {}, "namedRegex": "^/Vault(?:/)?$"}, {"page": "/Verify", "regex": "^/Verify(?:/)?$", "routeKeys": {}, "namedRegex": "^/Verify(?:/)?$"}, {"page": "/Will", "regex": "^/Will(?:/)?$", "routeKeys": {}, "namedRegex": "^/Will(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/assets", "regex": "^/assets(?:/)?$", "routeKeys": {}, "namedRegex": "^/assets(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth-debug", "regex": "^/auth\\-debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth\\-debug(?:/)?$"}, {"page": "/auth-fix", "regex": "^/auth\\-fix(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth\\-fix(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/contacts", "regex": "^/contacts(?:/)?$", "routeKeys": {}, "namedRegex": "^/contacts(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/debug", "regex": "^/debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug(?:/)?$"}, {"page": "/features/digital-assets", "regex": "^/features/digital\\-assets(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/digital\\-assets(?:/)?$"}, {"page": "/features/digital-vault", "regex": "^/features/digital\\-vault(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/digital\\-vault(?:/)?$"}, {"page": "/features/emergency-contacts", "regex": "^/features/emergency\\-contacts(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/emergency\\-contacts(?:/)?$"}, {"page": "/features/last-wishes", "regex": "^/features/last\\-wishes(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/last\\-wishes(?:/)?$"}, {"page": "/features/service-sunset", "regex": "^/features/service\\-sunset(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/service\\-sunset(?:/)?$"}, {"page": "/features/trustee-management", "regex": "^/features/trustee\\-management(?:/)?$", "routeKeys": {}, "namedRegex": "^/features/trustee\\-management(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/lander", "regex": "^/lander(?:/)?$", "routeKeys": {}, "namedRegex": "^/lander(?:/)?$"}, {"page": "/last-wishes", "regex": "^/last\\-wishes(?:/)?$", "routeKeys": {}, "namedRegex": "^/last\\-wishes(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/security", "regex": "^/security(?:/)?$", "routeKeys": {}, "namedRegex": "^/security(?:/)?$"}, {"page": "/service-sunset", "regex": "^/service\\-sunset(?:/)?$", "routeKeys": {}, "namedRegex": "^/service\\-sunset(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/settings/profile", "regex": "^/settings/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/profile(?:/)?$"}, {"page": "/settings/security", "regex": "^/settings/security(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/security(?:/)?$"}, {"page": "/subscription", "regex": "^/subscription(?:/)?$", "routeKeys": {}, "namedRegex": "^/subscription(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/time-capsule", "regex": "^/time\\-capsule(?:/)?$", "routeKeys": {}, "namedRegex": "^/time\\-capsule(?:/)?$"}, {"page": "/trustee/accept", "regex": "^/trustee/accept(?:/)?$", "routeKeys": {}, "namedRegex": "^/trustee/accept(?:/)?$"}, {"page": "/trustee/dashboard", "regex": "^/trustee/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/trustee/dashboard(?:/)?$"}, {"page": "/trustees", "regex": "^/trustees(?:/)?$", "routeKeys": {}, "namedRegex": "^/trustees(?:/)?$"}, {"page": "/vault", "regex": "^/vault(?:/)?$", "routeKeys": {}, "namedRegex": "^/vault(?:/)?$"}, {"page": "/verify", "regex": "^/verify(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify(?:/)?$"}, {"page": "/verify-password", "regex": "^/verify\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-password(?:/)?$"}, {"page": "/will", "regex": "^/will(?:/)?$", "routeKeys": {}, "namedRegex": "^/will(?:/)?$"}, {"page": "/will/advisor", "regex": "^/will/advisor(?:/)?$", "routeKeys": {}, "namedRegex": "^/will/advisor(?:/)?$"}, {"page": "/will-advisor", "regex": "^/will\\-advisor(?:/)?$", "routeKeys": {}, "namedRegex": "^/will\\-advisor(?:/)?$"}, {"page": "/will-advisor/advisor", "regex": "^/will\\-advisor/advisor(?:/)?$", "routeKeys": {}, "namedRegex": "^/will\\-advisor/advisor(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}