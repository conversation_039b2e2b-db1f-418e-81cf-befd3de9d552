require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Log the environment variables
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('SUPABASE_SERVICE_ROLE_KEY (first 10 chars):', process.env.SUPABASE_SERVICE_ROLE_KEY ? process.env.SUPABASE_SERVICE_ROLE_KEY.substring(0, 10) + '...' : 'undefined');

// Create a Supabase client with the service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Test the connection
async function testConnection() {
  try {
    console.log('Testing Supabase connection...');

    // Try to fetch a single row from the custom_users table
    const { data, error } = await supabaseAdmin
      .from('custom_users')
      .select('id')
      .limit(1);

    if (error) {
      console.error('Error connecting to Supabase:', error);
    } else {
      console.log('Successfully connected to Supabase!');
      console.log('Data:', data);
    }
  } catch (error) {
    console.error('Exception connecting to Supabase:', error);
  }
}

testConnection();
