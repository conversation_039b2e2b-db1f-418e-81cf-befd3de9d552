require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function checkVaultDocuments() {
  try {
    console.log('Checking vault_documents table...');
    
    // Query the vault_documents table
    const { data, error } = await supabaseAdmin
      .from('vault_documents')
      .select('*');
    
    if (error) {
      console.error('Error querying vault_documents:', error);
      return;
    }
    
    console.log('Found', data.length, 'documents:');
    console.log(JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error in checkVaultDocuments:', error);
  }
}

checkVaultDocuments();
