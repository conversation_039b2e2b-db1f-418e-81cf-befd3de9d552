#!/bin/bash

# Setup script for Supabase Auth Email Hook
echo "Setting up Supabase Auth Email Hook..."

# Step 1: Generate a webhook secret
# We'll use OpenSSL to generate a random string
WEBHOOK_SECRET=$(openssl rand -base64 32)
echo "Generated webhook secret"

# Step 2: Set the environment variables
echo "Setting environment variables..."
supabase secrets set SEND_EMAIL_HOOK_SECRET="$WEBHOOK_SECRET"
echo "Set SEND_EMAIL_HOOK_SECRET"

# Step 3: Update the Supabase configuration
echo "Updating Supabase configuration..."

# Create a temporary file with the auth hook configuration
cat > auth_hook_config.toml << EOL
[auth.hook.send_email]
enabled = true
uri = "https://ccwvtcudztphwwzzgwvg.supabase.co/functions/v1/send-email-hook"
EOL

# Push the configuration to Supabase
supabase config push --config-file auth_hook_config.toml

# Clean up
rm auth_hook_config.toml

echo "Auth hook setup complete!"
echo "Webhook secret: $WEBHOOK_SECRET"
echo ""
echo "To test the hook, try signing up with a new email address in your application."
