#!/bin/bash

# Deployment script for Vercel with environment variable checks

echo "Starting Vercel deployment process..."

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Check if required environment variables are set
echo "Checking environment variables..."

# List of required environment variables
required_vars=(
    "NEXT_PUBLIC_SUPABASE_URL"
    "NEXT_PUBLIC_SUPABASE_ANON_KEY"
    "SUPABASE_SERVICE_ROLE_KEY"
    "AUTH_SECRET"
    "RESEND_API_KEY"
    "NEXT_PUBLIC_SITE_URL"
)

# Check each variable
missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    else
        echo "✅ $var is set"
    fi
done

# If any variables are missing, prompt to set them
if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "❌ The following environment variables are missing:"
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    
    echo "Would you like to set them now? (y/n)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        for var in "${missing_vars[@]}"; do
            echo "Enter value for $var:"
            read -r value
            vercel env add $var production "$value"
        done
    else
        echo "Deployment aborted. Please set the missing environment variables."
        exit 1
    fi
fi

# Ensure we have the latest dependencies
echo "Installing dependencies..."
npm install

# Build the project
echo "Building the project..."
npm run build

# Deploy to Vercel
echo "Deploying to Vercel..."
vercel --prod

echo "Deployment complete!"
