require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function checkUsers() {
  try {
    console.log('Checking users in the custom_users table...');

    // Get all users
    const { data: users, error: usersError } = await supabase
      .from('custom_users')
      .select('*');

    if (usersError) {
      console.error('Error getting users:', usersError);
    } else {
      console.log(`Found ${users.length} users in the custom_users table.`);

      // Print the first user (if any)
      if (users.length > 0) {
        const firstUser = users[0];
        console.log('First user:');
        console.log('  ID:', firstUser.id);
        console.log('  Email:', firstUser.email);
        console.log('  First Name:', firstUser.first_name);
        console.log('  Last Name:', firstUser.last_name);
        console.log('  Email Verified:', firstUser.email_verified);
      }
    }

  } catch (error) {
    console.error('Error checking users:', error);
  }
}

checkUsers();
