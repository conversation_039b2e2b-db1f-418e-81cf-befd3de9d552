import { Session, User as SupabaseUser } from '@supabase/supabase-js';

export interface User extends SupabaseUser {
  firstName?: string;
  lastName?: string;
  emailVerified?: boolean;
  isAnonymous?: boolean;
}

export type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, firstName: string, lastName: string, autoVerify?: boolean, trusteeInvitationId?: string) => Promise<void>;
  signOut: () => Promise<void>;
  verifyOtp: (email: string, token: string) => Promise<void>;
  updateProfile: (firstName: string, lastName: string) => Promise<void>;
  updateEmail: (email: string) => Promise<void>;
  updatePassword: (password: string) => Promise<void>;
  resendVerificationCode: (email: string) => Promise<void>;
};

export type AuthState = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isPremium: boolean;
  isEnterprise: boolean;
  isTrial: boolean;
  isExpired: boolean;
  isCancelled: boolean;
  isSuspended: boolean;
  isLocked: boolean;
  isVerified: boolean;
  isPending: boolean;
  isInvited: boolean;
  isAnonymous: boolean;
  isMfaEnabled: boolean;
  isMfaVerified: boolean;
  isMfaPending: boolean;
  isMfaRequired: boolean;
  isMfaEnrolled: boolean;
  isMfaDisabled: boolean;
  isMfaExpired: boolean;
  isMfaLocked: boolean;
  isMfaCancelled: boolean;
  isMfaSuspended: boolean;
  isMfaVerifiedRequired: boolean;
  isMfaVerifiedEnrolled: boolean;
  isMfaVerifiedDisabled: boolean;
  isMfaVerifiedExpired: boolean;
  isMfaVerifiedLocked: boolean;
  isMfaVerifiedCancelled: boolean;
  isMfaVerifiedSuspended: boolean;
  isMfaVerifiedPending: boolean;
  isMfaVerifiedRequiredPending: boolean;
  isMfaVerifiedEnrolledPending: boolean;
  isMfaVerifiedDisabledPending: boolean;
  isMfaVerifiedExpiredPending: boolean;
  isMfaVerifiedLockedPending: boolean;
  isMfaVerifiedCancelledPending: boolean;
  isMfaVerifiedSuspendedPending: boolean;
};
