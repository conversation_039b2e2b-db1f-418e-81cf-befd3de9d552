"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { willAdvisorQuestions, willAdvisorRecommendations } from '@/data/will-advisor-data';
import { 
  WillAdvisorContextType,
  Answer,
  Question,
  Recommendation 
} from './will-advisor-types';
import { initialState } from './will-advisor-constants';

const WillAdvisorContext = createContext<WillAdvisorContextType | undefined>(undefined);

export const WillAdvisorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState(initialState);
  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);

  const updateVisibleQuestions = useCallback(() => {
    const visible = willAdvisorQuestions.filter(question => {
      if (!question.conditionalDisplay) return true;
      
      const { dependsOn, showIfValue } = question.conditionalDisplay;
      const dependentAnswer = state.answers.find(a => a.questionId === dependsOn);
      if (!dependentAnswer) return false;

      if (Array.isArray(showIfValue)) {
        return showIfValue.includes(String(dependentAnswer.value));
      }
      return String(dependentAnswer.value) === showIfValue;
    });
    setVisibleQuestions(visible);
  }, [state.answers]);

  useEffect(() => {
    updateVisibleQuestions();
  }, [state.answers, updateVisibleQuestions]);

  const currentQuestion = state.currentStep < visibleQuestions.length
    ? visibleQuestions[state.currentStep]
    : null;

  const setAnswer = (answer: Answer) => {
    setState(prevState => {
      const existingAnswerIndex = prevState.answers.findIndex(
        a => a.questionId === answer.questionId
      );

      let newAnswers;
      if (existingAnswerIndex >= 0) {
        newAnswers = [...prevState.answers];
        newAnswers[existingAnswerIndex] = answer;
      } else {
        newAnswers = [...prevState.answers, answer];
      }

      return {
        ...prevState,
        answers: newAnswers,
      };
    });
  };

  const nextQuestion = () => {
    if (state.currentStep < visibleQuestions.length - 1) {
      setState(prevState => ({
        ...prevState,
        currentStep: prevState.currentStep + 1,
      }));
    } else {
      completeAdvisor();
    }
  };

  const previousQuestion = () => {
    if (state.currentStep > 0) {
      setState(prevState => ({
        ...prevState,
        currentStep: prevState.currentStep - 1,
      }));
    }
  };

  const resetAdvisor = () => {
    setState(initialState);
  };

  const completeAdvisor = () => {
    const recommendations = getRecommendations();
    setState(prevState => ({
      ...prevState,
      recommendations,
      completed: true,
    }));
  };

  const getRecommendations = (): Recommendation[] => {
    return willAdvisorRecommendations.filter(recommendation => {
      if (!recommendation.conditionalDisplay) return true;

      const { logic, conditions } = recommendation.conditionalDisplay;
      const conditionResults = conditions.map(condition => {
        const { questionId, operator, value } = condition;
        const answer = state.answers.find(a => a.questionId === questionId);

        if (!answer) return false;

        switch (operator) {
          case 'equals':
            return answer.value === value;
          case 'notEquals':
            return answer.value !== value;
          case 'contains':
            if (Array.isArray(answer.value)) {
              if (Array.isArray(value)) {
                return value.some(v => (answer.value as string[]).includes(String(v)));
              } else {
                return (answer.value as string[]).includes(String(value));
              }
            }
            return false;
          case 'greaterThan':
            return typeof answer.value === 'number' &&
                   typeof value === 'number' &&
                   answer.value > value;
          case 'lessThan':
            return typeof answer.value === 'number' &&
                   typeof value === 'number' &&
                   answer.value < value;
          default:
            return false;
        }
      });

      return logic === 'AND' 
        ? conditionResults.every(result => result)
        : conditionResults.some(result => result);
    });
  };

  const getAnswerByQuestionId = (questionId: string): Answer | undefined => {
    return state.answers.find(answer => answer.questionId === questionId);
  };

  return (
    <WillAdvisorContext.Provider
      value={{
        state,
        questions: willAdvisorQuestions,
        currentQuestion,
        visibleQuestions,
        setAnswer,
        nextQuestion,
        previousQuestion,
        resetAdvisor,
        completeAdvisor,
        getRecommendations,
        getAnswerByQuestionId,
      }}
    >
      {children}
    </WillAdvisorContext.Provider>
  );
};

export const useWillAdvisor = () => {
  const context = useContext(WillAdvisorContext);
  if (context === undefined) {
    throw new Error('useWillAdvisor must be used within a WillAdvisorProvider');
  }
  return context;
};
