export type SubscriptionPlan = 'free' | 'premium';

export interface SubscriptionDetails {
  id: string;
  status: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

export interface SubscriptionContextType {
  plan: SubscriptionPlan;
  isLoading: boolean;
  isSubscribed: boolean;
  subscriptionDetails: SubscriptionDetails | null;
  checkSubscription: () => Promise<void>;
  createCheckout: (plan: 'premium') => Promise<string | null>;
}

// Constants
export const SUBSCRIPTION_PLANS = {
  free: {
    name: 'Free',
    price: 0,
    features: ['Basic features']
  },
  premium: {
    name: 'Premium',
    price: 9.99,
    features: ['All features', 'Priority support']
  }
};
