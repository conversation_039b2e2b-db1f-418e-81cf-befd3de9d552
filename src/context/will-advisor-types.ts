import { Answer, Question, Recommendation, WillAdvisorState } from '@/types/will-advisor.types';

export interface WillAdvisorContextType {
  state: WillAdvisorState;
  questions: Question[];
  currentQuestion: Question | null;
  visibleQuestions: Question[];
  setAnswer: (answer: Answer) => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  resetAdvisor: () => void;
  completeAdvisor: () => void;
  getRecommendations: () => Recommendation[];
  getAnswerByQuestionId: (questionId: string) => Answer | undefined;
}

export type { Answer, Question, Recommendation, WillAdvisorState };
