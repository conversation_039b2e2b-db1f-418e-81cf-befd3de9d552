"use client";

import { useContext } from 'react';
import { SubscriptionContext } from './subscription-context';
import type { SubscriptionContextType } from './subscription-types';

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};
