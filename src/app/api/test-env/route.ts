import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return environment variables (sanitized) for debugging
    return NextResponse.json({
      success: true,
      message: 'Environment test API is working',
      timestamp: new Date().toISOString(),
      env: {
        nodeEnv: process.env.NODE_ENV,
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
        supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
        supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
        authSecret: process.env.AUTH_SECRET ? 'Set' : 'Not set',
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL,
        resendApiKey: process.env.RESEND_API_KEY ? 'Set' : 'Not set',
        authSessionExpiryDays: process.env.AUTH_SESSION_EXPIRY_DAYS,
      },
      headers: {
        host: request.headers.get('host'),
        userAgent: request.headers.get('user-agent'),
      }
    });
  } catch (error: any) {
    console.error('Error in test-env API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
