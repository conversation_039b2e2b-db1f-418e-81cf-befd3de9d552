import { NextRequest, NextResponse } from 'next/server';
import {
  generateVerificationCode,
  getUserByEmail,
  sendVerificationEmail,
  storeVerificationCode,
  supabaseAdmin
} from '@/lib/auth-utils-edge';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await getUserByEmail(email);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check for rate limiting (max 3 attempts per hour)
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    const { count, error: countError } = await supabaseAdmin
      .from('verification_codes')
      .select('*', { count: 'exact', head: true })
      .eq('email', email)
      .gte('created_at', oneHourAgo.toISOString());

    if (countError) {
      console.error('Error checking rate limit:', countError);
      return NextResponse.json(
        { error: countError.message },
        { status: 500 }
      );
    }

    if (count && count >= 3) {
      return NextResponse.json(
        { error: 'Too many attempts. Please wait a few minutes before trying again.' },
        { status: 429 }
      );
    }

    // Generate and store verification code
    const verificationCode = generateVerificationCode();
    await storeVerificationCode(email, verificationCode);

    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationCode);
    if (!emailSent) {
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Verification code sent successfully',
    });
  } catch (error: any) {
    console.error('Error in resend verification:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while resending verification code' },
      { status: 500 }
    );
  }
}
