import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils-edge';

interface CustomError extends Error {
  code?: string;
}

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json({
        authenticated: false,
        user: null,
      });
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      // Session not found or expired
      cookieStore.delete('session_token');
      return NextResponse.json({
        authenticated: false,
        user: null,
      });
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      // User not found
      cookieStore.delete('session_token');
      return NextResponse.json({
        authenticated: false,
        user: null,
      });
    }

    // Return user data (excluding sensitive information)
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        emailVerified: user.email_verified,
      },
    });
  } catch (error: unknown) {
    console.error('Error in session:', error);
    
    const errorDetails = error instanceof Error ? {
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...(error as CustomError).code ? { code: (error as CustomError).code } : {}
    } : {};

    console.error('Error details:', errorDetails);

    // Check for specific error types
    if (error instanceof Error && error.message.includes('Database configuration error')) {
      return NextResponse.json(
        {
          authenticated: false,
          user: null,
          error: 'Database configuration error. Please check environment variables.'
        },
        { status: 500 }
      );
    } else if (error instanceof Error && error.message.includes('Database connection error')) {
      return NextResponse.json(
        {
          authenticated: false,
          user: null,
          error: 'Database connection error. Please try again later.'
        },
        { status: 503 }
      );
    }

    // For other errors, return a generic error message
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while getting session';
    return NextResponse.json(
      {
        authenticated: false,
        user: null,
        error: errorMessage
      },
      { status: 500 }
    );
  }
}
