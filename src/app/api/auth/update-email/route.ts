import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  generateVerificationCode,
  getSessionByToken,
  getUserByEmail,
  sendVerificationEmail,
  storeVerificationCode,
  updateUserEmail
} from '@/lib/auth-utils-edge';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      // Session not found or expired
      cookieStore.delete('session_token');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the request body
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if email is already in use by another user
    const existingUser = await getUserByEmail(email);
    if (existingUser && existingUser.id !== session.user_id) {
      return NextResponse.json(
        { error: 'Email is already in use by another account' },
        { status: 409 }
      );
    }

    // Update the user's email
    await updateUserEmail(session.user_id, email);

    // Generate and store verification code
    const verificationCode = generateVerificationCode();
    await storeVerificationCode(email, verificationCode);

    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationCode);
    if (!emailSent) {
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Email updated successfully. Please check your email for verification code.',
    });
  } catch (error: any) {
    console.error('Error in update-email:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while updating email' },
      { status: 500 }
    );
  }
}
