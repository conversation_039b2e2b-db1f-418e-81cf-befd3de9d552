import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { getVerificationEmailTemplate } from '@/emails/verification-email';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: Request) {
  try {
    const { email, code } = await request.json();

    if (!email || !code) {
      return NextResponse.json(
        { error: 'Email and code are required' },
        { status: 400 }
      );
    }

    const { data, error } = await resend.emails.send({
      from: 'Legalock <<EMAIL>>',
      to: [email],
      subject: 'Verify your Legalock account',
      html: getVerificationEmailTemplate(email, code),
    });

    if (error) {
      console.error('Error sending verification email:', error);
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Exception sending verification email:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
