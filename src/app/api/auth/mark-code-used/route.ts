import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/auth-utils-edge';

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { codeId } = await request.json();

    // Validate input
    if (!codeId) {
      return NextResponse.json(
        { error: 'Code ID is required' },
        { status: 400 }
      );
    }

    // Mark the code as used
    const { error } = await supabaseAdmin
      .from('verification_codes')
      .update({ used: true })
      .eq('id', codeId);

    if (error) {
      console.error('Error marking code as used:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Code marked as used successfully',
    });
  } catch (error: any) {
    console.error('Error in mark-code-used:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while marking code as used' },
      { status: 500 }
    );
  }
}
