import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/integrations/supabase/client';

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase client is initialized correctly
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    // Test Supabase connection
    const { data, error } = await supabase.from('custom_users').select('count(*)', { count: 'exact', head: true });
    
    return NextResponse.json({
      success: true,
      message: 'Supabase client test',
      env: {
        supabaseUrl: supabaseUrl ? 'Set' : 'Not set',
        supabaseAnonKey: supabaseAnonKey ? 'Set' : 'Not set',
        supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
      },
      supabaseTest: {
        error: error ? error.message : null,
        data: data || null,
      }
    });
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message || 'An error occurred',
    }, { status: 500 });
  }
}
