import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { Resend } from 'resend';
import { getVerificationEmailTemplate } from '@/emails/verification-email';

// Initialize Resend with API key
const resendApiKey = process.env.RESEND_API_KEY || '';
const resend = new Resend(resendApiKey);

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

const supabaseAdmin = createClient(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Function to generate a 6-digit OTP code
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      console.error('Resend verification: Email is required');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    console.log('Resending verification code for email:', email);

    // Check for rate limiting (max 3 attempts per hour)
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    const { count, error: countError } = await supabaseAdmin
      .from('verification_codes')
      .select('*', { count: 'exact', head: true })
      .eq('email', email)
      .gte('created_at', oneHourAgo.toISOString());

    if (countError) {
      console.error('Error checking rate limit:', countError);
      return NextResponse.json(
        { error: countError.message },
        { status: 500 }
      );
    }

    if (count && count >= 3) {
      console.error('Rate limit exceeded for email:', email);
      return NextResponse.json(
        { error: 'Too many attempts. Please wait a few minutes before trying again.' },
        { status: 429 }
      );
    }

    // Generate a 6-digit verification code
    const otp = generateOTP();
    console.log(`Generated OTP for ${email}: ${otp}`);

    // Calculate expiration time (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Store the verification code in our database using the admin client
    const { data: codeData, error: codeError } = await supabaseAdmin
      .from('verification_codes')
      .insert({
        email,
        code: otp,
        expires_at: expiresAt.toISOString(),
      })
      .select();

    if (codeError) {
      console.error('Error storing verification code:', codeError);
      return NextResponse.json(
        { error: codeError.message },
        { status: 500 }
      );
    }

    // Send our custom email with the OTP code
    try {
      // Send a custom email with Resend using our template
      const { data: emailData, error: emailError } = await resend.emails.send({
        from: 'Legalock <<EMAIL>>',
        to: [email],
        subject: 'Verify your Legalock account',
        html: getVerificationEmailTemplate(email, otp),
      });

      if (emailError) {
        console.error('Error sending custom email with Resend:', emailError);
        return NextResponse.json(
          { error: emailError.message },
          { status: 500 }
        );
      } else {
        console.log('Custom verification email sent successfully');
      }
    } catch (resendError) {
      console.error('Exception sending custom email with Resend:', resendError);
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    console.log('Verification code resent successfully');
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Unexpected error in resend-verification:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
