import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Return a simple response to test if API routes are working
    return NextResponse.json({
      success: true,
      message: 'API route is working',
      timestamp: new Date().toISOString(),
      env: {
        nodeEnv: process.env.NODE_ENV,
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
        supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
        supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
        authSecret: process.env.AUTH_SECRET ? 'Set' : 'Not set',
        siteUrl: process.env.NEXT_PUBLIC_SITE_URL,
      }
    });
  } catch (error: any) {
    console.error('Error in test-auth API route:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'An error occurred',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    }, { status: 500 });
  }
}
