import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id: documentId } = await params;

    // Get the session token from cookies (handles both sync and async cases)
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Get the document metadata
    const { data: document, error: fetchError } = await supabaseAdmin
      .from('vault_documents')
      .select('*')
      .eq('id', documentId)
      .single();

    if (fetchError) {
      console.error('Error fetching document:', fetchError);
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Check if the document belongs to the user
    if (document.user_id !== user.id) {
      return NextResponse.json(
        { error: 'You do not have permission to access this document' },
        { status: 403 }
      );
    }

    // Download the file using direct HTTP request
    const downloadUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/documents/${document.file_path}`;
    const response = await fetch(downloadUrl, {
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.error('Error downloading file:', response.statusText);
      return NextResponse.json(
        { error: `Failed to download file: ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.blob();

    if (!data) {
      return NextResponse.json(
        { error: 'No data returned from storage' },
        { status: 500 }
      );
    }

    // Return the file as a binary response instead of base64
    const headers = new Headers();
    headers.set('Content-Type', document.file_type || 'application/octet-stream');
    headers.set('Content-Disposition', `attachment; filename="${document.name}"`);
    headers.set('X-Document-Name', document.name);
    headers.set('X-Document-Path', document.file_path);
    headers.set('X-Document-Type', document.file_type || 'application/octet-stream');
    headers.set('X-Encryption-Key', document.encryption_key);

    return new NextResponse(data, {
      status: 200,
      headers
    });
  } catch (error: any) {
    console.error('Error in document download API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
