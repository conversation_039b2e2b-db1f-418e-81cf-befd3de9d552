import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { capsule_id, files } = body;

    if (!capsule_id || !files || !Array.isArray(files) || files.length === 0) {
      return NextResponse.json(
        { error: 'Capsule ID and files are required' },
        { status: 400 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Use the user's ID directly
    const userId = user.id;

    // Log for debugging
    console.log('Using user ID for media POST:', userId);

    // Verify that the capsule belongs to the user
    const { data: capsule, error: capsuleError } = await supabaseAdmin
      .from('time_capsules')
      .select('id')
      .eq('id', capsule_id)
      .eq('user_id', userId)
      .single();

    if (capsuleError || !capsule) {
      console.error('Error verifying capsule ownership:', capsuleError);
      return NextResponse.json(
        { error: 'Capsule not found or you do not have permission to add media to it' },
        { status: 404 }
      );
    }

    // Create media entries
    const mediaEntries = files.map(file => ({
      capsule_id,
      file_path: file.path,
      file_name: file.name,
      file_type: file.type,
      file_size: file.size
    }));

    const { data, error } = await supabaseAdmin
      .from('time_capsule_media')
      .insert(mediaEntries)
      .select();

    if (error) {
      console.error('Error saving media metadata:', error);
      return NextResponse.json(
        { error: 'Failed to save media metadata' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in POST /api/time-capsules/media:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the capsule ID from the query parameters
    const { searchParams } = new URL(request.url);
    const capsuleId = searchParams.get('capsule_id');

    if (!capsuleId) {
      return NextResponse.json(
        { error: 'Capsule ID is required' },
        { status: 400 }
      );
    }

    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Use the user's ID directly
    const userId = user.id;

    // Log for debugging
    console.log('Using user ID for media GET:', userId);

    // Verify that the capsule belongs to the user
    const { data: capsule, error: capsuleError } = await supabaseAdmin
      .from('time_capsules')
      .select('id')
      .eq('id', capsuleId)
      .eq('user_id', userId)
      .single();

    if (capsuleError) {
      console.error('Error verifying capsule ownership:', capsuleError);
      return NextResponse.json(
        { error: 'Capsule not found or you do not have permission to view its media' },
        { status: 404 }
      );
    }

    // Get the media for the capsule
    const { data, error } = await supabaseAdmin
      .from('time_capsule_media')
      .select('*')
      .eq('capsule_id', capsuleId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching media:', error);
      return NextResponse.json(
        { error: 'Failed to fetch media' },
        { status: 500 }
      );
    }

    // Generate signed URLs for each media item
    const mediaWithUrls = await Promise.all(data.map(async (media) => {
      const { data: urlData } = await supabaseAdmin.storage
        .from('time_capsule_media')
        .createSignedUrl(`${userId}/${media.file_path}`, 60 * 60); // 1 hour expiry

      return {
        ...media,
        signed_url: urlData?.signedUrl || null
      };
    }));

    return NextResponse.json(mediaWithUrls);
  } catch (error: any) {
    console.error('Error in GET /api/time-capsules/media:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
