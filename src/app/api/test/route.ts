import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'API is working',
    env: {
      nodeEnv: process.env.NODE_ENV,
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
      supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
      supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
      authSecret: process.env.AUTH_SECRET ? 'Set' : 'Not set',
      siteUrl: process.env.NEXT_PUBLIC_SITE_URL,
      resendApiKey: process.env.RESEND_API_KEY ? 'Set' : 'Not set',
    }
  });
}
