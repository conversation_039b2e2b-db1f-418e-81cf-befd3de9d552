import { NextRequest, NextResponse } from 'next/server';
import stripe from '@/lib/stripe';
import { supabaseAdmin } from '@/lib/auth-utils';

// This is your Stripe webhook secret for testing your endpoint locally.
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  const payload = await request.text();
  const sig = request.headers.get('stripe-signature') as string;

  let event;

  try {
    event = stripe.webhooks.constructEvent(payload, sig, endpointSecret!);
  } catch (err: any) {
    console.error(`Webhook Error: ${err.message}`);
    return NextResponse.json(
      { error: `Webhook Error: ${err.message}` },
      { status: 400 }
    );
  }

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed':
      const checkoutSession = event.data.object;

      // Get the user ID from the metadata
      const userId = checkoutSession.metadata.userId;

      // Get the customer ID
      const customerId = checkoutSession.customer;

      try {
        // Try to update the user's profile with the Stripe customer ID
        // If the profiles table doesn't exist, this will fail gracefully
        const { error: updateError } = await supabaseAdmin
          .from('profiles')
          .update({
            stripe_customer_id: customerId,
            subscription_tier: 'premium',
            subscription_status: 'active',
            subscription_start_date: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Error updating user profile:', updateError);
        }
      } catch (error) {
        console.error('Error in checkout.session.completed handler:', error);
      }

      break;

    case 'customer.subscription.updated':
      const subscription = event.data.object;
      const customerId2 = subscription.customer;

      try {
        // Get the user with this customer ID
        const { data: profiles, error: profileError } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .eq('stripe_customer_id', customerId2);

        if (profileError || !profiles || !profiles.length) {
          console.error('Error finding user profile:', profileError);
          break;
        }

        const userId2 = profiles[0].id;

        // Update the subscription status
        const { error: updateError2 } = await supabaseAdmin
          .from('profiles')
          .update({
            subscription_status: subscription.status,
            subscription_end_date: subscription.cancel_at
              ? new Date(subscription.cancel_at * 1000).toISOString()
              : null,
          })
          .eq('id', userId2);

        if (updateError2) {
          console.error('Error updating subscription status:', updateError2);
        }
      } catch (error) {
        console.error('Error in customer.subscription.updated handler:', error);
      }

      break;

    case 'customer.subscription.deleted':
      const canceledSubscription = event.data.object;
      const customerId3 = canceledSubscription.customer;

      try {
        // Get the user with this customer ID
        const { data: canceledProfiles, error: canceledProfileError } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .eq('stripe_customer_id', customerId3);

        if (canceledProfileError || !canceledProfiles || !canceledProfiles.length) {
          console.error('Error finding user profile:', canceledProfileError);
          break;
        }

        const userId3 = canceledProfiles[0].id;

        // Update the subscription status
        const { error: updateError3 } = await supabaseAdmin
          .from('profiles')
          .update({
            subscription_tier: 'free',
            subscription_status: 'inactive',
            subscription_end_date: new Date().toISOString(),
          })
          .eq('id', userId3);

        if (updateError3) {
          console.error('Error updating subscription status:', updateError3);
        }
      } catch (error) {
        console.error('Error in customer.subscription.deleted handler:', error);
      }

      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  return NextResponse.json({ received: true });
}
