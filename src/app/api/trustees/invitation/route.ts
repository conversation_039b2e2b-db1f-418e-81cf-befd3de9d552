import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { supabaseAdmin } from '@/lib/supabase-server';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    const inviteId = request.nextUrl.searchParams.get('id');
    let currentUserId = null;

    // Check if the user is already authenticated via session
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (sessionToken) {
      const session = await getSessionByToken(sessionToken);
      if (session) {
        const user = await getUserById(session.user_id);
        if (user) {
          currentUserId = user.id;
        }
      }
    }

    if (!inviteId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      );
    }

    console.log('Processing invitation request for ID:', inviteId);

    // Get the invitation details
    const { data: invitation, error: inviteError } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('id', inviteId)
      .single();

    if (inviteError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Get the inviter's details
    const { data: inviter, error: inviterError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', invitation.user_id)
      .single();

    if (inviterError || !inviter) {
      return NextResponse.json(
        { error: 'Could not retrieve inviter details' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      invitation,
      inviter,
      currentUserId
    });
  } catch (error: any) {
    console.error('Error fetching trustee invitation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch invitation' },
      { status: 500 }
    );
  }
}
