import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { supabaseAdmin } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if the user is a trustee for anyone
    // Include both active trustees and pending ones that match the user's email
    const { data: activeTrustees, error: activeError } = await supabaseAdmin
      .from('trustees')
      .select('id, user_id, status')
      .eq('trustee_user_id', user.id)
      .eq('status', 'active');

    if (activeError) {
      console.error('Error checking active trustee status:', activeError);
      return NextResponse.json(
        { error: 'Failed to check trustee status' },
        { status: 500 }
      );
    }

    // Also check for pending invitations by email
    const { data: pendingTrustees, error: pendingError } = await supabaseAdmin
      .from('trustees')
      .select('id, user_id, status, trustee_email')
      .eq('trustee_email', user.email)
      .eq('status', 'pending_auth')
      .is('trustee_user_id', null);

    // Combine the results
    const trusteeData = [...(activeTrustees || []), ...(pendingTrustees || [])];
    const trusteeError = activeError || pendingError;

    if (trusteeError) {
      console.error('Error checking trustee status:', trusteeError);
      return NextResponse.json(
        { error: 'Failed to check trustee status' },
        { status: 500 }
      );
    }

    // Return the trustee status
    return NextResponse.json({
      isTrustee: trusteeData && trusteeData.length > 0,
      trusteeCount: trusteeData ? trusteeData.length : 0,
      relationships: trusteeData || [],
      hasPendingInvitations: pendingTrustees && pendingTrustees.length > 0,
      pendingCount: pendingTrustees ? pendingTrustees.length : 0
    });
  } catch (error: any) {
    console.error('Error in check trustee status API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
