import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get last wishes for the user
    const supabaseAdmin = getSupabaseAdminClient();
    const { data, error } = await supabaseAdmin
      .from('last_wishes')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) {
      // If no record found, return an empty object (not an error)
      if (error.code === 'PGRST116') {
        return NextResponse.json({});
      }

      console.error('Error fetching last wishes:', error);
      return NextResponse.json(
        { error: 'Failed to fetch last wishes' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in last wishes API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Capitalize the first letter of each text field
    const capitalizedBody = {
      ...body,
      funeral_wishes: body.funeral_wishes ? body.funeral_wishes.charAt(0).toUpperCase() + body.funeral_wishes.slice(1) : body.funeral_wishes,
      burial_wishes: body.burial_wishes ? body.burial_wishes.charAt(0).toUpperCase() + body.burial_wishes.slice(1) : body.burial_wishes,
      personal_messages: body.personal_messages ? body.personal_messages.charAt(0).toUpperCase() + body.personal_messages.slice(1) : body.personal_messages,
      pet_care_instructions: body.pet_care_instructions ? body.pet_care_instructions.charAt(0).toUpperCase() + body.pet_care_instructions.slice(1) : body.pet_care_instructions,
      other_wishes: body.other_wishes ? body.other_wishes.charAt(0).toUpperCase() + body.other_wishes.slice(1) : body.other_wishes,
    };

    // Add the user_id to the last wishes data
    const lastWishesData = {
      ...capitalizedBody,
      user_id: user.id
    };

    try {
      // Get the admin client
      const supabaseAdmin = getSupabaseAdminClient();

      // First, try to create a profile for this user to avoid foreign key issues
      // This is a workaround for the foreign key constraint issue
      const { error: profileError } = await supabaseAdmin
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email,
          subscription_tier: 'free',
          subscription_status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, { onConflict: 'id' });

      if (profileError) {
        console.error('Error creating profile:', profileError);
        // Continue anyway, as we'll try to insert the last wishes
      }

      // Now try to insert the last wishes
      const { data, error: insertError } = await supabaseAdmin
        .from('last_wishes')
        .insert(lastWishesData)
        .select();

      if (insertError) {
        console.error('Error creating last wishes:', insertError);
        return NextResponse.json(
          { error: `Failed to create last wishes: ${insertError.message}` },
          { status: 500 }
        );
      }

      if (!data || data.length === 0) {
        return NextResponse.json(
          { error: 'No data returned after last wishes creation' },
          { status: 500 }
        );
      }

      return NextResponse.json(data[0]);
    } catch (insertError: any) {
      console.error('Exception creating last wishes:', insertError);
      return NextResponse.json(
        { error: `Exception creating last wishes: ${insertError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error in last wishes API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { id, ...data } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Last wishes ID is required' },
        { status: 400 }
      );
    }

    // Capitalize the first letter of each text field
    const lastWishesData = {
      ...data,
      funeral_wishes: data.funeral_wishes ? data.funeral_wishes.charAt(0).toUpperCase() + data.funeral_wishes.slice(1) : data.funeral_wishes,
      burial_wishes: data.burial_wishes ? data.burial_wishes.charAt(0).toUpperCase() + data.burial_wishes.slice(1) : data.burial_wishes,
      personal_messages: data.personal_messages ? data.personal_messages.charAt(0).toUpperCase() + data.personal_messages.slice(1) : data.personal_messages,
      pet_care_instructions: data.pet_care_instructions ? data.pet_care_instructions.charAt(0).toUpperCase() + data.pet_care_instructions.slice(1) : data.pet_care_instructions,
      other_wishes: data.other_wishes ? data.other_wishes.charAt(0).toUpperCase() + data.other_wishes.slice(1) : data.other_wishes,
    };

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // First, verify that the last wishes record belongs to the user
    const { data: existingRecord, error: fetchError } = await supabaseAdmin
      .from('last_wishes')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching last wishes:', fetchError);
      return NextResponse.json(
        { error: 'Last wishes record not found or you do not have permission to update it' },
        { status: 404 }
      );
    }

    // Update the last wishes
    const { data: updatedData, error: updateError } = await supabaseAdmin
      .from('last_wishes')
      .update({
        ...lastWishesData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating last wishes:', updateError);
      return NextResponse.json(
        { error: `Failed to update last wishes: ${updateError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedData);
  } catch (error: any) {
    console.error('Error in last wishes API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
