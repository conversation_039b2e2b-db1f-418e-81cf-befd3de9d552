import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Check environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    return NextResponse.json({
      success: true,
      env: {
        supabaseUrl: supabaseUrl ? `${supabaseUrl.substring(0, 10)}...` : 'Not set',
        supabaseAnonKey: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 10)}...` : 'Not set',
        supabaseServiceKey: supabaseServiceKey ? 'Set (hidden)' : 'Not set',
        nodeEnv: process.env.NODE_ENV || 'Not set',
      }
    });
  } catch (error: any) {
    console.error('Error checking environment variables:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
