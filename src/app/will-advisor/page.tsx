"use client";

import React from 'react';
import Link from 'next/link';
import { FileText, ArrowRight, CheckCircle, Shield, HelpCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import PageHeading from '@/components/ui/PageHeading';
import BackToDashboard from '@/components/Navigation/BackToDashboard';

export default function WillPage() {
  return (
    <div className="container mx-auto py-8">
      <BackToDashboard />
      <PageHeading
        title="Will Planning"
        description="Create and manage your will to ensure your wishes are carried out."
        icon={<FileText className="h-6 w-6" />}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <Card className="border-blue-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="bg-blue-50 border-b border-blue-100">
            <CardTitle className="flex items-center">
              <HelpCircle className="h-5 w-5 text-blue-600 mr-2" />
              Will Advisor
            </CardTitle>
            <CardDescription>
              Get personalized recommendations based on your situation
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <ul className="space-y-2">
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Answer simple questions about your situation</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Receive tailored recommendations for your will</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Identify gaps in your estate planning</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Get guidance on next steps</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/will-advisor/advisor">
                Start Will Advisor
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-indigo-100 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="bg-indigo-50 border-b border-indigo-100">
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 text-indigo-600 mr-2" />
              Create Your Will
            </CardTitle>
            <CardDescription>
              Create a legally valid will document
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <ul className="space-y-2">
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Step-by-step will creation process</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Specify beneficiaries and asset distribution</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Appoint executors and guardians</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <span>Generate a printable will document</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link href="/will-advisor/create">
                Create Will
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Shield className="h-5 w-5 text-primary mr-2" />
          Why Create a Will with Legalock?
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium mb-2">Protect Your Loved Ones</h4>
            <p className="text-gray-600 text-sm">
              Ensure your assets are distributed according to your wishes and provide for your family's future.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">Secure Digital Legacy</h4>
            <p className="text-gray-600 text-sm">
              Include provisions for your digital assets, online accounts, and digital legacy.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">Peace of Mind</h4>
            <p className="text-gray-600 text-sm">
              Rest easy knowing your affairs are in order and your wishes will be respected.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
