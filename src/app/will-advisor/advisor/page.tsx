"use client";

import React from 'react';
import { WillAdvisorProvider, useWillAdvisor } from '@/context/will-advisor-context';
import QuestionCard from '@/components/WillAdvisor/QuestionCard';
import Recommendations from '@/components/WillAdvisor/Recommendations';
import PageHeading from '@/components/ui/PageHeading';
import { Progress } from '@/components/ui/progress';
import { Clipboard, FileText } from 'lucide-react';

function WillAdvisorContent() {
  const { currentQuestion, state, visibleQuestions } = useWillAdvisor();
  const progress = visibleQuestions.length > 0 
    ? Math.round((state.currentStep / visibleQuestions.length) * 100) 
    : 0;

  return (
    <div className="container mx-auto py-8">
      <PageHeading
        title="Will Advisor"
        description="Answer a few questions to get personalized recommendations for your will."
        icon={<Clipboard className="h-6 w-6" />}
      />

      <div className="mb-8">
        <div className="flex justify-between text-sm text-gray-500 mb-2">
          <span>Progress</span>
          <span>{progress}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {state.completed ? (
        <Recommendations />
      ) : (
        currentQuestion && <QuestionCard question={currentQuestion} />
      )}
    </div>
  );
}

export default function WillAdvisorPage() {
  return (
    <WillAdvisorProvider>
      <WillAdvisorContent />
    </WillAdvisorProvider>
  );
}
