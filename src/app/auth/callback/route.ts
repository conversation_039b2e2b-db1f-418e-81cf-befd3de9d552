import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const provider = requestUrl.searchParams.get('provider');

  // Get the redirect_to parameter if it exists
  const redirectTo = requestUrl.searchParams.get('redirect_to');

  // Log all search parameters for debugging
  const allParams = {};
  requestUrl.searchParams.forEach((value, key) => {
    allParams[key] = value;
  });

  console.log('Auth callback route triggered', {
    code: !!code,
    provider,
    redirectTo,
    url: request.url,
    allParams,
    headers: Object.fromEntries(request.headers)
  });

  try {
    if (code) {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

      console.log('Exchanging code for session...');
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        // If there's an error, redirect to login
        return NextResponse.redirect(new URL(`/login?error=auth&message=${encodeURIComponent(error.message)}`, request.url));
      }

      // If we have a session, handle profile creation for Google users and redirect to dashboard
      if (data.session) {
        console.log('Session obtained successfully');

        // For Google users, ensure email is verified and create profile if needed
        if (data.session.user?.app_metadata?.provider === 'google') {
          console.log('Google user detected, checking for profile');

          // Check if the user already has a profile
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', data.session.user.id)
            .single();

          if (profileError && profileError.code !== 'PGRST116') {
            console.error('Error checking for existing profile:', profileError);
          }

          // If no profile exists, create one
          if (!profileData) {
            console.log('No profile found, creating one for Google user');

            // Extract name from Google metadata
            let firstName = '';
            let lastName = '';

            const fullName = data.session.user.user_metadata?.full_name ||
                          data.session.user.user_metadata?.name ||
                          data.session.user.user_metadata?.email?.split('@')[0] || '';

            if (fullName) {
              const nameParts = fullName.split(' ');
              firstName = nameParts[0] || '';
              lastName = nameParts.slice(1).join(' ') || '';
            }

            // Ensure we have at least some value for first name
            if (!firstName && data.session.user.email) {
              firstName = data.session.user.email.split('@')[0] || 'User';
            }

            const profileData = {
              id: data.session.user.id,
              first_name: firstName,
              last_name: lastName,
              email: data.session.user.email,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            console.log('Creating profile with data:', profileData);

            const { error: insertError } = await supabase
              .from('profiles')
              .insert(profileData);

            if (insertError) {
              console.error('Error creating profile for Google user:', insertError);
            } else {
              console.log('Profile created successfully for Google user');
            }
          } else {
            console.log('Existing profile found for Google user');
          }
        }

        console.log('Redirecting to dashboard');

        // Add a small delay to ensure cookies are properly set
        await new Promise(resolve => setTimeout(resolve, 500));

        // Always redirect to the dashboard
        // For local development, redirect to localhost dashboard
        // For production, redirect to production dashboard
        const baseUrl = process.env.NODE_ENV === 'production'
          ? 'https://legalock.com'
          : new URL(request.url).origin;

        const dashboardUrl = new URL('/dashboard', baseUrl).toString();
        console.log('Redirecting directly to dashboard:', dashboardUrl);
        return NextResponse.redirect(dashboardUrl);
      } else {
        console.error('No session data returned');
        return NextResponse.redirect(new URL('/login?error=no-session', request.url));
      }
    } else {
      console.error('No code parameter in callback URL');
      return NextResponse.redirect(new URL('/login?error=no-code', request.url));
    }
  } catch (error) {
    console.error('Unexpected error in auth callback:', error);
    return NextResponse.redirect(new URL(`/login?error=unexpected&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`, request.url));
  }
}
