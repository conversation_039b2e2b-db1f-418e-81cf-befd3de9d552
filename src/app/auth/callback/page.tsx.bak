"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Loader2 } from 'lucide-react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string>('Please wait while we complete your authentication...');
  const supabase = createClientComponentClient();

  useEffect(() => {
    // Get all search params for debugging
    const allParams = {};
    searchParams.forEach((value, key) => {
      allParams[key] = value;
    });
    console.log('Auth callback page loaded with params:', allParams);

    const code = searchParams.get('code');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');
    const redirectTo = searchParams.get('redirect_to');

    console.log('Auth callback key params:', {
      code: !!code,
      error,
      errorDescription,
      redirectTo
    });

    // If there's an error in the URL params, handle it
    if (error) {
      setError(`Authentication error: ${error}${errorDescription ? ` - ${errorDescription}` : ''}`);
      router.push(`/login?error=provider&message=${encodeURIComponent(errorDescription || error)}`);
      return;
    }

    async function handleCallback() {
      try {
        if (!code) {
          // Check if we already have a session
          const { data: { session: existingSession } } = await supabase.auth.getSession();

          if (existingSession) {
            // We already have a session, redirect to dashboard
            window.location.href = '/dashboard';
            return;
          }

          setError('No authentication code found');
          router.push('/login?error=no-code');
          return;
        }

        // Exchange the code for a session
        const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

        if (exchangeError) {
          setError(exchangeError.message);
          router.push(`/login?error=auth&message=${encodeURIComponent(exchangeError.message)}`);
          return;
        }

        // Check if we have a session
        const { data: { session } } = await supabase.auth.getSession();

        if (session) {
          // We have a session, always redirect to dashboard
          // For local development, redirect to localhost dashboard
          // For production, redirect to production dashboard
          const baseUrl = process.env.NODE_ENV === 'production'
            ? 'https://legalock.com'
            : window.location.origin;

          console.log('Redirecting directly to dashboard:', `${baseUrl}/dashboard`);
          window.location.href = `${baseUrl}/dashboard`;
        } else {
          setError('Authentication successful but no session was created');
          router.push('/login?error=no-session');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
        router.push(`/login?error=unexpected&message=${encodeURIComponent(err instanceof Error ? err.message : 'Unknown error')}`);
      }
    }

    handleCallback();
  }, [router, searchParams, supabase.auth]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-xl shadow-md">
        <div className="flex flex-col items-center justify-center text-center">
          <h2 className="mt-6 text-2xl font-bold text-gray-900">
            {error ? 'Authentication Error' : 'Completing Authentication'}
          </h2>

          {error ? (
            <div className="mt-4 text-red-600">
              <p>{error}</p>
              <p className="mt-2 text-sm text-gray-500">Redirecting you back to login...</p>
            </div>
          ) : (
            <div className="mt-4 flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
              <p className="mt-4 text-gray-600">
                {message}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
