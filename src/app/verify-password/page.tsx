"use client";

import React, { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Shield, Lock, ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/auth-context';
import { toast } from 'sonner';
import AuthHeader from '@/components/Navigation/AuthHeader';

// Import the shared fallback component
import ClientFallback from '@/components/ClientFallback';

// Main component that uses searchParams
function VerifyPasswordContent() {
  const [verificationCode, setVerificationCode] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const { verifyOtp } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get email from URL query parameter
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      toast.error('Email address is missing. Please go back and try again.');
      return;
    }

    if (!verificationCode || verificationCode.length !== 6) {
      toast.error('Please enter a valid 6-digit verification code.');
      return;
    }

    try {
      setIsSubmitting(true);

      // Use the same verifyOtp function but with 'recovery' type
      await verifyPasswordOtp(email, verificationCode);

      toast.success('Password updated successfully!');
      router.push('/settings/security');
    } catch (error: any) {
      console.error('Verification error:', error);
      toast.error(error.message || 'Error verifying code');
      setIsSubmitting(false);
    }
  };

  const verifyPasswordOtp = async (email: string, token: string) => {
    const { supabase } = await import('@/lib/supabase');
    const { error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'recovery',
    });

    if (error) throw error;
  };

  const handleResendCode = async () => {
    if (!email) {
      toast.error('Email address is missing. Please go back and try again.');
      return;
    }

    try {
      setIsResending(true);

      // Use Supabase to resend the verification code
      const { supabase } = await import('@/lib/supabase');
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: undefined,
      });

      if (error) {
        throw new Error(error.message);
      }

      toast.success('Verification code resent. Please check your email.');
    } catch (error: any) {
      toast.error(error.message || 'Failed to resend verification code');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <AuthHeader />
      <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div className="flex flex-col items-center">
            <div className="flex items-center gap-2">
              <Shield className="h-10 w-10 text-primary" />
              <h1 className="text-2xl font-bold">Legalock</h1>
            </div>
            <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
              Verify Password Change
            </h2>
            <div className="mt-8 flex flex-col items-center">
              <div className="bg-blue-50 p-6 rounded-full">
                <Lock className="h-12 w-12 text-primary" />
              </div>
              <p className="mt-6 text-center text-gray-600">
                We've sent a 6-digit verification code to {email || 'your email address'}. Please enter the code below to confirm your password change.
              </p>
              <p className="mt-4 text-center text-gray-500 text-sm">
                If you don't see the email, check your spam folder or click the resend button below.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="mt-8 w-full">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="verification-code">Verification Code</Label>
                  <Input
                    id="verification-code"
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={6}
                    placeholder="Enter 6-digit code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/[^0-9]/g, ''))}
                    className="mt-1 block w-full text-center text-lg tracking-widest"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting || verificationCode.length !== 6}
                >
                  {isSubmitting ? 'Verifying...' : 'Confirm Password Change'}
                </Button>

                <div className="flex justify-between items-center">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleResendCode}
                    disabled={isResending}
                    className="text-sm"
                  >
                    {isResending ? (
                      <>
                        <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                        Resending...
                      </>
                    ) : (
                      'Resend code'
                    )}
                  </Button>

                  <Button variant="ghost" size="sm" asChild className="text-sm">
                    <Link href="/settings/security">
                      <ArrowLeft className="mr-1 h-3 w-3" />
                      Back to Security
                    </Link>
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div className="relative hidden w-0 flex-1 lg:block">
        <div className="absolute inset-0 h-full w-full bg-gradient-to-r from-blue-600 to-indigo-600">
          <div className="flex h-full items-center justify-center p-12">
            <div className="max-w-xl text-white">
              <h2 className="text-3xl font-bold mb-6">Secure Your Digital Legacy</h2>
              <p className="text-xl mb-8">
                Legalock helps you organize, protect, and pass on your digital assets and final wishes to your loved ones.
              </p>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <Shield className="h-6 w-6 mr-2 flex-shrink-0" />
                  <span>Create a comprehensive inventory of your digital assets</span>
                </li>
                <li className="flex items-start">
                  <Shield className="h-6 w-6 mr-2 flex-shrink-0" />
                  <span>Securely store sensitive documents for your trustees</span>
                </li>
                <li className="flex items-start">
                  <Shield className="h-6 w-6 mr-2 flex-shrink-0" />
                  <span>Schedule future messages to loved ones</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Export the page component with Suspense
export default function VerifyPasswordPage() {
  return (
    <Suspense fallback={<ClientFallback />}>
      <VerifyPasswordContent />
    </Suspense>
  );
}
