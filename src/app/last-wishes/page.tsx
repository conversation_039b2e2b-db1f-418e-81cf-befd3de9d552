"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { LastWishes } from '@/types/database.types';
import {
  Heart,
  Save,
  Loader2,
  AlertTriangle,
  Info,
  Users,
  Leaf,
  PawPrint,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import PageHeading from '@/components/ui/PageHeading';

import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import USStatesSelect from '@/components/ui/USStatesSelect';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function LastWishesPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [lastWishes, setLastWishes] = useState<Partial<LastWishes>>({
    funeral_wishes: '',
    burial_wishes: '',
    burial_option: '',
    personal_messages: '',
    show_personal_messages: false,
    is_organ_donor: false,
    organ_donor_country: 'USA',
    organ_donor_state: '',
    has_pets: false,
    pet_care_instructions: '',
    other_wishes: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Burial options
  const burialOptions = [
    { value: 'burial', label: 'Traditional Burial' },
    { value: 'cremation', label: 'Cremation' },
    { value: 'green_burial', label: 'Green/Natural Burial' },
    { value: 'donation', label: 'Body Donation to Science' },
    { value: 'other', label: 'Other' },
  ];

  // Countries with organ donation programs
  const organDonorCountries = [
    { value: 'USA', label: 'United States' },
    { value: 'CAN', label: 'Canada' },
    { value: 'GBR', label: 'United Kingdom' },
    { value: 'AUS', label: 'Australia' },
    { value: 'NZL', label: 'New Zealand' },
    { value: 'DEU', label: 'Germany' },
    { value: 'FRA', label: 'France' },
    { value: 'ESP', label: 'Spain' },
    { value: 'ITA', label: 'Italy' },
    { value: 'JPN', label: 'Japan' },
    { value: 'OTHER', label: 'Other' },
  ];

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user) {
      fetchLastWishes();
    }
  }, [user, loading, router]);

  const fetchLastWishes = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/last-wishes');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch last wishes');
      }

      const data = await response.json();

      if (data && Object.keys(data).length > 0) {
        setLastWishes(data);
      }
    } catch (error: any) {
      console.error('Error fetching last wishes:', error);
      toast.error(error.message || 'Failed to load your last wishes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setLastWishes(prev => ({ ...prev, [name]: value }));
    setHasUnsavedChanges(true);
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setLastWishes(prev => ({ ...prev, [name]: checked }));
    setHasUnsavedChanges(true);
  };

  const handleStateChange = (value: string) => {
    setLastWishes(prev => ({ ...prev, organ_donor_state: value }));
    setHasUnsavedChanges(true);
  };

  const handleCountryChange = (value: string) => {
    // If changing from USA to another country, clear the state
    const newState = value !== 'USA' ? '' : lastWishes.organ_donor_state;
    setLastWishes(prev => ({
      ...prev,
      organ_donor_country: value,
      organ_donor_state: newState
    }));
    setHasUnsavedChanges(true);
  };

  const handleSave = async () => {
    try {
      if (!user) return;

      setIsSaving(true);

      // Check if we have an ID (for update) or not (for create)
      if (lastWishes.id) {
        // Update existing record
        const response = await fetch('/api/last-wishes', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(lastWishes),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update last wishes');
        }

        const updatedData = await response.json();
        setLastWishes(updatedData);
      } else {
        // Insert new record
        const response = await fetch('/api/last-wishes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(lastWishes),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create last wishes');
        }

        const newData = await response.json();
        setLastWishes(newData);
      }

      toast.success('Last wishes saved successfully');
      setHasUnsavedChanges(false);
    } catch (error: any) {
      console.error('Error saving last wishes:', error);
      toast.error(error.message || 'Failed to save last wishes');
    } finally {
      setIsSaving(false);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="container mx-auto py-6 px-4 md:px-6 text-center">
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <div className="mt-12">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading your last wishes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Last Wishes"
        description="Document your final wishes to share with your trustees"
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-6">
        <div className="lg:col-span-2 space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Funeral & Memorial Preferences</CardTitle>
              <CardDescription>
                Document your preferences for funeral services and memorials
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                name="funeral_wishes"
                placeholder="Describe your preferences for funeral services, memorial gatherings, or celebrations of life..."
                className="min-h-[150px]"
                value={lastWishes.funeral_wishes || ''}
                onChange={handleInputChange}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Burial & Remains Preferences</CardTitle>
              <CardDescription>
                Document your preferences for burial, cremation, or other arrangements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="burial-option">Preferred Option</Label>
                <Select
                  value={lastWishes.burial_option || ''}
                  onValueChange={(value) => {
                    setLastWishes(prev => ({ ...prev, burial_option: value }));
                    setHasUnsavedChanges(true);
                  }}
                >
                  <SelectTrigger id="burial-option">
                    <SelectValue placeholder="Select a burial option" />
                  </SelectTrigger>
                  <SelectContent>
                    {burialOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="burial-wishes">Additional Details</Label>
                <Textarea
                  id="burial-wishes"
                  name="burial_wishes"
                  placeholder="Provide additional details about your burial or remains preferences..."
                  className="min-h-[150px]"
                  value={lastWishes.burial_wishes || ''}
                  onChange={handleInputChange}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Personal Messages</CardTitle>
              <CardDescription>
                Leave personal messages for your loved ones
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-personal-messages">Enable Personal Messages</Label>
                  <p className="text-sm text-gray-500">
                    Toggle to write personal messages for your loved ones
                  </p>
                </div>
                <Switch
                  id="show-personal-messages"
                  checked={lastWishes.show_personal_messages}
                  onCheckedChange={(checked) => handleSwitchChange('show_personal_messages', checked)}
                />
              </div>

              {lastWishes.show_personal_messages && (
                <div className="pt-2">
                  <Textarea
                    name="personal_messages"
                    placeholder="Write personal messages, thoughts, or reflections you'd like to share with your loved ones..."
                    className="min-h-[150px]"
                    value={lastWishes.personal_messages || ''}
                    onChange={handleInputChange}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Other Wishes</CardTitle>
              <CardDescription>
                Document any other final wishes or instructions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                name="other_wishes"
                placeholder="Document any other final wishes, instructions, or preferences not covered in the sections above..."
                className="min-h-[150px]"
                value={lastWishes.other_wishes || ''}
                onChange={handleInputChange}
              />
            </CardContent>
          </Card>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Leaf className="h-5 w-5 text-green-600 mr-2" />
                Organ Donation
              </CardTitle>
              <CardDescription>
                Indicate your organ donation preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="organ-donor">I am an organ donor</Label>
                  <p className="text-sm text-gray-500">
                    Indicate if you are registered as an organ donor
                  </p>
                </div>
                <Switch
                  id="organ-donor"
                  checked={lastWishes.is_organ_donor}
                  onCheckedChange={(checked) => handleSwitchChange('is_organ_donor', checked)}
                />
              </div>

              {lastWishes.is_organ_donor && (
                <div className="space-y-4 pt-2">
                  <div>
                    <Label htmlFor="donor-country">Country</Label>
                    <Select
                      value={lastWishes.organ_donor_country || 'USA'}
                      onValueChange={handleCountryChange}
                    >
                      <SelectTrigger id="donor-country">
                        <SelectValue placeholder="Select a country" />
                      </SelectTrigger>
                      <SelectContent>
                        {organDonorCountries.map((country) => (
                          <SelectItem key={country.value} value={country.value}>
                            {country.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      Select the country where you are registered as an organ donor
                    </p>
                  </div>

                  {lastWishes.organ_donor_country === 'USA' && (
                    <div>
                      <Label htmlFor="donor-state">State</Label>
                      <USStatesSelect
                        value={lastWishes.organ_donor_state || ''}
                        onValueChange={handleStateChange}
                        placeholder="Select the state where you're registered"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Select the state where you are registered as an organ donor
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PawPrint className="h-5 w-5 text-amber-600 mr-2" />
                Pet Care
              </CardTitle>
              <CardDescription>
                Provide instructions for the care of your pets
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="has-pets">I have pets</Label>
                  <p className="text-sm text-gray-500">
                    Indicate if you have pets that will need care
                  </p>
                </div>
                <Switch
                  id="has-pets"
                  checked={lastWishes.has_pets}
                  onCheckedChange={(checked) => handleSwitchChange('has_pets', checked)}
                />
              </div>

              {lastWishes.has_pets && (
                <div className="space-y-2 pt-2">
                  <Label htmlFor="pet-care">Pet Care Instructions</Label>
                  <Textarea
                    id="pet-care"
                    name="pet_care_instructions"
                    placeholder="Provide details about your pets and instructions for their care..."
                    className="min-h-[100px]"
                    value={lastWishes.pet_care_instructions || ''}
                    onChange={handleInputChange}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 text-blue-600 mr-2" />
                Trustee Access
              </CardTitle>
              <CardDescription>
                Information about who can access your last wishes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-blue-700">
                      Your last wishes will be shared with your trustees after your passing. Make sure to keep this information up to date.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/trustees')}
                >
                  Manage Trustees
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="sticky top-4">
            <Card className={hasUnsavedChanges ? 'border-amber-300 shadow-md' : ''}>
              <CardContent className="pt-6">
                {hasUnsavedChanges && (
                  <div className="bg-amber-50 p-3 rounded-md mb-4 flex items-start">
                    <AlertTriangle className="h-5 w-5 text-amber-600 mr-2 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-amber-800">
                      You have unsaved changes. Please save your changes before leaving this page.
                    </p>
                  </div>
                )}

                <Button
                  className="w-full"
                  onClick={handleSave}
                  disabled={isSaving || !hasUnsavedChanges}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Last Wishes
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
