"use client";

import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function LanderRedirectPage() {
  const [isRedirecting, setIsRedirecting] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const supabase = createClientComponentClient();

  useEffect(() => {
    // Check if the user is authenticated
    const checkAuth = async () => {
      const { data } = await supabase.auth.getSession();
      setIsAuthenticated(!!data.session);
    };

    checkAuth();

    // This is a special redirect page that will redirect to the local dashboard
    // when developing locally but was redirected to the production lander

    // Check if we have the localDevelopment flag in localStorage
    const isLocalDevelopment = localStorage.getItem('localDevelopment') === 'true';
    const localOrigin = localStorage.getItem('localOrigin') || 'http://localhost:3000';

    if (isLocalDevelopment) {
      console.log('Local development detected, redirecting to local dashboard');
      console.log('Local origin:', localOrigin);

      // Small delay to ensure the message is visible
      setTimeout(() => {
        // Clear the localStorage flags
        localStorage.removeItem('localDevelopment');
        localStorage.removeItem('localOrigin');

        // Redirect to the local dashboard
        window.location.href = `${localOrigin}/dashboard`;
      }, 1500);
    } else {
      // In production, we would handle this differently
      console.log('Production environment, staying on lander page');
      setIsRedirecting(false);
    }
  }, [supabase.auth]);

  const goToDashboard = () => {
    // For local development
    const localOrigin = localStorage.getItem('localOrigin') || 'http://localhost:3000';
    if (process.env.NODE_ENV === 'development') {
      window.location.href = `${localOrigin}/dashboard`;
    } else {
      // For production
      window.location.href = '/dashboard';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center p-8 max-w-md">
        {isRedirecting ? (
          <>
            <h1 className="text-2xl font-bold mb-4">Redirecting to Dashboard</h1>
            <p className="mb-6">
              You've been authenticated successfully. Redirecting you to your local dashboard...
            </p>
            <div className="flex justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          </>
        ) : (
          <>
            <h1 className="text-2xl font-bold mb-4">Welcome to Legalock</h1>
            <p className="mb-6">
              {isAuthenticated
                ? "You're authenticated and ready to use Legalock. Go to your dashboard to manage your digital legacy."
                : "Legalock helps you manage your digital legacy and ensure your wishes are documented and secure."}
            </p>
            {isAuthenticated && (
              <Button onClick={goToDashboard} className="w-full">
                Go to Dashboard
              </Button>
            )}
            {!isAuthenticated && (
              <Button onClick={() => window.location.href = '/login'} className="w-full">
                Sign In
              </Button>
            )}
          </>
        )}
      </div>
    </div>
  );
}
