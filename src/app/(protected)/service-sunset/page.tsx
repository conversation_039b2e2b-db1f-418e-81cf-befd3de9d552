"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { ServiceSunset } from '@/types/service-sunset.types';

type ServiceSunsetFormData = {
  name: string;
  category: string;
  custom_category?: string;
  description?: string;
  account_number?: string;
  cancellation_instructions?: string;
  website?: string;
  contact_info?: string;
};
import {
  Sunset,
  PlusCircle,
  ArrowLeft,
  Pencil,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';
import PageHeading from '@/components/ui/PageHeading';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import ServiceSunsetForm from '@/components/ServiceSunset/ServiceSunsetForm';

export default function ServiceSunsetPage() {
  const { user } = useAuth();
  const [services, setServices] = useState<ServiceSunset[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<ServiceSunset | null>(null);

  useEffect(() => {
    if (user) {
      fetchServices();
    }
  }, [user]);

  const fetchServices = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/service-sunset');

      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }

      const data = await response.json();
      setServices(data);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast.error('Failed to load services');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateService = () => {
    setSelectedService(null);
    setIsFormOpen(true);
  };

  const handleEditService = (service: ServiceSunset) => {
    setSelectedService(service);
    setIsFormOpen(true);
  };

  const handleDeleteService = (service: ServiceSunset) => {
    setSelectedService(service);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteService = async () => {
    if (!selectedService) return;

    try {
      const response = await fetch(`/api/service-sunset?id=${selectedService.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete service');
      }

      toast.success('Service deleted successfully');
      fetchServices();
    } catch (error: any) {
      console.error('Error deleting service:', error);
      toast.error(error.message || 'Error deleting service');
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedService(null);
    }
  };

  const handleAddService = async (data: ServiceSunsetFormData) => {
    try {
      const response = await fetch('/api/service-sunset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.name,
          category: data.category,
          custom_category: data.category === 'other' ? data.custom_category : '',
          description: data.description || '',
          account_number: data.account_number || '',
          cancellation_instructions: data.cancellation_instructions || '',
          website: data.website || '',
          contact_info: data.contact_info || '',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add service');
      }

      toast.success('Service added successfully');
      setIsFormOpen(false);
      fetchServices();
    } catch (error: any) {
      console.error('Error adding service:', error);
      toast.error(error.message || 'Error adding service');
    }
  };

  const handleUpdateService = async (data: ServiceSunsetFormData) => {
    if (!selectedService) return;

    try {
      const response = await fetch('/api/service-sunset', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedService.id,
          name: data.name,
          category: data.category,
          custom_category: data.category === 'other' ? data.custom_category : '',
          description: data.description || '',
          account_number: data.account_number || '',
          cancellation_instructions: data.cancellation_instructions || '',
          website: data.website || '',
          contact_info: data.contact_info || '',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update service');
      }

      toast.success('Service updated successfully');
      setIsFormOpen(false);
      fetchServices();
    } catch (error: any) {
      console.error('Error updating service:', error);
      toast.error(error.message || 'Error updating service');
    }
  };

  const getCategoryLabel = (service: ServiceSunset) => {
    const categories: Record<string, string> = {
      'utilities': 'Utilities',
      'internet': 'Internet',
      'streaming': 'Streaming',
      'subscription': 'Subscription',
      'membership': 'Membership',
      'financial': 'Financial',
      'insurance': 'Insurance',
      'telecom': 'Telecom',
      'other': 'Other'
    };

    // Safely check for custom category
    if (service.category === 'other' && 'custom_category' in service && service.custom_category) {
      return service.custom_category as string;
    }

    return categories[service.category] || service.category;
  };



  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Service Sunset"
        description="Manage services that need to be canceled by your trustees after your passing."
        actions={
          services.length > 0 && (
            <Button onClick={handleCreateService}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Service
            </Button>
          )
        }
      />

      <div className="mt-6">
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">
              Your Services
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({services.length} {services.length === 1 ? 'service' : 'services'})
              </span>
            </h3>
            {isLoading ? (
              // Loading skeletons
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                    <Skeleton className="h-8 w-16" />
                  </div>
                ))}
              </div>
            ) : services.length === 0 ? (
              // Empty state
              <div className="text-center py-12">
                <Sunset className="mx-auto h-12 w-12 text-gray-400" />
                <h4 className="mt-4 text-lg font-medium text-gray-900">No Services Yet</h4>
                <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                  Add services that need to be canceled by your trustees after your passing, such as utilities, subscriptions, or memberships.
                </p>
                <Button onClick={handleCreateService} className="mt-4">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Your First Service
                </Button>
              </div>
            ) : (
              // Service list
              <div className="space-y-4">
                {services.map((service) => (
                  <div key={service.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="bg-orange-100 rounded-full p-2">
                        <Sunset className="h-6 w-6 text-orange-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{service.name}</h4>
                        <p className="text-sm text-gray-500">{getCategoryLabel(service)}</p>
                        {service.description && (
                          <p className="text-sm text-gray-500 mt-1">{service.description}</p>
                        )}
                        {service.account_number && (
                          <p className="text-xs text-gray-500 mt-1">Account: {service.account_number}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleEditService(service)}>
                        <Pencil className="h-4 w-4 mr-2" />
                        Edit
                      </Button>

                      <Button variant="ghost" size="sm" onClick={() => handleDeleteService(service)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Service Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedService ? 'Edit Service' : 'Add New Service'}</DialogTitle>
            <DialogDescription>
              {selectedService
                ? 'Update the details of this service that needs to be canceled after your passing.'
                : 'Add a service that needs to be canceled by your trustees after your passing.'}
            </DialogDescription>
          </DialogHeader>
          <ServiceSunsetForm
            onSubmit={selectedService ? handleUpdateService : handleAddService}
            onCancel={() => setIsFormOpen(false)}
            defaultValues={selectedService || undefined}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently remove {selectedService?.name} from your services.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteService} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
