"use client";

import React from 'react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import PageHeading from '@/components/ui/PageHeading';
import TrusteeInviteForm from '@/components/Trustees/TrusteeInviteForm';

export default function TrusteeInvitePage() {
  return (
    <div>
      <div className="mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/trustees" className="flex items-center text-gray-500 hover:text-gray-700">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Trustees
          </Link>
        </Button>
      </div>
      
      <PageHeading 
        title="Invite a Trustee" 
        description="Invite someone you trust to have access to your digital legacy." 
      />
      
      <div className="mt-6 bg-white rounded-lg border p-6">
        <TrusteeInviteForm />
      </div>
    </div>
  );
}
