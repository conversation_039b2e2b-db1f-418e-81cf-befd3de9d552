"use client";

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { FileText, Download, Eye } from 'lucide-react';
import PageHeading from '@/components/ui/PageHeading';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

export default function WillPage() {
  return (
    <div>
      <PageHeading 
        title="Will & Testament" 
        description="Create and manage your legal will and final wishes."
        actions={
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            Start Will Creation
          </Button>
        }
      />
      
      <div className="mt-6">
        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="witnesses">Witnesses</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Will Status</CardTitle>
                <CardDescription>
                  Current status of your will and important information.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between p-4 bg-amber-50 rounded-lg border border-amber-200">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-amber-200 flex items-center justify-center">
                      <FileText className="h-5 w-5 text-amber-700" />
                    </div>
                    <div className="ml-4">
                      <h3 className="font-medium">Will In Progress</h3>
                      <p className="text-sm text-gray-600">You've started creating your will but haven't finalized it.</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
                    Draft
                  </Badge>
                </div>
                
                <div className="mt-6 space-y-4">
                  <h3 className="font-medium">Completion Progress</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Personal Details</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700">Completed</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Assets & Beneficiaries</span>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700">In Progress</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Executor Appointment</span>
                      <Badge variant="outline" className="bg-gray-100">Not Started</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Witnesses & Signing</span>
                      <Badge variant="outline" className="bg-gray-100">Not Started</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button>Continue Will Creation</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="documents" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Will Documents</CardTitle>
                <CardDescription>
                  Access and manage your will documents.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="divide-y">
                  <div className="py-4 flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Will - Draft Version</h4>
                      <p className="text-sm text-gray-500">Last updated: March 15, 2025</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="witnesses" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Witnesses</CardTitle>
                <CardDescription>
                  Manage witnesses for your will signing.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Your will requires at least two witnesses who are not beneficiaries to be legally valid.
                </p>
                
                <Button>Add Witnesses</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
