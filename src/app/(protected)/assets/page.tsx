"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Asset } from '@/types/database.types';
import Image from 'next/image';

type AssetFormData = Omit<Partial<Asset>, 'id' | 'user_id' | 'created_at' | 'updated_at'>;
import { ASSET_CATEGORIES } from '@/types/asset.types';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PlusCircle, ArrowLeft, Edit, Trash2, Home, Package, Gem } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import AssetForm from '@/components/Assets/AssetForm';
import AssetPieChart from '@/components/Assets/AssetPieChart';
import PageHeading from '@/components/ui/PageHeading';
import { formatCurrency } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

export default function AssetsPage() {
  const router = useRouter();
  const [assets, setAssets] = useState<Asset[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([]);

  // Fetch assets
  useEffect(() => {
    const fetchAssets = async () => {
      try {
        const response = await fetch('/api/assets');
        if (!response.ok) {
          throw new Error('Failed to fetch assets');
        }
        const data = await response.json();
        setAssets(data);
        setFilteredAssets(data);
      } catch (error) {
        console.error('Error fetching assets:', error);
        toast.error('Failed to load assets');
        // Use mock data if API fails
        const mockAssets = [
          {
            id: '1',
            user_id: '1',
            name: 'Primary Residence',
            type: 'physical',
            category: 'real_estate',
            value: 450000,
            currency: 'USD',
            location: '123 Main St',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: '2',
            user_id: '1',
            name: 'Wedding Ring',
            type: 'physical',
            category: 'jewelry',
            value: 5000,
            currency: 'USD',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            image_url: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
          },
          {
            id: '3',
            user_id: '1',
            name: 'Car - Toyota Camry',
            type: 'physical',
            category: 'vehicle',
            value: 18000,
            currency: 'USD',
            location: 'Garage',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: '4',
            user_id: '1',
            name: 'Antique Watch',
            type: 'physical',
            category: 'antique',
            value: 3500,
            currency: 'USD',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            image_url: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
          },
          {
            id: '5',
            user_id: '1',
            name: 'Family Heirloom Vase',
            type: 'physical',
            category: 'heirloom',
            value: 2000,
            currency: 'USD',
            location: 'Living Room Cabinet',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            image_url: 'https://images.unsplash.com/photo-1493106641515-6b5631de4bb9?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
          },
        ];
        setAssets(mockAssets as Asset[]);
        setFilteredAssets(mockAssets as Asset[]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssets();
  }, []);

  // Filter assets when category changes
  useEffect(() => {
    if (selectedCategory) {
      setFilteredAssets(assets.filter(asset => asset.category === selectedCategory));
    } else {
      setFilteredAssets(assets);
    }
  }, [selectedCategory, assets]);

  const handleCreateAsset = () => {
    setSelectedAsset(null);
    setIsFormOpen(true);
  };

  const handleEditAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsFormOpen(true);
  };

  const handleDeleteAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteAsset = async () => {
    if (!selectedAsset) return;

    try {
      const response = await fetch(`/api/assets?id=${selectedAsset.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete asset');
      }

      // Remove the asset from the state
      setAssets(assets.filter(asset => asset.id !== selectedAsset.id));
      toast.success('Asset deleted successfully');
    } catch (error) {
      console.error('Error deleting asset:', error);
      toast.error('Failed to delete asset');
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedAsset(null);
    }
  };

  const handleAssetFormSubmit = async (formData: AssetFormData) => {
    try {
      const method = selectedAsset ? 'PUT' : 'POST';
      const url = '/api/assets';
      const body = selectedAsset ? { id: selectedAsset.id, ...formData } : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${selectedAsset ? 'update' : 'create'} asset`);
      }

      const data = await response.json();

      if (selectedAsset) {
        // Update the asset in the state
        setAssets(assets.map(asset => (asset.id === selectedAsset.id ? data : asset)));
        toast.success('Asset updated successfully');
      } else {
        // Add the new asset to the state
        setAssets([data, ...assets]);
        toast.success('Asset created successfully');
      }

      setIsFormOpen(false);
    } catch (error) {
      console.error('Error saving asset:', error);
      toast.error(`Failed to ${selectedAsset ? 'update' : 'create'} asset`);

      // For demo purposes, simulate successful operation with mock data
      if (!selectedAsset) {
        const newAsset = {
          id: `temp-${Date.now()}`,
          user_id: '1',
          name: formData.name,
          type: 'physical',
          category: formData.category,
          description: formData.description,
          value: formData.value,
          currency: formData.currency || 'USD',
          location: formData.location,
          image_url: formData.image_url,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setAssets([newAsset as Asset, ...assets]);
        toast.success('Asset created successfully (demo mode)');
      } else {
        const updatedAsset = {
          ...selectedAsset,
          ...formData,
          updated_at: new Date().toISOString()
        };
        setAssets(assets.map(asset => (asset.id === selectedAsset.id ? updatedAsset : asset)));
        toast.success('Asset updated successfully (demo mode)');
      }
      setIsFormOpen(false);
    }
  };

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category === 'all' ? null : category);
  };

  // Helper function to get category label
  const getCategoryLabel = (categoryValue: string) => {
    const category = ASSET_CATEGORIES.find(cat => cat.value === categoryValue);
    return category ? category.label : categoryValue;
  };

  // Convert currency for the pie chart
  const convertCurrency = (value: number, fromCurrency: string, toCurrency: string) => {
    // In a real app, this would use exchange rates
    // For now, we'll just return the value
    return value;
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Asset Management"
        description="Manage your physical assets in one secure place."
        actions={
          assets.length > 0 && (
            <div className="flex gap-2">
              {selectedCategory && (
                <Button variant="outline" onClick={() => setSelectedCategory(null)}>
                  View All
                </Button>
              )}
              <Button onClick={handleCreateAsset}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Asset
              </Button>
            </div>
          )
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* Pie Chart */}
        <div className="lg:col-span-1">
          <AssetPieChart
            assets={assets}
            selectedCurrency="USD"
            currencySymbol="$"
            convertCurrency={convertCurrency}
            onCategorySelect={handleCategorySelect}
          />
        </div>

        {/* Asset List */}
        <div className="lg:col-span-2">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                {selectedCategory
                  ? `${getCategoryLabel(selectedCategory)} Assets`
                  : 'All Assets'}
                <span className="ml-2 text-sm font-normal text-gray-500">
                  ({filteredAssets.length} {filteredAssets.length === 1 ? 'item' : 'items'})
                </span>
              </h3>

              {isLoading ? (
                // Loading skeletons
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <Skeleton className="h-16 w-16 rounded-md" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-1/3" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                      <Skeleton className="h-8 w-16" />
                    </div>
                  ))}
                </div>
              ) : filteredAssets.length === 0 ? (
                // Empty state
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">No assets found</p>
                  <Button onClick={handleCreateAsset}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Your First Asset
                  </Button>
                </div>
              ) : (
                // Asset list
                <div className="space-y-4">
                  {filteredAssets.map((asset) => (
                    <div
                      key={asset.id}
                      className="flex items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors h-24"
                    >
                      {/* Asset Image */}
                      <div className="h-16 w-16 rounded-md bg-gray-100 mr-4 overflow-hidden flex-shrink-0">
                        {asset.image_url ? (
                          <Image
                            src={asset.image_url}
                            alt={asset.name}
                            className="h-full w-full object-cover"
                            width={64}
                            height={64}
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center bg-gray-200">
                            <span className="text-gray-400 text-xs">No image</span>
                          </div>
                        )}
                      </div>

                      {/* Asset Details */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate">{asset.name}</h4>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <span className="bg-blue-50 text-blue-700 px-2 py-0.5 rounded text-xs">
                            {getCategoryLabel(asset.category)}
                          </span>
                          {asset.location && (
                            <span className="ml-2 truncate">{asset.location}</span>
                          )}
                        </div>
                      </div>

                      {/* Asset Value */}
                      <div className="text-right mr-4">
                        {asset.value ? (
                          <div className="font-semibold text-gray-900">
                            {formatCurrency(asset.value, asset.currency || 'USD')}
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">No value</div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditAsset(asset)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteAsset(asset)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Asset Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedAsset ? 'Edit Asset' : 'Add New Asset'}</DialogTitle>
          </DialogHeader>
          <AssetForm
            onSubmit={handleAssetFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            defaultValues={selectedAsset || undefined}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the asset "{selectedAsset?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteAsset} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
