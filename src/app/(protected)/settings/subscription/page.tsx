"use client";

import React from 'react';
import { useSubscription } from '@/context/SubscriptionContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import SubscriptionStatus from '@/components/Subscription/SubscriptionStatus';
import SubscriptionPlans from '@/components/Subscription/SubscriptionPlans';

export default function SubscriptionPage() {
  const { isSubscribed, subscriptionDetails, plan } = useSubscription();
  const router = useRouter();

  const handleCancelSubscription = async () => {
    // This would typically call an API endpoint to cancel the subscription
    // For now, we'll just redirect to a confirmation page
    router.push('/settings/subscription/cancel');
  };

  return (
    <div className="container max-w-5xl py-8">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => router.push('/settings')}
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Settings
        </Button>
        <h1 className="text-2xl font-bold">Subscription Management</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <SubscriptionStatus />
        </div>

        <div>
          {isSubscribed && (
            <Card>
              <CardHeader>
                <CardTitle>Subscription Details</CardTitle>
                <CardDescription>Manage your current subscription</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-sm">Plan</h3>
                    <p>{plan.charAt(0).toUpperCase() + plan.slice(1)}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">Status</h3>
                    <p>{subscriptionDetails?.status}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">Current Period Ends</h3>
                    <p>{subscriptionDetails?.currentPeriodEnd ? new Date(subscriptionDetails.currentPeriodEnd).toLocaleDateString() : 'N/A'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">Auto Renewal</h3>
                    <p>{subscriptionDetails?.cancelAtPeriodEnd ? 'Off' : 'On'}</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  variant="destructive"
                  className="w-full"
                  onClick={handleCancelSubscription}
                >
                  Cancel Subscription
                </Button>
              </CardFooter>
            </Card>
          )}

          {!isSubscribed && (
            <Card>
              <CardHeader>
                <CardTitle>Upgrade Your Plan</CardTitle>
                <CardDescription>Get more features with our premium plan</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionPlans />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
