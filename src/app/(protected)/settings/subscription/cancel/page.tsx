"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export default function CancelSubscriptionPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleCancelSubscription = async () => {
    try {
      setIsLoading(true);
      
      // Call the API to cancel the subscription
      const response = await fetch('/api/stripe/subscription/cancel', {
        method: 'POST',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to cancel subscription');
      }
      
      toast.success('Your subscription has been cancelled');
      router.push('/settings/subscription');
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      toast.error(error.message || 'Failed to cancel subscription');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container max-w-md py-8">
      <Button 
        variant="ghost" 
        size="sm" 
        className="mb-6"
        onClick={() => router.push('/settings/subscription')}
      >
        <ArrowLeft className="h-4 w-4 mr-1" />
        Back to Subscription
      </Button>
      
      <Card>
        <CardHeader>
          <CardTitle>Cancel Subscription</CardTitle>
          <CardDescription>
            Are you sure you want to cancel your subscription?
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              If you cancel, your subscription will remain active until the end of your current billing period. After that, your account will be downgraded to the free plan with limited features.
            </p>
            <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
              <h4 className="font-medium text-amber-800 mb-2">What you'll lose:</h4>
              <ul className="list-disc pl-5 text-sm text-amber-700 space-y-1">
                <li>Unlimited assets and contacts</li>
                <li>5GB vault storage (will be reduced to 1GB)</li>
                <li>Time capsule messages</li>
                <li>Priority support</li>
              </ul>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button 
            variant="destructive" 
            className="w-full"
            onClick={handleCancelSubscription}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Cancelling...
              </>
            ) : (
              'Yes, Cancel My Subscription'
            )}
          </Button>
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => router.push('/settings/subscription')}
            disabled={isLoading}
          >
            No, Keep My Subscription
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
