"use client";

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { supabase } from '@/integrations/supabase/client';
import { TimeCapsule, TimeCapsuleMedia } from '@/types/database.types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, File, Download, ArrowLeft, Mail, User, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';

export default function TimeCapsuleViewPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const id = params.id as string;
  const accessCode = searchParams.get('code');

  const [capsule, setCapsule] = useState<TimeCapsule | null>(null);
  const [media, setMedia] = useState<TimeCapsuleMedia[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [accessCodeInput, setAccessCodeInput] = useState(accessCode || '');

  useEffect(() => {
    const fetchTimeCapsule = async () => {
      if (!id) return;

      try {
        setIsLoading(true);

        // First, try to fetch the capsule with the access code
        const { data, error } = await supabase
          .from('time_capsules')
          .select('*')
          .eq('id', id)
          .eq('status', 'delivered')
          .single();

        if (error) {
          console.error('Error fetching time capsule:', error);
          return;
        }

        setCapsule(data);

        // Check if the access code matches
        if (accessCode && data.access_code === accessCode) {
          setIsAuthenticated(true);
          fetchMedia(id);
        }
      } catch (error) {
        console.error('Error:', error);
        toast.error('Failed to load time capsule');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTimeCapsule();
  }, [id, accessCode]);

  const fetchMedia = async (capsuleId: string) => {
    try {
      const { data, error } = await supabase
        .from('time_capsule_media')
        .select('*')
        .eq('capsule_id', capsuleId);

      if (error) throw error;

      if (data) {
        // For each media item, get a signed URL
        const mediaWithUrls = await Promise.all(data.map(async (item) => {
          const { data: urlData } = await supabase.storage
            .from('time_capsule_media')
            .createSignedUrl(item.file_path, 60 * 60); // 1 hour expiry

          return {
            ...item,
            signed_url: urlData?.signedUrl
          };
        }));

        setMedia(mediaWithUrls as TimeCapsuleMedia[]);
      }
    } catch (error) {
      console.error('Error fetching media:', error);
      toast.error('Failed to load attachments');
    }
  };

  const verifyAccessCode = async () => {
    if (!capsule) return;

    if (accessCodeInput === capsule.access_code) {
      setIsAuthenticated(true);
      fetchMedia(capsule.id);

      // Update the URL with the access code
      const url = new URL(window.location.href);
      url.searchParams.set('code', accessCodeInput);
      window.history.replaceState({}, '', url.toString());
    } else {
      toast.error('Invalid access code');
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full mb-4" />
            <Skeleton className="h-16 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!capsule) {
    return (
      <div className="container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Time Capsule Not Found</CardTitle>
            <CardDescription>
              The time capsule you're looking for doesn't exist or hasn't been delivered yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500">
              If you received a link to a time capsule, please check that the URL is correct.
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Home
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-12 px-4">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Access Protected Time Capsule</CardTitle>
            <CardDescription>
              This time capsule is protected with an access code.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-500">
              Please enter the access code that was included in the email you received.
            </p>
            <div className="flex space-x-2">
              <input
                type="text"
                value={accessCodeInput}
                onChange={(e) => setAccessCodeInput(e.target.value)}
                placeholder="Enter access code"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
              <Button onClick={verifyAccessCode}>
                Verify
              </Button>
            </div>

            <div className="mt-8 pt-6 border-t">
              <p className="text-sm text-gray-500 mb-4">
                For enhanced security and to access all your time capsules in one place, create a recipient account.
              </p>
              <Button variant="outline" asChild className="w-full">
                <Link href="/time-capsule/recipient/register">
                  Create Recipient Account
                </Link>
              </Button>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Already have an account? <Link href="/time-capsule/recipient/dashboard" className="text-blue-600 hover:underline">Sign in</Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Get recipient name
  const recipientName = capsule.recipient_first_name && capsule.recipient_last_name
    ? `${capsule.recipient_first_name} ${capsule.recipient_last_name}`
    : capsule.recipient_name || 'Recipient';

  return (
    <div className="container mx-auto py-12 px-4">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">{capsule.title}</CardTitle>
              <CardDescription>
                A time capsule delivered to you
              </CardDescription>
            </div>
            <div className="bg-blue-100 rounded-full p-3">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-2">
              <User className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">From</p>
                <p>{capsule.sender_name || 'Someone who cares about you'}</p>
              </div>
            </div>

            <div className="flex items-start space-x-2">
              <Calendar className="h-5 w-5 text-gray-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Delivered On</p>
                <p>{format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}</p>
              </div>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-medium mb-3">Message</h3>
            <div className="bg-gray-50 p-4 rounded-md whitespace-pre-wrap min-h-[100px]">
              {capsule.message || 'No message content'}
            </div>
          </div>

          {media.length > 0 && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium mb-3">Attachments</h3>
              <div className="space-y-2">
                {media.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center">
                      <File className="h-5 w-5 text-gray-400 mr-2" />
                      <div>
                        <p className="font-medium">{item.file_name}</p>
                        <p className="text-sm text-gray-500">
                          {(item.file_size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>

                    {item.signed_url && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={item.signed_url} target="_blank" rel="noopener noreferrer" download={item.file_name}>
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </a>
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between border-t pt-6">
          <p className="text-sm text-gray-500">
            This time capsule was created for you and delivered on {format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
