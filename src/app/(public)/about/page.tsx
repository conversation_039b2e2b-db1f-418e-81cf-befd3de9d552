"use client";

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft, Shield, Heart, Users, Lock } from 'lucide-react';
import MainHeader from '@/components/Navigation/MainHeader';
import PageHeading from '@/components/ui/PageHeading';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />
      <div className="max-w-5xl mx-auto px-4 py-12">
        <Button variant="outline" className="mb-6" asChild>
          <Link href="/">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
        
        <PageHeading 
          title="About Legalock" 
          description="Our mission and the team behind the platform"
        />
        
        <div className="bg-white rounded-lg shadow p-8 mb-8">
          <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
          <p className="text-gray-700 mb-6">
            Legalock was founded with a simple yet powerful mission: to help people protect and pass on their digital legacy. 
            In today's increasingly digital world, our online lives contain valuable assets, memories, and information that 
            are often overlooked in traditional estate planning.
          </p>
          
          <p className="text-gray-700 mb-6">
            We believe everyone deserves peace of mind knowing that their digital assets will be properly managed according 
            to their wishes. Our platform provides the tools and guidance needed to organize, protect, and securely transfer 
            digital legacy information to loved ones.
          </p>
          
          <div className="grid md:grid-cols-2 gap-8 my-10">
            <div className="flex flex-col items-center text-center p-6 border border-gray-100 rounded-lg shadow-sm">
              <Shield className="h-12 w-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Protection</h3>
              <p className="text-gray-600">Securing your digital assets with enterprise-grade encryption and safety measures</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 border border-gray-100 rounded-lg shadow-sm">
              <Heart className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Care</h3>
              <p className="text-gray-600">Creating thoughtful ways to share your legacy with the people you love</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 border border-gray-100 rounded-lg shadow-sm">
              <Users className="h-12 w-12 text-green-600 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Trust</h3>
              <p className="text-gray-600">Building reliable systems for trustee management and verification</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 border border-gray-100 rounded-lg shadow-sm">
              <Lock className="h-12 w-12 text-purple-600 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Privacy</h3>
              <p className="text-gray-600">Respecting your confidentiality with strict privacy controls</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-8 mb-8">
          <h2 className="text-2xl font-bold mb-4">Our Story</h2>
          <p className="text-gray-700 mb-4">
            Legalock was founded in 2023 by a team who experienced firsthand the challenges of managing a loved one's digital 
            assets after their passing. What started as a personal project to solve our own problems quickly grew into a 
            mission to help others avoid similar difficulties.
          </p>
          <p className="text-gray-700">
            After months of research and development, consulting with estate planners, digital security experts, and families 
            who had faced similar challenges, we created Legalock – a comprehensive platform for digital legacy management.
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-8">
          <h2 className="text-2xl font-bold mb-6">Meet Our Team</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <h3 className="font-semibold text-lg">Emma Wilson</h3>
              <p className="text-blue-600">CEO & Co-founder</p>
              <p className="mt-2 text-gray-600 text-sm">Former estate attorney with a passion for making legacy planning accessible to everyone.</p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <h3 className="font-semibold text-lg">David Chen</h3>
              <p className="text-blue-600">CTO & Co-founder</p>
              <p className="mt-2 text-gray-600 text-sm">Cybersecurity expert focused on building secure systems for sensitive information.</p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <h3 className="font-semibold text-lg">Olivia Martinez</h3>
              <p className="text-blue-600">Head of Product</p>
              <p className="mt-2 text-gray-600 text-sm">UX specialist dedicated to creating intuitive experiences for complex tasks.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
