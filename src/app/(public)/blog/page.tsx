"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, User, Clock, ChevronRight } from 'lucide-react';
import { Input } from '@/components/ui/input';
import MainHeader from '@/components/Navigation/MainHeader';
import PageHeading from '@/components/ui/PageHeading';

const blogPosts = [
  {
    id: 1,
    title: "Why Digital Legacy Planning Matters More Than Ever",
    excerpt: "In today's digital world, our online presence continues to grow. Learn why planning for your digital assets is just as important as traditional estate planning.",
    date: "April 2, 2025",
    author: "<PERSON>",
    readTime: "5 min read",
    category: "Digital Planning",
    image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 2,
    title: "How to Create a Comprehensive Digital Asset Inventory",
    excerpt: "A step-by-step guide to identifying and documenting all your digital assets to ensure nothing is overlooked in your estate plan.",
    date: "March 28, 2025",
    author: "<PERSON>",
    readTime: "8 min read",
    category: "<PERSON>torials",
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 3,
    title: "The Future of Digital Estate Planning: AI and Blockchain",
    excerpt: "Exploring how emerging technologies are transforming the way we approach digital legacy planning and asset protection.",
    date: "March 15, 2025",
    author: "Olivia Martinez",
    readTime: "6 min read",
    category: "Technology",
    image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 4,
    title: "Trustees: Choosing the Right People to Manage Your Digital Legacy",
    excerpt: "How to select, approach, and prepare trustees who will be responsible for your digital assets after you're gone.",
    date: "March 8, 2025",
    author: "Emma Wilson",
    readTime: "7 min read",
    category: "Planning",
    image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 5,
    title: "Digital Time Capsules: A Meaningful Way to Connect Beyond Life",
    excerpt: "How scheduled messages and media can provide comfort and guidance to loved ones after you're gone.",
    date: "February 28, 2025",
    author: "Michael Johnson",
    readTime: "4 min read",
    category: "Time Capsule",
    image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 6,
    title: "The Legal Side of Digital Assets: What You Need to Know",
    excerpt: "Understanding the legal implications of digital ownership and how to ensure your wishes are legally enforceable.",
    date: "February 15, 2025",
    author: "Sarah Lee, JD",
    readTime: "9 min read",
    category: "Legal",
    image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  }
];

const categories = [
  "All Categories",
  "Digital Planning",
  "Tutorials",
  "Technology",
  "Planning",
  "Time Capsule",
  "Legal",
  "Security"
];

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />
      <div className="max-w-6xl mx-auto px-4 py-12">
        <Button variant="outline" className="mb-6" asChild>
          <Link href="/">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
        
        <PageHeading 
          title="Legalock Blog" 
          description="Insights, guides, and news about digital legacy planning"
        />
        
        <div className="grid md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            {/* Featured Post */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
              <div className="h-64 overflow-hidden">
                <Image 
                  src={blogPosts[0].image} 
                  alt={blogPosts[0].title}
                  className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-500"
                  width={800}
                  height={400}
                  priority
                />
              </div>
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                    {blogPosts[0].category}
                  </span>
                  <span className="mx-2">•</span>
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>{blogPosts[0].date}</span>
                  <span className="mx-2">•</span>
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{blogPosts[0].readTime}</span>
                </div>
                
                <h2 className="text-2xl font-bold mb-2">
                  <Link href={`/blog/${blogPosts[0].id}`} className="hover:text-blue-600 transition-colors">
                    {blogPosts[0].title}
                  </Link>
                </h2>
                
                <p className="text-gray-600 mb-4">
                  {blogPosts[0].excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-200 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-700">By {blogPosts[0].author}</span>
                  </div>
                  
                  <Button variant="link" size="sm" className="text-blue-600" asChild>
                    <Link href={`/blog/${blogPosts[0].id}`}>
                      Read More <ChevronRight className="ml-1 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Regular Posts */}
            <div className="grid gap-8 sm:grid-cols-2">
              {blogPosts.slice(1).map((post) => (
                <div key={post.id} className="bg-white rounded-lg shadow overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-48 overflow-hidden">
                    <Image 
                      src={post.image} 
                      alt={post.title}
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-500"
                      width={400}
                      height={200}
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center text-xs text-gray-500 mb-2">
                      <span className="bg-blue-50 text-blue-700 px-2 py-0.5 rounded">
                        {post.category}
                      </span>
                      <span className="mx-2">•</span>
                      <span>{post.date}</span>
                    </div>
                    
                    <h3 className="text-lg font-bold mb-2">
                      <Link href={`/blog/${post.id}`} className="hover:text-blue-600 transition-colors">
                        {post.title}
                      </Link>
                    </h3>
                    
                    <p className="text-gray-600 mb-4 text-sm">
                      {post.excerpt.length > 100 ? post.excerpt.substring(0, 100) + '...' : post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">{post.readTime}</span>
                      <Button variant="link" size="sm" className="text-blue-600 p-0" asChild>
                        <Link href={`/blog/${post.id}`}>
                          Read More
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-8 text-center">
              <Button variant="outline">Load More Articles</Button>
            </div>
          </div>
          
          <div className="space-y-8">
            {/* Search */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Search Articles</h3>
              <div className="relative">
                <Input placeholder="Search..." />
                <Button className="absolute right-0 top-0 rounded-l-none">
                  Search
                </Button>
              </div>
            </div>
            
            {/* Categories */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Categories</h3>
              <ul className="space-y-2">
                {categories.map((category, index) => (
                  <li key={index}>
                    <Link 
                      href={`/blog/category/${category.toLowerCase().replace(/ /g, '-')}`}
                      className="text-gray-700 hover:text-blue-600 flex items-center"
                    >
                      <ChevronRight className="h-4 w-4 mr-2 text-blue-500" />
                      {category}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Popular Articles */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Popular Articles</h3>
              <ul className="space-y-4">
                {blogPosts.slice(0, 3).map((post) => (
                  <li key={post.id} className="flex space-x-3">
                    <div className="w-20 h-16 flex-shrink-0 rounded overflow-hidden">
                      <Image 
                        src={post.image}
                        alt={post.title}
                        className="w-full h-full object-cover"
                        width={80}
                        height={64}
                      />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">
                        <Link href={`/blog/${post.id}`} className="hover:text-blue-600">
                          {post.title}
                        </Link>
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">{post.date}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Newsletter */}
            <div className="bg-blue-50 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-2">Subscribe to our Newsletter</h3>
              <p className="text-gray-600 text-sm mb-4">
                Get the latest articles and resources straight to your inbox.
              </p>
              <Input placeholder="Your email address" className="mb-3" />
              <Button className="w-full">Subscribe</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
