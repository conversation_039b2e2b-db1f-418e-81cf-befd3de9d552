"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import {
  Users,
  Package,
  FileText,
  Phone,
  Power,
  ArrowRight,
  AlertTriangle,
  Heart,
  HeartPulse
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import PageHeading from '@/components/ui/PageHeading';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  Al<PERSON><PERSON><PERSON>ogT<PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Grantor {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  relationship: string;
  permissions: string[];
  status: string;
}

export default function TrusteeDashboardPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [grantors, setGrantors] = useState<Grantor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedGrantor, setSelectedGrantor] = useState<Grantor | null>(null);
  const [isReportingDeath, setIsReportingDeath] = useState(false);

  // Redirect to the not-a-trustee page if the user has no trustee relationships
  useEffect(() => {
    if (!isLoading && grantors.length === 0) {
      // We don't immediately redirect to allow users to see the empty state
      // and click the "Learn More" button themselves
    }
  }, [isLoading, grantors, router]);

  const fetchGrantors = useCallback(async () => {
    if (!user?.id) return;
    try {
      setIsLoading(true);

      // Fetch all trustees where the current user is the trustee
      const { data: trusteeData, error: trusteeError } = await supabase
        .from('trustees')
        .select('*, user_id')
        .eq('trustee_user_id', user?.id)
        .eq('status', 'active');

      if (trusteeError) throw trusteeError;

      if (!trusteeData || trusteeData.length === 0) {
        setGrantors([]);
        return;
      }

      // Fetch the grantor profiles
      const grantorIds = trusteeData.map(t => t.user_id);
      const { data: grantorProfiles, error: grantorError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, email')
        .in('id', grantorIds);

      if (grantorError) throw grantorError;

      // Combine the data
      const combinedData = trusteeData.map(trustee => {
        const profile = grantorProfiles?.find(p => p.id === trustee.user_id);
        return {
          id: trustee.user_id,
          first_name: profile?.first_name || '',
          last_name: profile?.last_name || '',
          email: profile?.email || '',
          relationship: trustee.relationship,
          permissions: trustee.permissions,
          status: 'alive', // Default status
        };
      });

      // Check for death notifications
      const { data: deathNotifications, error: deathError } = await supabase
        .from('death_notifications')
        .select('user_id, status')
        .in('user_id', grantorIds)
        .in('status', ['pending', 'verified']);

      if (deathError) throw deathError;

      // Update status based on death notifications
      if (deathNotifications && deathNotifications.length > 0) {
        deathNotifications.forEach(notification => {
          const grantorIndex = combinedData.findIndex(g => g.id === notification.user_id);
          if (grantorIndex >= 0) {
            combinedData[grantorIndex].status = notification.status === 'verified' ? 'deceased' : 'pending';
          }
        });
      }

      setGrantors(combinedData);
    } catch (error: any) {
      console.error('Error fetching grantors:', error);
      toast.error('Failed to load your trustee relationships');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, setGrantors, setIsLoading]);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user) {
      fetchGrantors();
    }
  }, [user, loading, router, fetchGrantors]);

  const handleReportDeath = async () => {
    try {
      if (!selectedGrantor) return;

      setIsReportingDeath(true);

      // Check if there's already a death notification
      const { data: existingNotification, error: checkError } = await supabase
        .from('death_notifications')
        .select('id, status')
        .eq('user_id', selectedGrantor.id)
        .single();

      if (existingNotification) {
        if (existingNotification.status === 'verified') {
          toast.error('Death has already been verified');
          return;
        }

        // Update existing notification
        const { error: updateError } = await supabase
          .from('death_notifications')
          .update({
            reported_by: user?.id,
            reported_at: new Date().toISOString(),
          })
          .eq('id', existingNotification.id);

        if (updateError) throw updateError;
      } else {
        // Create new death notification
        const { error: createError } = await supabase
          .from('death_notifications')
          .insert({
            user_id: selectedGrantor.id,
            reported_by: user?.id,
            status: 'pending',
            reported_at: new Date().toISOString(),
          });

        if (createError) throw createError;
      }

      toast.success('Death reported. Our team will verify this information.');
      setSelectedGrantor(null);
      fetchGrantors();
    } catch (error: any) {
      console.error('Error reporting death:', error);
      toast.error(error.message || 'Failed to report death');
    } finally {
      setIsReportingDeath(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'alive':
        return <span className="flex items-center text-green-600 text-sm font-medium">
          <HeartPulse className="h-4 w-4 mr-1" /> Active
        </span>;
      case 'pending':
        return <span className="flex items-center text-amber-600 text-sm font-medium">
          <AlertTriangle className="h-4 w-4 mr-1" /> Death Reported (Pending Verification)
        </span>;
      case 'deceased':
        return <span className="flex items-center text-gray-600 text-sm font-medium">
          <Heart className="h-4 w-4 mr-1" /> Deceased
        </span>;
      default:
        return <span>{status}</span>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 text-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-500">Loading trustee dashboard...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <PageHeading
        title="Trustee Dashboard"
        description="Manage your responsibilities as a trustee"
        icon={<Users className="h-6 w-6" />}
      />

      {isLoading ? (
        <div className="text-center py-10">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-500">Loading your trustee relationships...</p>
        </div>
      ) : grantors.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
          <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No trustee relationships found</h3>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">
            You are not currently a trustee for anyone. If someone has invited you to be their trustee, please check your email for an invitation.
          </p>
          <Button asChild>
            <Link href="/trustee/not-a-trustee">
              Learn More About Being a Trustee
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      ) : (
        <div className="space-y-8 mt-8">
          {grantors.map((grantor) => (
            <Card key={grantor.id} className="overflow-hidden">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">
                      {grantor.first_name} {grantor.last_name}
                    </CardTitle>
                    <CardDescription>
                      {grantor.relationship} • {grantor.email}
                    </CardDescription>
                  </div>
                  {getStatusBadge(grantor.status)}
                </div>
              </CardHeader>
              <CardContent>
                {grantor.status === 'alive' ? (
                  <div className="bg-blue-50 p-4 rounded-md mb-6">
                    <p className="text-blue-800 text-sm">
                      You are a trustee for {grantor.first_name}. You will be able to access their digital legacy after their passing.
                    </p>
                  </div>
                ) : grantor.status === 'pending' ? (
                  <div className="bg-amber-50 p-4 rounded-md mb-6">
                    <p className="text-amber-800 text-sm">
                      You have reported {grantor.first_name}'s passing. Our team is verifying this information. You will be notified when verification is complete.
                    </p>
                  </div>
                ) : (
                  <div className="bg-gray-100 p-4 rounded-md mb-6">
                    <p className="text-gray-800 text-sm">
                      {grantor.first_name}'s passing has been verified. You now have access to their digital legacy as specified below.
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {grantor.permissions.includes('assets') && (
                    <Card className={`border ${grantor.status === 'deceased' ? 'border-primary' : 'border-gray-200'}`}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center">
                          <Package className="h-4 w-4 mr-2" />
                          Assets
                        </CardTitle>
                      </CardHeader>
                      <CardFooter className="pt-2">
                        <Button
                          asChild
                          variant={grantor.status === 'deceased' ? "default" : "outline"}
                          disabled={grantor.status !== 'deceased'}
                          className="w-full"
                        >
                          <Link href={`/trustee/${grantor.id}/assets`}>
                            View Assets
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  )}

                  {grantor.permissions.includes('vault') && (
                    <Card className={`border ${grantor.status === 'deceased' ? 'border-primary' : 'border-gray-200'}`}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center">
                          <FileText className="h-4 w-4 mr-2" />
                          Digital Vault
                        </CardTitle>
                      </CardHeader>
                      <CardFooter className="pt-2">
                        <Button
                          asChild
                          variant={grantor.status === 'deceased' ? "default" : "outline"}
                          disabled={grantor.status !== 'deceased'}
                          className="w-full"
                        >
                          <Link href={`/trustee/${grantor.id}/vault`}>
                            View Documents
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  )}

                  {grantor.permissions.includes('contacts') && (
                    <Card className={`border ${grantor.status === 'deceased' ? 'border-primary' : 'border-gray-200'}`}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center">
                          <Phone className="h-4 w-4 mr-2" />
                          Contacts
                        </CardTitle>
                      </CardHeader>
                      <CardFooter className="pt-2">
                        <Button
                          asChild
                          variant={grantor.status === 'deceased' ? "default" : "outline"}
                          disabled={grantor.status !== 'deceased'}
                          className="w-full"
                        >
                          <Link href={`/trustee/${grantor.id}/contacts`}>
                            View Contacts
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  )}

                  {grantor.permissions.includes('services') && (
                    <Card className={`border ${grantor.status === 'deceased' ? 'border-primary' : 'border-gray-200'}`}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center">
                          <Power className="h-4 w-4 mr-2" />
                          Service Sunset
                        </CardTitle>
                      </CardHeader>
                      <CardFooter className="pt-2">
                        <Button
                          asChild
                          variant={grantor.status === 'deceased' ? "default" : "outline"}
                          disabled={grantor.status !== 'deceased'}
                          className="w-full"
                        >
                          <Link href={`/trustee/${grantor.id}/services`}>
                            View Services
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  )}

                  <Card className={`border ${grantor.status === 'deceased' ? 'border-primary' : 'border-gray-200'}`}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Heart className="h-4 w-4 mr-2" />
                        Last Wishes
                      </CardTitle>
                    </CardHeader>
                    <CardFooter className="pt-2">
                      <Button
                        asChild
                        variant={grantor.status === 'deceased' ? "default" : "outline"}
                        disabled={grantor.status !== 'deceased'}
                        className="w-full"
                      >
                        <Link href={`/trustee/${grantor.id}/last-wishes`}>
                          View Last Wishes
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                {grantor.status === 'alive' && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" className="text-red-600 border-red-200">
                        <AlertTriangle className="mr-2 h-4 w-4" />
                        Report Death
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Report Death</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to report the death of {grantor.first_name} {grantor.last_name}? This will initiate the process of granting you access to their digital legacy.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => {
                            setSelectedGrantor(grantor);
                            handleReportDeath();
                          }}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          {isReportingDeath ? 'Processing...' : 'Report Death'}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
