"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { ServiceSunset } from '@/types/database.types';
import {
  SERVICE_CATEGORIES,
  SERVICE_PRIORITIES
} from '@/types/service-sunset.types';
import {
  Power,
  Search,
  Globe,
  Calendar,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Info,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { format } from 'date-fns';
import Link from 'next/link';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function TrusteeServicesPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.userId as string;
  const { user, loading } = useAuth();

  const [services, setServices] = useState<(ServiceSunset & { completed: boolean })[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [userName, setUserName] = useState('');
  const [hasAccess, setHasAccess] = useState(false);
  const [completedServices, setCompletedServices] = useState<Record<string, boolean>>({});

  const fetchServices = useCallback(async () => {
    try {
      setIsLoading(true);

      let query = supabase
        .from('service_sunset')
        .select('*')
        .eq('user_id', userId);

      if (searchQuery) {
        query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }

      if (selectedCategory && selectedCategory !== 'all') {
        query = query.eq('category', selectedCategory);
      }

      if (selectedPriority && selectedPriority !== 'all') {
        query = query.eq('priority', selectedPriority);
      }

      if (activeTab === 'auto_renewal') {
        query = query.eq('auto_renewal', true);
      } else if (activeTab === 'completed') {
        // Filter for completed services
      } else if (activeTab === 'pending') {
        // Filter for pending services
      }

      const { data, error } = await query.order('priority');

      if (error) throw error;

      const servicesWithCompletion = (data || []).map(service => ({
        ...service,
        completed: completedServices[service.id] || false,
      }));

      setServices(servicesWithCompletion);
    } catch (error: any) {
      console.error('Error fetching services:', error);
      toast.error(`Failed to load services: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [userId, searchQuery, selectedCategory, selectedPriority, activeTab, completedServices]);

  const checkAccess = useCallback(async () => {
    const fetchUserInfo = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('first_name, last_name')
          .eq('id', userId)
          .single();

        if (error) throw error;

        if (data) {
          setUserName(`${data.first_name} ${data.last_name}`);
        }
      } catch (error) {
        console.error('Error fetching user info:', error);
      }
    };

    const fetchCompletedServices = async () => {
      try {
        const savedCompletedServices = localStorage.getItem(`completed_services_${userId}`);
        if (savedCompletedServices) {
          setCompletedServices(JSON.parse(savedCompletedServices));
        }
      } catch (error) {
        console.error('Error fetching completed services:', error);
      }
    };

    try {
      // Check if the current user is a trustee for the specified user
      const { data: trusteeData, error: trusteeError } = await supabase
        .from('trustees')
        .select('*')
        .eq('user_id', userId)
        .eq('trustee_user_id', user?.id)
        .eq('status', 'active')
        .single();

      if (trusteeError || !trusteeData) {
        toast.error('You do not have access to this user\'s services');
        router.push('/trustee/dashboard');
        return;
      }

      // Check if the permissions include services
      if (!trusteeData.permissions.includes('services')) {
        toast.error('You do not have permission to view this user\'s services');
        router.push('/trustee/dashboard');
        return;
      }

      // Check if there's a verified death notification
      const { data: deathData, error: deathError } = await supabase
        .from('death_notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'verified')
        .single();

      if (deathError || !deathData) {
        toast.error('You cannot access this information until the user\'s death has been verified');
        router.push('/trustee/dashboard');
        return;
      }

      setHasAccess(true);
      await fetchUserInfo();
      await fetchServices();
      await fetchCompletedServices();
    } catch (error: any) {
      console.error('Error checking access:', error);
      toast.error('Failed to verify access permissions');
      router.push('/trustee/dashboard');
    }
  }, [userId, user?.id, router, fetchServices]);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user && userId) {
      checkAccess();
    }
  }, [user, loading, userId, router, checkAccess]);

  useEffect(() => {
    if (hasAccess && userId) {
      fetchServices();
    }
  }, [userId, searchQuery, selectedCategory, selectedPriority, activeTab, hasAccess, fetchServices]);

  const fetchUserInfo = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('first_name, last_name')
        .eq('id', userId)
        .single();

      if (error) throw error;

      if (data) {
        setUserName(`${data.first_name} ${data.last_name}`);
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  const fetchCompletedServices = async () => {
    try {
      const savedCompletedServices = localStorage.getItem(`completed_services_${userId}`);
      if (savedCompletedServices) {
        setCompletedServices(JSON.parse(savedCompletedServices));
      }
    } catch (error) {
      console.error('Error fetching completed services:', error);
    }
  };

  const handleToggleCompleted = (serviceId: string, completed: boolean) => {
    setServices(services.map(service =>
      service.id === serviceId ? { ...service, completed } : service
    ));

    const updatedCompletedServices = {
      ...completedServices,
      [serviceId]: completed,
    };

    setCompletedServices(updatedCompletedServices);
    localStorage.setItem(`completed_services_${userId}`, JSON.stringify(updatedCompletedServices));

    if (completed) {
      toast.success('Service marked as canceled');
    } else {
      toast.info('Service marked as pending');
    }
  };

  const getCategoryLabel = (categoryValue: string) => {
    const category = SERVICE_CATEGORIES.find(c => c.value === categoryValue);
    return category ? category.label : categoryValue;
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" /> High
        </Badge>;
      case 'medium':
        return <Badge variant="default" className="flex items-center gap-1 bg-amber-500">
          <Info className="h-3 w-3" /> Medium
        </Badge>;
      case 'low':
        return <Badge variant="outline" className="flex items-center gap-1 text-green-600 border-green-200 bg-green-50">
          <CheckCircle className="h-3 w-3" /> Low
        </Badge>;
      default:
        return <Badge>{priority}</Badge>;
    }
  };

  if (loading || !hasAccess) {
    return (
      <div className="container mx-auto py-8 text-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-500">Checking access...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="outline" asChild className="mb-6">
          <Link href="/trustee/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Trustee Dashboard
          </Link>
        </Button>

        <h1 className="text-3xl font-bold mb-2">Service Sunset List</h1>
        <p className="text-gray-600 mb-6">
          Services that need to be canceled for {userName}
        </p>

        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-800">Trustee Instructions</h3>
              <p className="text-sm text-blue-700 mt-1">
                As a trustee, you are responsible for canceling these services on behalf of {userName}.
                Please follow the cancellation instructions for each service and mark them as completed when done.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="all">All Services</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="auto_renewal">Auto-Renewal</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative w-full md:w-1/2">
            <Input
              type="text"
              placeholder="Search services..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {SERVICE_CATEGORIES.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedPriority} onValueChange={setSelectedPriority}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="All Priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              {SERVICE_PRIORITIES.map((priority) => (
                <SelectItem key={priority.value} value={priority.value}>
                  {priority.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-10">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-500">Loading services...</p>
        </div>
      ) : services.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
          <Power className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No services found</h3>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">
            There are no services to cancel for this user based on your current filters.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services
            .filter(service => {
              if (activeTab === 'pending') return !service.completed;
              if (activeTab === 'completed') return service.completed;
              if (activeTab === 'auto_renewal') return service.auto_renewal;
              return true;
            })
            .map((service) => (
              <Card
                key={service.id}
                className={`overflow-hidden hover:shadow-md transition-shadow ${
                  service.completed ? 'bg-gray-50 border-gray-200' : ''
                }`}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className={`text-lg ${service.completed ? 'text-gray-500' : ''}`}>
                      {service.name}
                    </CardTitle>
                    {getPriorityBadge(service.priority)}
                  </div>
                  <CardDescription>
                    {getCategoryLabel(service.category)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {service.description && (
                      <p className={`text-sm ${service.completed ? 'text-gray-500' : 'text-gray-600'}`}>
                        {service.description}
                      </p>
                    )}

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {service.website && (
                        <div className="flex items-center">
                          <Globe className="h-4 w-4 text-gray-500 mr-2" />
                          <a
                            href={service.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline truncate max-w-[150px] flex items-center"
                          >
                            Website
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </a>
                        </div>
                      )}

                      {service.renewal_date && (
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                          <span className={service.completed ? 'text-gray-500' : 'text-gray-700'}>
                            {format(new Date(service.renewal_date), 'MMM d, yyyy')}
                          </span>
                        </div>
                      )}

                      {service.cost_per_period && (
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 text-gray-500 mr-2" />
                          <span className={service.completed ? 'text-gray-500' : 'text-gray-700'}>
                            ${service.cost_per_period}{service.period ? `/${service.period.charAt(0)}` : ''}
                          </span>
                        </div>
                      )}
                    </div>

                    {service.auto_renewal && (
                      <div className="bg-amber-50 text-amber-800 text-xs p-2 rounded-md flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Auto-renewal enabled
                      </div>
                    )}

                    {service.cancellation_instructions && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Cancellation Instructions:</h4>
                        <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-200">
                          {service.cancellation_instructions}
                        </p>
                      </div>
                    )}

                    <div className="flex items-center space-x-2 pt-2">
                      <Checkbox
                        id={`completed-${service.id}`}
                        checked={service.completed}
                        onCheckedChange={(checked) =>
                          handleToggleCompleted(service.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`completed-${service.id}`}
                        className="text-sm cursor-pointer"
                      >
                        Mark as canceled
                      </Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      )}
    </div>
  );
}
