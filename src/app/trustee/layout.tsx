"use client";

import React from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import Header from '@/components/Navigation/Header';
import { useAuth } from '@/context/auth-context';
import { Loader2 } from 'lucide-react';

export default function TrusteeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { loading } = useAuth();
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
  const isAcceptPage = pathname.includes('/trustee/accept');

  if (loading && !isAcceptPage) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // For the accept page, we'll let the page component handle its own layout
  // This prevents the header from showing twice
  if (isAcceptPage) {
    return <>{children}</>;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <div className="flex-1 flex flex-col">
          <Header />
          <main className="flex-1 bg-gray-50">
            <div className="page-container">
              {children}
            </div>
          </main>
          <footer className="py-4 px-6 border-t border-gray-200 text-center text-sm text-gray-500">
            <p>Legalock © {new Date().getFullYear()} - Your digital legacy secured</p>
          </footer>
        </div>
      </div>
    </SidebarProvider>
  );
}
