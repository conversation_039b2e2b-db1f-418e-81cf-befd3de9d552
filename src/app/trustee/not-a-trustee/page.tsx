"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Users, Mail, Info, ArrowRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import PageHeading from '@/components/ui/PageHeading';

export default function NotATrusteePage() {
  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" asChild className="mb-6">
        <Link href="/dashboard">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Link>
      </Button>

      <PageHeading
        title="Trustee Dashboard"
        description="You are not currently a trustee for anyone"
        icon={<Users className="h-6 w-6" />}
      />

      <div className="max-w-3xl mx-auto mt-8">
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-blue-800">
              <Info className="h-5 w-5 mr-2 text-blue-600" />
              What is a Trustee?
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700">
            <p>
              A trustee is someone who has been designated by another Legalock user to manage their digital legacy after their passing. 
              Trustees are given specific permissions to access assets, documents, and other important information.
            </p>
          </CardContent>
        </Card>

        <div className="mt-8 space-y-6">
          <h3 className="text-xl font-semibold">How to Become a Trustee</h3>
          
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-primary" />
                  Receive an Invitation
                </CardTitle>
                <CardDescription>
                  Someone must invite you to be their trustee
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  To become a trustee, another Legalock user must invite you through their Trustees management page. 
                  You'll receive an email invitation with instructions on how to accept the role.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Users className="h-5 w-5 mr-2 text-primary" />
                  Invite Others
                </CardTitle>
                <CardDescription>
                  You can invite others to be your trustees
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  While you wait to be invited as a trustee, you can set up your own trustees who will manage your digital legacy.
                </p>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full">
                  <Link href="/trustees" className="flex items-center justify-center">
                    Manage Your Trustees
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>

          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Check Your Email</CardTitle>
              <CardDescription>
                If someone has invited you to be their trustee, check your email for an invitation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Trustee invitations are sent via email. If you've been invited to be a trustee, check your inbox (and spam folder) for an invitation from Legalock. 
                The email will contain a link to accept the invitation and set up your trustee access.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
