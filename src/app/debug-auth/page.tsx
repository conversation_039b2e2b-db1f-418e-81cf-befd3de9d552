"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import Link from 'next/link';

export default function DebugAuthPage() {
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [testType, setTestType] = useState('');

  const testEnvironment = async () => {
    setLoading(true);
    setTestType('environment');
    try {
      const response = await fetch('/api/test-env');
      const data = await response.json();
      setTestResults(data);
      toast.success('Environment test completed');
    } catch (error: any) {
      console.error('Test error:', error);
      toast.error(`Test failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testDatabase = async () => {
    setLoading(true);
    setTestType('database');
    try {
      // Create a test API endpoint to test database connectivity
      const response = await fetch('/api/test-db', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      setTestResults(data);

      if (response.ok) {
        toast.success('Database connection test successful');
      } else {
        toast.error(`Database test failed: ${data.error || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('Database test error:', error);
      setTestResults({
        success: false,
        error: error.message
      });
      toast.error(`Database test failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testSignIn = async () => {
    if (!email || !password) {
      toast.error('Please enter email and password');
      return;
    }

    setLoading(true);
    setTestType('signin');
    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      setTestResults({
        signInTest: {
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          data
        }
      });

      if (!response.ok) {
        toast.error(`Sign in test failed: ${data.error || `Error ${response.status}: ${response.statusText}`}`);
      } else {
        toast.success('Sign in test successful');
      }
    } catch (error: any) {
      console.error('Sign in test error:', error);
      setTestResults({
        signInTest: {
          success: false,
          error: error.message
        }
      });
      toast.error(`Sign in test failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Auth Debugging Page</h1>
        <Button variant="outline" asChild>
          <Link href="/">Back to Home</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Environment Test</CardTitle>
            <CardDescription>Test environment variables and configuration</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={testEnvironment}
              disabled={loading && testType === 'environment'}
              className="w-full"
            >
              {loading && testType === 'environment' ? 'Testing...' : 'Test Environment'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Database Test</CardTitle>
            <CardDescription>Test database connectivity</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={testDatabase}
              disabled={loading && testType === 'database'}
              className="w-full"
            >
              {loading && testType === 'database' ? 'Testing...' : 'Test Database'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sign In Test</CardTitle>
            <CardDescription>Test the sign in functionality</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
              />
            </div>
            <Button
              onClick={testSignIn}
              disabled={(loading && testType === 'signin') || !email || !password}
              className="w-full"
            >
              {loading && testType === 'signin' ? 'Testing...' : 'Test Sign In'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {testResults && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
