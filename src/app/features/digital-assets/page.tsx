import React from 'react';
import { metadata } from './metadata';
import Image from 'next/image';
import Link from 'next/link';
import { Database, Shield, CheckCircle, Users, ArrowRight } from 'lucide-react';
import FeaturePageLayout from '@/components/Layout/FeaturePageLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function DigitalAssetsPage() {
  return (
    <FeaturePageLayout
      title="Digital Asset Inventory"
      description="Create a comprehensive catalog of your digital and physical assets with secure access instructions for your trustees."
      iconName="Database"
      iconColor="text-blue-100"
      iconBgColor="bg-gradient-to-r from-blue-600 to-blue-800"
    >
      <div className="max-w-4xl mx-auto">
        {/* Overview Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Why Document Your Assets?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <p className="text-lg text-gray-700 mb-4">
                In today's digital world, we accumulate countless online accounts, digital subscriptions, and virtual assets alongside our physical possessions. Without proper documentation, these assets can be lost forever when you pass away.
              </p>
              <p className="text-lg text-gray-700 mb-4">
                Legalock's Digital Asset Inventory provides a secure, organized way to catalog all your assets and ensure they're properly managed according to your wishes.
              </p>
            </div>
            <div className="bg-gray-100 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Did You Know?</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">The average person has over 90 online accounts</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Digital assets worth $25 billion are lost annually due to inadequate estate planning</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Only 13% of people have made provisions for their digital assets in their will</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                  <Database className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Document Your Assets</CardTitle>
                <CardDescription>Create a comprehensive inventory</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Easily catalog your digital accounts, financial assets, physical possessions, and more. Add details like account numbers, passwords hints, and access instructions.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Assign Trustees</CardTitle>
                <CardDescription>Designate trusted individuals</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Select trusted individuals to manage your assets after your passing. Specify which assets each trustee can access and provide specific instructions.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Secure Access</CardTitle>
                <CardDescription>Protected until needed</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">
                  Your asset inventory remains securely encrypted and private during your lifetime. Trustees gain access only after your passing has been verified.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Trustee Role Section */}
        <div className="mb-16 bg-gray-50 p-8 rounded-xl border border-gray-200">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">The Trustee's Role</h2>
          <div className="space-y-6">
            <p className="text-lg text-gray-700">
              When you designate someone as a trustee for your digital assets, you're entrusting them with important responsibilities. Here's what your trustees will do:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Before Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Receive notification of their designation as a trustee</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Accept the trustee role and responsibilities</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Have no access to your asset information</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">After Your Passing</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Report your passing through the Legalock platform</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Gain access to your asset inventory after verification</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Manage your assets according to your documented wishes</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Transfer digital assets to designated beneficiaries</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Close or memorialize accounts as specified</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Comprehensive Asset Categories</h3>
                <p className="text-gray-700">
                  Document digital accounts, financial assets, physical possessions, collectibles, and more in one secure location.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Visual Asset Distribution</h3>
                <p className="text-gray-700">
                  View your asset distribution with an interactive pie chart, helping you understand your legacy at a glance.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Secure Password Hints</h3>
                <p className="text-gray-700">
                  Store password hints that are secure yet helpful for trustees, without compromising your current security.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Beneficiary Designation</h3>
                <p className="text-gray-700">
                  Specify who should receive each asset, providing clear instructions for your trustees to follow.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Start Documenting Your Assets Today</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            Don't leave your digital legacy to chance. Create your asset inventory now and ensure your wishes are honored.
          </p>
          <Button size="lg" asChild>
            <Link href="/register">
              Get Started for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </FeaturePageLayout>
  );
}
