import type { Metadata } from 'next';

export const metadata: Metadata = {
  metadataBase: new URL('https://legalock.com'),
  title: 'Legalock - Secure Your Digital Legacy & Estate Planning',
  description: '<PERSON><PERSON> helps you organize, protect, and pass on your digital assets and final wishes to your loved ones. Comprehensive digital legacy planning solution.',
  keywords: ['digital legacy', 'estate planning', 'digital assets', 'will planning', 'legacy guardian', 'trustee management', 'digital vault', 'secure documents'],
  authors: [{ name: '<PERSON><PERSON>' }],
  creator: '<PERSON><PERSON>',
  publisher: 'Legalock',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://legalock.com',
    siteName: 'Legalock',
    title: 'Legalock - Secure Your Digital Legacy & Estate Planning',
    description: 'Organize, protect, and pass on your digital assets and final wishes to your loved ones with <PERSON><PERSON>.',
    images: [
      {
        url: '/images/Legalock-og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Legalock - Digital Legacy Planning',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Legalock - Secure Your Digital Legacy',
    description: 'Organize, protect, and pass on your digital assets and final wishes to your loved ones.',
    images: ['/images/Legalock-og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};
