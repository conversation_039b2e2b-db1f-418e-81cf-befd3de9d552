import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://legalock.com';
  
  // Define all static routes
  const routes = [
    '',
    '/features/digital-assets',
    '/features/digital-vault',
    '/features/trustee-management',
    '/features/emergency-contacts',
    '/features/service-sunset',
    '/features/last-wishes',
    '/pricing',
    '/terms',
    '/privacy',
    '/security',
    '/blog',
    '/help',
    '/contact',
    '/register',
    '/login',
  ];

  // Create sitemap entries with last modified date and priority
  const sitemap = routes.map((route) => {
    // Higher priority for homepage and key feature pages
    let priority = 0.7;
    if (route === '') {
      priority = 1.0;
    } else if (route.startsWith('/features/')) {
      priority = 0.8;
    } else if (route === '/pricing' || route === '/register') {
      priority = 0.9;
    }

    return {
      url: `${baseUrl}${route}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority,
    };
  });

  return sitemap;
}
