"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';

import { Contact } from '@/types/database.types';
import { UserPlus, Mail, Phone, Pencil, Trash2, Users, ArrowLeft, PlusCircle } from 'lucide-react';
import { formatPhoneNumber } from '@/lib/phone-utils';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import PageHeading from '@/components/ui/PageHeading';
import { toast } from 'sonner';
import ContactForm from '@/components/Contacts/ContactForm';
import MainLayout from '@/components/Layout/MainLayout';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function ContactsPage() {
  const { user } = useAuth();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  useEffect(() => {
    if (user) {
      fetchContacts();
    }
  }, [user]);

  const fetchContacts = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/contacts');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch contacts');
      }

      const data = await response.json();
      setContacts(data as Contact[]);
    } catch (error: any) {
      console.error('Error fetching contacts:', error);
      toast.error(`Failed to load contacts: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddContact = async (contactData: any) => {
    try {
      if (!user) {
        toast.error('You must be logged in to add contacts');
        return;
      }

      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add contact');
      }

      const data = await response.json();

      toast.success('Contact added successfully');
      setContacts([...contacts, data as Contact]);
      setIsFormOpen(false);
    } catch (error: any) {
      console.error('Error adding contact:', error);
      toast.error(`Failed to add contact: ${error.message}`);
    }
  };

  const handleUpdateContact = async (contactData: any) => {
    try {
      if (!editingContact) return;

      const response = await fetch('/api/contacts', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingContact.id,
          ...contactData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contact');
      }

      const data = await response.json();

      toast.success('Contact updated successfully');
      setContacts(contacts.map(c => c.id === editingContact.id ? data as Contact : c));
      setIsEditDialogOpen(false);
      setEditingContact(null);
    } catch (error: any) {
      console.error('Error updating contact:', error);
      toast.error(`Failed to update contact: ${error.message}`);
    }
  };

  const handleDeleteContact = async () => {
    if (!selectedContact) return;

    try {
      const response = await fetch(`/api/contacts?id=${selectedContact.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete contact');
      }

      toast.success('Contact deleted successfully');
      setContacts(contacts.filter(c => c.id !== selectedContact.id));
      setIsDeleteDialogOpen(false);
      setSelectedContact(null);
    } catch (error: any) {
      console.error('Error deleting contact:', error);
      toast.error(`Failed to delete contact: ${error.message}`);
    }
  };

  const handleCreateContact = () => {
    setEditingContact(null);
    setIsFormOpen(true);
  };

  const handleEditContact = (contact: Contact) => {
    setEditingContact(contact);
    setIsEditDialogOpen(true);
  };

  const confirmDeleteContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDeleteDialogOpen(true);
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4 md:px-6">
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <PageHeading
          title="Emergency Contacts"
          description="Manage the people your trustees will contact after your passing. These contacts will be shared with your trustees when needed."
          actions={
            contacts.length > 0 && (
              <Button onClick={handleCreateContact}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Contact
              </Button>
            )
          }
        />

        <div className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                Your Contacts
                <span className="ml-2 text-sm font-normal text-gray-500">
                  ({contacts.length} {contacts.length === 1 ? 'person' : 'people'})
                </span>
              </h3>
              {isLoading ? (
                // Loading skeletons
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-1/3" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                      <Skeleton className="h-8 w-16" />
                    </div>
                  ))}
                </div>
              ) : contacts.length === 0 ? (
                // Empty state
                <div className="text-center py-12">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h4 className="mt-4 text-lg font-medium text-gray-900">No Contacts Yet</h4>
                  <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                    Add people who should be contacted by your trustees after your passing. Either an email or phone number is required.
                  </p>
                  <Button onClick={handleCreateContact} className="mt-4">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Your First Contact
                  </Button>
                </div>
              ) : (
                // Contact list
                <div className="space-y-4">
                  {contacts.map((contact) => {
                    // Get display name (handle both new and old format)
                    const displayName = contact.first_name && contact.last_name
                      ? `${contact.first_name} ${contact.last_name}`
                      : contact.name || 'Unknown';

                    // Get initials for avatar
                    const initials = contact.first_name && contact.last_name
                      ? `${contact.first_name[0]}${contact.last_name[0]}`.toUpperCase()
                      : (contact.name ? contact.name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U');

                    return (
                      <div
                        key={contact.id}
                        className="flex items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        {/* Contact Avatar */}
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0 text-blue-700 font-medium">
                          {initials}
                        </div>

                        {/* Contact Details */}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 truncate">{displayName}</h4>
                          <div className="flex flex-col text-sm text-gray-500 mt-1">
                            {contact.email && (
                              <div className="flex items-center">
                                <Mail className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="truncate">{contact.email}</span>
                              </div>
                            )}
                            {contact.phone && (
                              <div className="flex items-center">
                                <Phone className="h-3 w-3 mr-1 text-gray-400" />
                                <span>{formatPhoneNumber(contact.phone)}</span>
                              </div>
                            )}
                          </div>
                          {contact.relationship && (
                            <div className="mt-1">
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700">
                                {contact.relationship}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditContact(contact)}
                          >
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => confirmDeleteContact(contact)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contact Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Contact</DialogTitle>
            <DialogDescription>
              Add people who should be contacted by your trustees after your passing.
            </DialogDescription>
          </DialogHeader>
          <ContactForm
            onSubmit={handleAddContact}
            onCancel={() => setIsFormOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Contact Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) setEditingContact(null);
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Contact</DialogTitle>
            <DialogDescription>
              Update contact information for {editingContact ?
                (editingContact.first_name && editingContact.last_name ?
                  `${editingContact.first_name} ${editingContact.last_name}` :
                  editingContact.name || 'this contact') :
                'this contact'}
            </DialogDescription>
          </DialogHeader>
          {editingContact && (
            <ContactForm
              onSubmit={handleUpdateContact}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setEditingContact(null);
              }}
              defaultValues={{
                first_name: editingContact.first_name,
                last_name: editingContact.last_name,
                name: editingContact.name,
                email: editingContact.email || '',
                phone: editingContact.phone || '',
                relationship: editingContact.relationship,
                notes: editingContact.notes || '',
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the contact "{selectedContact ?
                (selectedContact.first_name && selectedContact.last_name ?
                  `${selectedContact.first_name} ${selectedContact.last_name}` :
                  selectedContact.name || 'this contact') :
                'this contact'}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteContact} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </MainLayout>
  );
}
