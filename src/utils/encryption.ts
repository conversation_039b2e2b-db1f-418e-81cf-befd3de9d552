
/**
 * Utility functions for client-side encryption and decryption of files
 * Using the Web Crypto API for secure client-side encryption
 */

/**
 * Generate a random password of a given length
 * @param length The length of the password to generate
 * @returns A random password
 */
export const generatePassword = (length: number): string => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
};

/**
 * Convert a Uint8Array to a Base64 string
 * @param buffer The Uint8Array to convert
 * @returns A Base64 string
 */
export const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

/**
 * Convert a Base64 string to a Uint8Array
 * @param base64 The Base64 string to convert
 * @returns A Uint8Array
 */
export const base64ToArrayBuffer = (base64: string): Uint8Array => {
  try {
    // Make sure the base64 string is valid
    if (!base64 || typeof base64 !== 'string') {
      console.error('Invalid base64 string:', base64);
      throw new Error('Invalid base64 string');
    }

    // Remove any non-base64 characters (like whitespace)
    const cleanBase64 = base64.replace(/[^A-Za-z0-9+/=]/g, '');

    const binaryString = atob(cleanBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  } catch (error) {
    console.error('Error converting base64 to array buffer:', error);
    throw new Error('Failed to convert base64 to array buffer: ' + error.message);
  }
};

/**
 * Encrypt a file using AES-GCM with streaming for large files
 * @param file The file to encrypt
 * @returns The encrypted file
 */
export const encryptFile = async (file: File): Promise<File> => {
  try {
    // Generate a random initialization vector (IV)
    const iv = crypto.getRandomValues(new Uint8Array(12)); // 96 bits is recommended for AES-GCM

    // Generate a random encryption key
    const key = await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256,
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );

    // Export the key to raw format
    const exportedKey = await crypto.subtle.exportKey('raw', key);

    // Store the key in sessionStorage for later use
    sessionStorage.setItem('lastEncryptionKey', arrayBufferToBase64(exportedKey));

    // For large files, we'll use a chunked approach
    const CHUNK_SIZE = 10 * 1024 * 1024; // 10MB chunks
    const fileSize = file.size;
    const chunks = Math.ceil(fileSize / CHUNK_SIZE);

    // If the file is small enough, use the simple approach
    if (chunks === 1) {
      const fileBuffer = await file.arrayBuffer();
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv,
        },
        key,
        fileBuffer
      );

      // Combine the IV and encrypted data
      // Format: [IV length (1 byte)][IV][Encrypted data]
      const combinedBuffer = new Uint8Array(1 + iv.length + encryptedBuffer.byteLength);
      combinedBuffer[0] = iv.length;
      combinedBuffer.set(iv, 1);
      combinedBuffer.set(new Uint8Array(encryptedBuffer), 1 + iv.length);

      // Create a new File object with the encrypted data
      const encryptedFile = new File([combinedBuffer], file.name, {
        type: 'application/encrypted',
        lastModified: file.lastModified,
      });

      return encryptedFile;
    }
    // For larger files, use a chunked approach with a Blob
    else {
      // Create a header with the IV
      const header = new Uint8Array(1 + iv.length);
      header[0] = iv.length;
      header.set(iv, 1);

      const encryptedChunks = [header];

      for (let i = 0; i < chunks; i++) {
        const start = i * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, fileSize);
        const chunk = await file.slice(start, end).arrayBuffer();

        const encryptedChunk = await crypto.subtle.encrypt(
          {
            name: 'AES-GCM',
            iv: iv, // Use the same IV for all chunks
          },
          key,
          chunk
        );

        encryptedChunks.push(new Uint8Array(encryptedChunk));
      }

      // Create a new File object with all the encrypted chunks
      const encryptedFile = new File(encryptedChunks, file.name, {
        type: 'application/encrypted',
        lastModified: file.lastModified,
      });

      return encryptedFile;
    }
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt file');
  }
};

/**
 * Decrypt a file using AES-GCM with streaming for large files
 * @param encryptedFile The encrypted file
 * @param encryptionKey The encryption key as a Uint8Array or Base64 string
 * @param originalFileName Optional original file name
 * @param originalMimeType Optional original MIME type
 * @returns A Promise resolving to the decrypted file
 */
export const decryptFile = async (
  encryptedFile: Blob,
  encryptionKey: string | Uint8Array,
  originalFileName?: string,
  originalMimeType?: string
): Promise<File> => {
  console.log('Decrypting file:', {
    fileSize: encryptedFile.size,
    keyType: typeof encryptionKey,
    keyLength: typeof encryptionKey === 'string' ? encryptionKey.length : encryptionKey.length
  });
  try {
    // Convert string key to Uint8Array if needed
    const keyArray = typeof encryptionKey === 'string'
      ? base64ToArrayBuffer(encryptionKey)
      : encryptionKey;

    // Import the key
    const key = await crypto.subtle.importKey(
      'raw',
      keyArray,
      {
        name: 'AES-GCM',
        length: 256,
      },
      false, // not extractable
      ['decrypt']
    );

    // Read the first chunk to get the IV
    const headerChunk = await encryptedFile.slice(0, 100).arrayBuffer(); // First 100 bytes should be enough for the header
    const headerData = new Uint8Array(headerChunk);

    // Extract the IV length and IV
    const ivLength = headerData[0];

    // Check if the IV length is valid (should be between 1 and 16)
    if (ivLength < 1 || ivLength > 16) {
      console.error('Invalid IV length:', ivLength);
      throw new Error('Invalid file format or encryption');
    }

    const iv = headerData.slice(1, 1 + ivLength);

    // For large files, use a chunked approach
    const CHUNK_SIZE = 10 * 1024 * 1024; // 10MB chunks
    const fileSize = encryptedFile.size;
    const dataStart = 1 + ivLength; // Start of the actual encrypted data

    try {
      // If the file is small enough, use the simple approach
      if (fileSize - dataStart < CHUNK_SIZE) {
        const encryptedBuffer = await encryptedFile.arrayBuffer();
        const encryptedData = new Uint8Array(encryptedBuffer);
        const data = encryptedData.slice(dataStart);

        const decryptedBuffer = await crypto.subtle.decrypt(
          {
            name: 'AES-GCM',
            iv,
          },
          key,
          data
        );

        // Create a new File object with the decrypted data
        const fileName = originalFileName || "decrypted-file";
        const mimeType = originalMimeType || 'application/octet-stream';

        const decryptedFile = new File([decryptedBuffer], fileName, {
          type: mimeType,
          lastModified: Date.now(),
        });

        return decryptedFile;
      }
      // For larger files, use a chunked approach
      else {
        const chunks = Math.ceil((fileSize - dataStart) / CHUNK_SIZE);
        const decryptedChunks = [];

        for (let i = 0; i < chunks; i++) {
          const start = dataStart + (i * CHUNK_SIZE);
          const end = Math.min(start + CHUNK_SIZE, fileSize);
          const chunk = await encryptedFile.slice(start, end).arrayBuffer();

          const decryptedChunk = await crypto.subtle.decrypt(
            {
              name: 'AES-GCM',
              iv,
            },
            key,
            chunk
          );

          decryptedChunks.push(new Uint8Array(decryptedChunk));
        }

        // Create a new File object with all the decrypted chunks
        const fileName = originalFileName || "decrypted-file";
        const mimeType = originalMimeType || 'application/octet-stream';

        const decryptedFile = new File(decryptedChunks, fileName, {
          type: mimeType,
          lastModified: Date.now(),
        });

        return decryptedFile;
      }
    } catch (cryptoError) {
      console.error('Crypto operation error:', cryptoError);

      // Try an alternative approach for files from the API
      // This handles the case where the file might not have the IV header format
      // because it was uploaded before the encryption was implemented
      try {
        const encryptedBuffer = await encryptedFile.arrayBuffer();

        // Use a fixed IV for legacy files (not secure, but needed for compatibility)
        const fixedIv = new Uint8Array(12).fill(1); // A fixed IV for legacy files

        const decryptedBuffer = await crypto.subtle.decrypt(
          {
            name: 'AES-GCM',
            iv: fixedIv,
          },
          key,
          encryptedBuffer
        );

        // Create a new File object with the decrypted data
        const fileName = originalFileName || "decrypted-file";
        const mimeType = originalMimeType || 'application/octet-stream';

        const decryptedFile = new File([decryptedBuffer], fileName, {
          type: mimeType,
          lastModified: Date.now(),
        });

        return decryptedFile;
      } catch (fallbackError) {
        console.error('Fallback decryption failed:', fallbackError);
        throw new Error('Failed to decrypt file: ' + fallbackError.message);
      }
    }
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt file: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Check if a file is encrypted
 * @param file The file to check
 * @returns A Promise resolving to a boolean indicating if the file is encrypted
 */
export const isFileEncrypted = async (file: File): Promise<boolean> => {
  try {
    // Read the first few bytes of the file
    const headerChunk = await file.slice(0, 20).arrayBuffer();
    const headerData = new Uint8Array(headerChunk);

    // Check if the file has our encryption header format
    // The first byte should be the IV length (typically 12 for AES-GCM)
    const ivLength = headerData[0];

    // A valid IV length for AES-GCM is typically 12 bytes
    return ivLength === 12 && file.size > 13; // IV length byte + IV + some data
  } catch (error) {
    console.error('Error checking if file is encrypted:', error);
    return false;
  }
};
