import { Question, Recommendation } from '@/types/will-advisor.types';

export const willAdvisorQuestions: Question[] = [
  {
    id: 'marital_status',
    text: 'What is your current marital status?',
    type: 'single-select',
    options: [
      { id: 'single', label: 'Single', value: 'single' },
      { id: 'married', label: 'Married', value: 'married' },
      { id: 'domestic_partnership', label: 'Domestic Partnership', value: 'domestic_partnership' },
      { id: 'divorced', label: 'Divorced', value: 'divorced' },
      { id: 'widowed', label: 'Widowed', value: 'widowed' },
    ],
    required: true,
    helpText: 'Your marital status affects how your assets may be distributed.',
  },
  {
    id: 'children',
    text: 'Do you have any children?',
    type: 'boolean',
    required: true,
  },
  {
    id: 'children_count',
    text: 'How many children do you have?',
    type: 'number',
    conditionalDisplay: {
      dependsOn: 'children',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'children_minors',
    text: 'Are any of your children under 18 years old?',
    type: 'boolean',
    conditionalDisplay: {
      dependsOn: 'children',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'guardian_appointed',
    text: 'Have you appointed a guardian for your minor children in case of your death?',
    type: 'boolean',
    conditionalDisplay: {
      dependsOn: 'children_minors',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'real_estate',
    text: 'Do you own any real estate?',
    type: 'boolean',
    required: true,
  },
  {
    id: 'real_estate_type',
    text: 'What type of real estate do you own?',
    type: 'multi-select',
    options: [
      { id: 'primary_residence', label: 'Primary Residence', value: 'primary_residence' },
      { id: 'vacation_home', label: 'Vacation Home', value: 'vacation_home' },
      { id: 'rental_property', label: 'Rental Property', value: 'rental_property' },
      { id: 'commercial_property', label: 'Commercial Property', value: 'commercial_property' },
      { id: 'land', label: 'Undeveloped Land', value: 'land' },
    ],
    conditionalDisplay: {
      dependsOn: 'real_estate',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'business_owner',
    text: 'Do you own a business or have ownership interest in a business?',
    type: 'boolean',
    required: true,
  },
  {
    id: 'business_succession',
    text: 'Do you have a business succession plan in place?',
    type: 'boolean',
    conditionalDisplay: {
      dependsOn: 'business_owner',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'investments',
    text: 'Do you have significant investments or financial assets?',
    type: 'boolean',
    required: true,
    helpText: 'This includes stocks, bonds, retirement accounts, etc.',
  },
  {
    id: 'investment_types',
    text: 'What types of investments do you have?',
    type: 'multi-select',
    options: [
      { id: 'stocks', label: 'Stocks', value: 'stocks' },
      { id: 'bonds', label: 'Bonds', value: 'bonds' },
      { id: 'retirement', label: 'Retirement Accounts (401k, IRA, etc.)', value: 'retirement' },
      { id: 'crypto', label: 'Cryptocurrency', value: 'crypto' },
      { id: 'mutual_funds', label: 'Mutual Funds', value: 'mutual_funds' },
      { id: 'other', label: 'Other', value: 'other' },
    ],
    conditionalDisplay: {
      dependsOn: 'investments',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'digital_assets',
    text: 'Do you have digital assets that you want to include in your will?',
    type: 'boolean',
    required: true,
    helpText: 'Digital assets include online accounts, digital files, cryptocurrency, etc.',
  },
  {
    id: 'digital_asset_types',
    text: 'What types of digital assets do you have?',
    type: 'multi-select',
    options: [
      { id: 'social_media', label: 'Social Media Accounts', value: 'social_media' },
      { id: 'email', label: 'Email Accounts', value: 'email' },
      { id: 'crypto', label: 'Cryptocurrency', value: 'crypto' },
      { id: 'digital_files', label: 'Digital Files (photos, videos, documents)', value: 'digital_files' },
      { id: 'online_accounts', label: 'Online Accounts with Monetary Value', value: 'online_accounts' },
      { id: 'domains', label: 'Domain Names', value: 'domains' },
      { id: 'other', label: 'Other', value: 'other' },
    ],
    conditionalDisplay: {
      dependsOn: 'digital_assets',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'pets',
    text: 'Do you have pets that you want to make provisions for?',
    type: 'boolean',
    required: true,
  },
  {
    id: 'existing_will',
    text: 'Do you already have a will?',
    type: 'boolean',
    required: true,
  },
  {
    id: 'will_update_reason',
    text: 'Why are you updating your will?',
    type: 'multi-select',
    options: [
      { id: 'marriage', label: 'Recent Marriage or Divorce', value: 'marriage' },
      { id: 'children', label: 'Birth or Adoption of Children', value: 'children' },
      { id: 'assets', label: 'Significant Change in Assets', value: 'assets' },
      { id: 'move', label: 'Moved to a Different State/Country', value: 'move' },
      { id: 'beneficiaries', label: 'Change in Beneficiaries', value: 'beneficiaries' },
      { id: 'other', label: 'Other', value: 'other' },
    ],
    conditionalDisplay: {
      dependsOn: 'existing_will',
      showIfValue: 'true',
    },
    required: true,
  },
  {
    id: 'specific_bequests',
    text: 'Do you want to leave specific items or amounts to particular individuals?',
    type: 'boolean',
    required: true,
    helpText: 'These are called specific bequests and can include jewelry, artwork, family heirlooms, or specific amounts of money.',
  },
  {
    id: 'charitable_giving',
    text: 'Are you interested in leaving any assets to charity?',
    type: 'boolean',
    required: true,
  },
  {
    id: 'executor_selected',
    text: 'Have you selected an executor for your will?',
    type: 'boolean',
    required: true,
    helpText: 'An executor is responsible for carrying out the instructions in your will.',
  },
  {
    id: 'power_of_attorney',
    text: 'Do you have a power of attorney document?',
    type: 'boolean',
    required: true,
    helpText: 'A power of attorney designates someone to make decisions on your behalf if you become incapacitated.',
  },
  {
    id: 'healthcare_directive',
    text: 'Do you have an advance healthcare directive or living will?',
    type: 'boolean',
    required: true,
    helpText: 'This document specifies your wishes for medical treatment if you become unable to communicate.',
  },
];

export const willAdvisorRecommendations: Recommendation[] = [
  {
    id: 'basic_will',
    title: 'Create a Basic Will',
    description: 'Based on your answers, we recommend creating a basic will to ensure your assets are distributed according to your wishes.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'existing_will', operator: 'equals', value: false }
      ]
    },
    actionLink: '/will/create',
    actionText: 'Create Will Now',
  },
  {
    id: 'update_will',
    title: 'Update Your Existing Will',
    description: 'Your life circumstances have changed since creating your will. We recommend updating it to reflect your current situation.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'existing_will', operator: 'equals', value: true }
      ]
    },
    actionLink: '/will/update',
    actionText: 'Update Will Now',
  },
  {
    id: 'guardian_appointment',
    title: 'Appoint a Guardian for Minor Children',
    description: 'You have minor children but haven\'t appointed a guardian. This is crucial to ensure your children are cared for by someone you trust if something happens to you.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'children_minors', operator: 'equals', value: true },
        { questionId: 'guardian_appointed', operator: 'equals', value: false }
      ]
    },
    actionLink: '/will/guardian',
    actionText: 'Appoint Guardian',
  },
  {
    id: 'business_succession_plan',
    title: 'Create a Business Succession Plan',
    description: 'As a business owner without a succession plan, it\'s important to establish what happens to your business after your death to protect its value and legacy.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'business_owner', operator: 'equals', value: true },
        { questionId: 'business_succession', operator: 'equals', value: false }
      ]
    },
    actionLink: '/will/business-succession',
    actionText: 'Plan Business Succession',
  },
  {
    id: 'digital_assets_plan',
    title: 'Include Digital Assets in Your Will',
    description: 'You have significant digital assets that should be included in your estate planning. Our Digital Vault can help you organize these assets and ensure they\'re properly addressed in your will.',
    priority: 'medium',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'digital_assets', operator: 'equals', value: true }
      ]
    },
    actionLink: '/vault',
    actionText: 'Set Up Digital Vault',
  },
  {
    id: 'pet_trust',
    title: 'Consider a Pet Trust or Provision',
    description: 'You\'ve indicated you have pets. Consider including specific provisions for their care in your will or setting up a pet trust.',
    priority: 'medium',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'pets', operator: 'equals', value: true }
      ]
    },
    actionLink: '/will/pet-provisions',
    actionText: 'Add Pet Provisions',
  },
  {
    id: 'specific_bequests',
    title: 'Document Your Specific Bequests',
    description: 'You want to leave specific items to particular individuals. Make sure to clearly document these wishes in your will to avoid confusion or disputes.',
    priority: 'medium',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'specific_bequests', operator: 'equals', value: true }
      ]
    },
    actionLink: '/will/bequests',
    actionText: 'Document Bequests',
  },
  {
    id: 'charitable_giving',
    title: 'Plan Your Charitable Giving',
    description: 'You\'ve expressed interest in charitable giving. Consider establishing a charitable trust or specific bequests to organizations you care about.',
    priority: 'medium',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'charitable_giving', operator: 'equals', value: true }
      ]
    },
    actionLink: '/will/charitable-giving',
    actionText: 'Plan Charitable Giving',
  },
  {
    id: 'executor_selection',
    title: 'Select an Executor for Your Will',
    description: 'You haven\'t selected an executor for your will. This person will be responsible for carrying out your wishes, so it\'s important to choose someone trustworthy and capable.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'executor_selected', operator: 'equals', value: false }
      ]
    },
    actionLink: '/will/executor',
    actionText: 'Select Executor',
  },
  {
    id: 'power_of_attorney',
    title: 'Create a Power of Attorney',
    description: 'You don\'t have a power of attorney document. This is crucial for ensuring someone can make financial and legal decisions on your behalf if you become incapacitated.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'power_of_attorney', operator: 'equals', value: false }
      ]
    },
    actionLink: '/will/power-of-attorney',
    actionText: 'Create Power of Attorney',
  },
  {
    id: 'healthcare_directive',
    title: 'Create an Advance Healthcare Directive',
    description: 'You don\'t have an advance healthcare directive. This document ensures your medical wishes are respected if you can\'t communicate them yourself.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'healthcare_directive', operator: 'equals', value: false }
      ]
    },
    actionLink: '/will/healthcare-directive',
    actionText: 'Create Healthcare Directive',
  },
  {
    id: 'real_estate_planning',
    title: 'Plan for Your Real Estate',
    description: 'You own real estate, which is often one of the most valuable assets in an estate. Ensure your will clearly addresses how these properties should be handled.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'real_estate', operator: 'equals', value: true }
      ]
    },
    actionLink: '/will/real-estate',
    actionText: 'Plan Real Estate Distribution',
  },
  {
    id: 'investment_planning',
    title: 'Plan for Your Investment Assets',
    description: 'You have significant investments that should be carefully addressed in your estate planning. Consider working with a financial advisor to optimize the transfer of these assets.',
    priority: 'high',
    conditionalDisplay: {
      logic: 'AND',
      conditions: [
        { questionId: 'investments', operator: 'equals', value: true }
      ]
    },
    actionLink: '/will/investments',
    actionText: 'Plan Investment Distribution',
  },
  {
    id: 'crypto_planning',
    title: 'Plan for Your Cryptocurrency Assets',
    description: 'You own cryptocurrency, which requires special consideration in estate planning. Ensure your beneficiaries will have the information needed to access these digital assets.',
    priority: 'medium',
    conditionalDisplay: {
      logic: 'OR',
      conditions: [
        { 
          questionId: 'investment_types', 
          operator: 'contains', 
          value: 'crypto' 
        },
        { 
          questionId: 'digital_asset_types', 
          operator: 'contains', 
          value: 'crypto' 
        }
      ]
    },
    actionLink: '/vault/crypto',
    actionText: 'Secure Crypto Assets',
  }
];
