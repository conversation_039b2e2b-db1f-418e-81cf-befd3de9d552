// Types for the Will Advisor feature

export type QuestionType = 'single-select' | 'multi-select' | 'text' | 'boolean' | 'number';

export interface Option {
  id: string;
  label: string;
  value: string;
}

export interface Question {
  id: string;
  text: string;
  type: QuestionType;
  options?: Option[];
  conditionalDisplay?: {
    dependsOn: string; // Question ID this depends on
    showIfValue: string | string[]; // Value(s) that trigger this question to be shown
  };
  helpText?: string;
  required?: boolean;
}

export interface Answer {
  questionId: string;
  value: string | string[] | boolean | number;
}

export interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  conditionalDisplay?: {
    logic: 'AND' | 'OR';
    conditions: Array<{
      questionId: string;
      operator: 'equals' | 'contains' | 'greaterThan' | 'lessThan' | 'notEquals';
      value: string | string[] | boolean | number;
    }>;
  };
  actionLink?: string;
  actionText?: string;
}

export interface WillAdvisorState {
  currentStep: number;
  answers: Answer[];
  recommendations: Recommendation[];
  completed: boolean;
}
