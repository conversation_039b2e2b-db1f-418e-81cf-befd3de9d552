// Types for the Service Sunset feature

export type ServiceCategory =
  | 'subscription'
  | 'utility'
  | 'financial'
  | 'entertainment'
  | 'other';

// Priority and cancellation method types removed as they're not needed

export interface ServiceSunset {
  id: string;
  user_id: string;
  name: string;
  category: ServiceCategory;
  custom_category?: string;
  description?: string;
  website?: string;
  account_number?: string;
  contact_info?: string;
  cancellation_instructions?: string;
  auto_renewal: boolean;
  renewal_date?: string;
  cost_per_period?: number;
  period?: string;
  created_at: string;
  updated_at: string;
}

export const SERVICE_CATEGORIES: { value: ServiceCategory; label: string }[] = [
  { value: 'subscription', label: 'Subscription Service' },
  { value: 'utility', label: 'Utility' },
  { value: 'financial', label: 'Financial Service' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'other', label: 'Other' },
];

// Priority and cancellation method constants removed as they're not needed
