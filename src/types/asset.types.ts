// Types for the Asset Management feature

export type AssetType = 'physical' | 'digital';

export interface AssetCategory {
  value: string;
  label: string;
  icon: string;
  description: string;
}

export const ASSET_CATEGORIES: AssetCategory[] = [
  {
    value: 'real_estate',
    label: 'Real Estate',
    icon: 'Home',
    description: 'Properties, land, and buildings'
  },
  {
    value: 'financial_account',
    label: 'Financial Account',
    icon: 'DollarSign',
    description: 'Bank accounts, investment accounts, retirement accounts'
  },
  {
    value: 'vehicle',
    label: 'Vehicle',
    icon: 'Car',
    description: 'Cars, boats, motorcycles, and other vehicles'
  },
  {
    value: 'jewelry',
    label: 'Jewelry',
    icon: 'Diamond',
    description: 'Valuable jewelry and watches'
  },
  {
    value: 'art',
    label: 'Art & Collectibles',
    icon: 'Image',
    description: 'Artwork, paintings, sculptures, and collectibles'
  },
  {
    value: 'antique',
    label: 'Antiques',
    icon: 'Clock',
    description: 'Antiques and vintage items'
  },
  {
    value: 'financial_instrument',
    label: 'Financial Instrument',
    icon: 'FileText',
    description: 'Stocks, bonds, certificates, insurance policies'
  },
  {
    value: 'business',
    label: 'Business',
    icon: 'Briefcase',
    description: 'Business assets and ownership interests'
  },
  {
    value: 'heirloom',
    label: 'Family Heirloom',
    icon: 'Gift',
    description: 'Family heirlooms and items of sentimental value'
  },
  {
    value: 'other',
    label: 'Other Asset',
    icon: 'Package',
    description: 'Other assets not listed above'
  }
];

// For backward compatibility
export const PHYSICAL_ASSET_CATEGORIES = ASSET_CATEGORIES;
export const DIGITAL_ASSET_CATEGORIES = [];
