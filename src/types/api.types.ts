export interface ApiResponse {
  success: boolean
  data?: unknown
  error?: {
    message: string
    code?: string
  }
}

export interface User {
  id: string
  email: string
  first_name?: string
  last_name?: string
  email_verified?: boolean
  password_hash?: string
  created_at?: string
  updated_at?: string
}

export interface AuthResponse extends ApiResponse {
  data?: User
}

export interface Document {
  id: string
  name: string
  url: string
  size: number
  type: string
  created_at: string
  updated_at: string
}

export interface Asset {
  id: string
  name: string
  type: string
  value?: number
  notes?: string
  created_at: string
  updated_at: string
}

export interface ServiceSunset {
  id: string
  name: string
  url: string
  credentials?: string
  instructions?: string
  created_at: string
  updated_at: string
}

export interface Trustee {
  id: string
  name: string
  email: string
  relationship: string
  status: 'pending' | 'accepted' | 'declined'
  created_at: string
  updated_at: string
}

export interface TimeCapsule {
  id: string
  title: string
  message: string
  delivery_date: string
  recipients: Array<{
    name: string
    email: string
  }>
  media_urls?: string[]
  created_at: string
  updated_at: string
}
