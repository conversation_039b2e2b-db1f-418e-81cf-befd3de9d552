/* Custom styles for react-phone-input-2 */

.phone-input-container {
  width: 100%;
}

.phone-container {
  width: 100% !important;
  height: 40px;
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--input)) !important;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out !important;
  display: flex !important;
}

.phone-container:hover {
  border-color: hsl(var(--ring)) !important;
}

.phone-container:focus-within {
  border-color: hsl(var(--ring)) !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3) !important;
}

.phone-input {
  width: 100% !important;
  height: 40px !important;
  padding: 0.5rem 0.75rem !important;
  padding-left: 60px !important; /* Make room for the flag */
  border-radius: 0.375rem !important;
  border: none !important;
  background-color: transparent !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 150ms !important;
  font-family: inherit !important;
}

.phone-input:focus {
  outline: none !important;
  box-shadow: none !important;
}

.phone-dropdown-button {
  border: none !important;
  background-color: transparent !important;
  border-right: 1px solid hsl(var(--input)) !important;
  border-radius: 0.375rem 0 0 0.375rem !important;
  padding: 0 0.5rem !important;
  transition: background-color 0.2s ease-in-out !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  height: 100% !important;
  width: 50px !important;
  z-index: 2 !important;
}

.phone-dropdown-button:hover {
  background-color: hsl(var(--accent) / 0.1) !important;
}

.phone-dropdown {
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  background-color: hsl(var(--background)) !important;
  max-height: 300px !important;
  z-index: 100 !important;
}

.phone-search {
  margin: 0.5rem !important;
  padding: 0.5rem !important;
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--input)) !important;
  width: calc(100% - 1rem) !important;
}

.react-tel-input .country-list {
  margin: 0 !important;
  padding: 0.5rem 0 !important;
  border-radius: 0.375rem !important;
  overflow-y: auto !important;
  max-height: 200px !important;
}

.react-tel-input .country-list .country {
  padding: 0.5rem 0.75rem !important;
  transition: background-color 0.2s ease-in-out !important;
}

.react-tel-input .country-list .country:hover,
.react-tel-input .country-list .country.highlight {
  background-color: hsl(var(--accent) / 0.1) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.react-tel-input .flag-dropdown.open {
  background-color: transparent !important;
}

.react-tel-input .selected-flag {
  padding: 0 0.75rem 0 0.5rem !important;
  transition: background-color 0.2s ease-in-out !important;
  width: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.react-tel-input .selected-flag:hover,
.react-tel-input .selected-flag:focus {
  background-color: transparent !important;
}

.react-tel-input .selected-flag .arrow {
  border-top-color: hsl(var(--foreground)) !important;
  transition: transform 0.2s ease-in-out !important;
}

.react-tel-input .selected-flag .arrow.up {
  border-bottom-color: hsl(var(--foreground)) !important;
  border-top-color: transparent !important;
}

.react-tel-input .flag {
  transform: scale(1.2) !important;
}

/* Fix for flag display */
.react-tel-input .flag-dropdown {
  border: none !important;
  background-color: transparent !important;
}

/* Fix for selected flag */
.react-tel-input .selected-flag:hover,
.react-tel-input .selected-flag:focus,
.react-tel-input .selected-flag.open {
  background-color: transparent !important;
}
