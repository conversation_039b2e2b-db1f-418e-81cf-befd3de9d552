// ESLint-disable-next-line react-refresh/only-export-components
// Phone validation functions
export const validatePhoneNumber = (phone: string, countryCode: string): boolean => {
  if (!phone) return true; // Empty is valid (optional)
  
  // Remove any non-digit characters except for the leading +
  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  // Basic validation patterns by country code
  const patterns: Record<string, RegExp> = {
    '+1': /^\+?1?\d{10}$/, // US/Canada: +1 followed by 10 digits
    '+44': /^\+?44?\d{10}$/, // UK: +44 followed by 10 digits
    '+61': /^\+?61?\d{9}$/, // Australia: +61 followed by 9 digits
    '+33': /^\+?33?\d{9}$/, // France: +33 followed by 9 digits
    '+49': /^\+?49?\d{10,11}$/, // Germany: +49 followed by 10-11 digits
    '+81': /^\+?81?\d{10}$/, // Japan: +81 followed by 10 digits
    '+86': /^\+?86?\d{11}$/, // China: +86 followed by 11 digits
    '+91': /^\+?91?\d{10}$/, // India: +91 followed by 10 digits
  };
  
  // If we have a specific pattern for this country code, use it
  if (patterns[countryCode]) {
    return patterns[countryCode].test(cleanPhone);
  }
  
  // Default validation: country code + at least 7 digits
  return /^\+?\d{1,4}\d{7,}$/.test(cleanPhone);
};

// Format phone number for display
export const formatPhoneNumber = (phone: string, countryCode: string = '+1'): string => {
  if (!phone) return '';
  
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format based on country code
  if (countryCode === '+1') {
    // US/Canada: (XXX) XXX-XXXX
    if (digits.length === 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    }
  }
  
  // Default: just return the cleaned number with the country code
  return `${countryCode} ${digits}`;
};
