import { createClient } from '@supabase/supabase-js';
import { AuthResponse, User } from '../types/api.types';

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export const supabaseAdmin = createClient(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Environment variables are already defined above

/**
 * Generate a secure random string of specified length
 */
export function generateRandomString(length: number): string {
  const array = new Uint8Array(Math.ceil(length / 2));
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0'))
    .join('')
    .slice(0, length);
}

/**
 * Generate a 6-digit verification code
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Hash a password using PBKDF2
 */
export async function hashPassword(password: string): Promise<string> {
  // Generate a random salt
  const saltBuffer = new Uint8Array(16);
  crypto.getRandomValues(saltBuffer);
  const salt = Array.from(saltBuffer, byte => byte.toString(16).padStart(2, '0')).join('');

  // Import the password as a key
  const encoder = new TextEncoder();
  const passwordBuffer = encoder.encode(password);
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    passwordBuffer,
    { name: 'PBKDF2' },
    false,
    ['deriveBits']
  );

  // Derive the key
  const derivedBits = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt: encoder.encode(salt),
      iterations: 10000,
      hash: 'SHA-512'
    },
    keyMaterial,
    512 // 64 bytes
  );

  // Convert to hex
  const derivedKey = Array.from(new Uint8Array(derivedBits))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  // Format: iterations:salt:hash
  return `10000:${salt}:${derivedKey}`;
}

/**
 * Verify a password against a hash
 */
export async function verifyPassword(password: string, storedHash: string): Promise<boolean> {
  // Split the stored hash into its components
  const [iterations, salt, hash] = storedHash.split(':');
  const iterCount = parseInt(iterations);

  // Import the password as a key
  const encoder = new TextEncoder();
  const passwordBuffer = encoder.encode(password);
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    passwordBuffer,
    { name: 'PBKDF2' },
    false,
    ['deriveBits']
  );

  // Derive the key
  const derivedBits = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt: encoder.encode(salt),
      iterations: iterCount,
      hash: 'SHA-512'
    },
    keyMaterial,
    512 // 64 bytes
  );

  // Convert to hex
  const derivedKey = Array.from(new Uint8Array(derivedBits))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  // Compare the hashes
  return derivedKey === hash;
}

/**
 * Generate a session token
 */
export function generateSessionToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Send a verification email with a 6-digit code
 * Note: Actual email sending is handled by API route /api/auth/send-verification-email
 */
export async function sendVerificationEmail(email: string, code: string): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/send-verification-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, code }),
    });

    if (!response.ok) {
      console.error('Error sending verification email:', await response.text());
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception sending verification email:', error);
    return false;
  }
}

/**
 * Store a verification code in the database
 */
export async function storeVerificationCode(email: string, code: string, expiresInHours: number = 24) {
  // Calculate expiration time
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + expiresInHours);

  // Store the code in the database
  const { data, error } = await supabaseAdmin
    .from('verification_codes')
    .insert({
      email,
      code,
      expires_at: expiresAt.toISOString(),
    })
    .select();

  if (error) {
    console.error('Error storing verification code:', error);
    throw new Error('Failed to store verification code');
  }

  return data;
}

/**
 * Verify a code for a given email
 */
export async function verifyCode(email: string, code: string): Promise<boolean> {
  // Check if the code exists and is valid
  const { data, error } = await supabaseAdmin
    .from('verification_codes')
    .select('*')
    .eq('email', email)
    .eq('code', code)
    .eq('used', false)
    .gte('expires_at', new Date().toISOString())
    .order('created_at', { ascending: false })
    .limit(1);

  if (error) {
    console.error('Error verifying code:', error);
    return false;
  }

  if (!data || data.length === 0) {
    return false;
  }

  // Mark the code as used
  const { error: updateError } = await supabaseAdmin
    .from('verification_codes')
    .update({ used: true })
    .eq('id', data[0].id);

  if (updateError) {
    console.error('Error marking code as used:', updateError);
    // Continue anyway, this is not critical
  }

  return true;
}

/**
 * Create a profile for a user
 */
export async function createUserProfile(userId: string, email: string, firstName: string, lastName: string) {
  try {
    const { error } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: userId,
        email,
        first_name: firstName,
        last_name: lastName,
        subscription_tier: 'free',
        subscription_status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error creating user profile:', error);
      // Don't throw an error here, as we want the user creation to succeed even if profile creation fails
    }
  } catch (error) {
    console.error('Exception creating user profile:', error);
    // Don't throw an error here, as we want the user creation to succeed even if profile creation fails
  }
}

/**
 * Create a new user
 */
export async function createUser(email: string, password: string, firstName: string, lastName: string) {
  // Hash the password
  const passwordHash = await hashPassword(password);

  // Create the user
  const { data, error } = await supabaseAdmin
    .from('custom_users')
    .insert({
      email,
      password_hash: passwordHash,
      first_name: firstName,
      last_name: lastName,
    })
    .select();

  if (error) {
    console.error('Error creating user:', error);
    throw new Error('Failed to create user');
  }

  // Create a profile for the user
  await createUserProfile(data[0].id, email, firstName, lastName);

  return data[0];
}

/**
 * Get a user by email
 */
export async function getUserByEmail(email: string) {
  try {
    // Check if Supabase URL and key are set
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Supabase URL or service key is not set');
      throw new Error('Database configuration error: Missing Supabase credentials');
    }

    // Log connection attempt (without sensitive data)
    console.log(`Attempting to connect to Supabase at ${supabaseUrl.substring(0, 20)}... to get user by email`);

    // Use the existing supabaseAdmin client
    const { data, error } = await supabaseAdmin
      .from('custom_users')
      .select('*')
      .eq('email', email)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No user found
        console.log(`No user found with email: ${email}`);
        return null;
      }
      console.error('Error getting user by email:', error);
      throw new Error(`Database error: ${error.message || 'Failed to get user'}`);
    }

    console.log(`Successfully retrieved user with email: ${email}`);
    return data;
  } catch (error: any) {
    console.error('Exception getting user by email:', error);
    // Provide more detailed error message
    if (error.message && error.message.includes('Database configuration error')) {
      throw error; // Rethrow the same error if it's our custom error
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new Error(`Database connection error: ${error.message}`);
    } else {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }
}

/**
 * Get a user by ID
 */
export async function getUserById(id: string | unknown): Promise<User | null> {
  // Convert id to string if it's not already
  const userId = typeof id === 'string' ? id : String(id);
  try {
    // Check if Supabase URL and key are set
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Supabase URL or service key is not set');
      throw new Error('Database configuration error: Missing Supabase credentials');
    }

    // Log connection attempt (without sensitive data)
    console.log(`Attempting to connect to Supabase at ${supabaseUrl.substring(0, 20)}... to get user by ID`);

    const { data, error } = await supabaseAdmin
      .from('custom_users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No user found
        console.log(`No user found with ID: ${userId}`);
        return null;
      }
      console.error('Error getting user by ID:', error);
      throw new Error(`Database error: ${error.message || 'Failed to get user'}`);
    }

    console.log(`Successfully retrieved user with ID: ${userId}`);
    return data;
  } catch (error: any) {
    console.error('Exception getting user by ID:', error);
    // Provide more detailed error message
    if (error.message && error.message.includes('Database configuration error')) {
      throw error; // Rethrow the same error if it's our custom error
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new Error(`Database connection error: ${error.message}`);
    } else {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }
}

/**
 * Create a session for a user
 */
export async function createSession(userId: string, expiresInDays: number = parseInt(process.env.AUTH_SESSION_EXPIRY_DAYS || '7')) {
  // Generate a session token
  const sessionToken = generateSessionToken();

  // Calculate expiration time
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiresInDays);

  // Create the session
  const { data, error } = await supabaseAdmin
    .from('user_sessions')
    .insert({
      user_id: userId,
      session_token: sessionToken,
      expires_at: expiresAt.toISOString(),
    })
    .select();

  if (error) {
    console.error('Error creating session:', error);
    throw new Error('Failed to create session');
  }

  return {
    sessionToken,
    expiresAt,
    userId,
  };
}

/**
 * Get a session by token
 */
export async function getSessionByToken(token: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('user_sessions')
      .select('*')
      .eq('session_token', token)
      .gte('expires_at', new Date().toISOString())
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No session found
        return null;
      }
      console.error('Error getting session by token:', error);
      return null; // Return null instead of throwing an error
    }

    return data;
  } catch (error) {
    console.error('Exception getting session by token:', error);
    return null; // Return null instead of throwing an error
  }
}

/**
 * Delete a session
 */
export async function deleteSession(token: string) {
  const { error } = await supabaseAdmin
    .from('user_sessions')
    .delete()
    .eq('session_token', token);

  if (error) {
    console.error('Error deleting session:', error);
    throw new Error('Failed to delete session');
  }

  return true;
}

/**
 * Mark a user's email as verified
 */
export async function markEmailAsVerified(email: string) {
  const { error } = await supabaseAdmin
    .from('custom_users')
    .update({ email_verified: true })
    .eq('email', email);

  if (error) {
    console.error('Error marking email as verified:', error);
    throw new Error('Failed to mark email as verified');
  }

  return true;
}

/**
 * Update a user's profile
 */
export async function updateUserProfile(userId: string, firstName: string, lastName: string) {
  const { error } = await supabaseAdmin
    .from('custom_users')
    .update({
      first_name: firstName,
      last_name: lastName,
    })
    .eq('id', userId);

  if (error) {
    console.error('Error updating user profile:', error);
    throw new Error('Failed to update user profile');
  }

  return true;
}

/**
 * Update a user's email
 */
export async function updateUserEmail(userId: string, email: string) {
  const { error } = await supabaseAdmin
    .from('custom_users')
    .update({
      email,
      email_verified: false, // Require verification of the new email
    })
    .eq('id', userId);

  if (error) {
    console.error('Error updating user email:', error);
    throw new Error('Failed to update user email');
  }

  return true;
}

/**
 * Update a user's password
 */
export async function updateUserPassword(userId: string, password: string) {
  // Hash the new password
  const passwordHash = await hashPassword(password);

  const { error } = await supabaseAdmin
    .from('custom_users')
    .update({
      password_hash: passwordHash,
    })
    .eq('id', userId);

  if (error) {
    console.error('Error updating user password:', error);
    throw new Error('Failed to update user password');
  }

  return true;
}
