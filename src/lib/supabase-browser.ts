import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

// Define the URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a singleton instance for the browser
let supabaseInstance: ReturnType<typeof createClient<Database>> | null = null;

// Function to get the Supabase client (singleton pattern)
export function getSupabaseClient() {
  if (typeof window === 'undefined') {
    throw new Error('getSupabaseClient should only be called in browser context');
  }

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase URL or Anon Key is not set');
    throw new Error('Supabase configuration error: Missing credentials');
  }

  if (supabaseInstance) return supabaseInstance;

  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  });

  return supabaseInstance;
}

// For backward compatibility
export const supabase = typeof window !== 'undefined' ? getSupabaseClient() : null;
