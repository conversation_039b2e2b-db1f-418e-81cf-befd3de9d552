import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a file size in bytes to a human-readable string
 * @param bytes The file size in bytes
 * @returns A formatted string (e.g., "1.5 MB")
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * Format a currency value
 * @param value The value to format
 * @param currency The currency code (e.g., 'USD')
 * @returns A formatted currency string (e.g., "$1,234.56")
 */
export function formatCurrency(value: number, currency: string = 'USD'): string {
  // Define currency symbols
  const currencySymbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    CAD: 'CA$',
    AUD: 'A$',
    CNY: '¥',
    INR: '₹',
  };

  // Get the symbol or use the currency code if no symbol is defined
  const symbol = currencySymbols[currency] || currency;

  // Format the value
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    currencyDisplay: 'symbol',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  // For currencies without symbols in our map, we'll use the formatted value
  if (!currencySymbols[currency]) {
    return formatter.format(value);
  }

  // For currencies with symbols, we'll use our symbol and format the number
  return symbol + value.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
}
