/**
 * Utility functions for making API calls
 */

/**
 * Make an API call to a Next.js API route
 * @param endpoint The API endpoint to call (e.g., '/api/documents')
 * @param options Fetch options
 * @returns The response data
 */
export async function fetchFromApi(endpoint: string, options?: RequestInit) {
  const response = await fetch(endpoint, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `API call failed with status ${response.status}`);
  }

  return response.json();
}

/**
 * Get data from an API endpoint
 * @param endpoint The API endpoint to call
 * @returns The response data
 */
export async function getData(endpoint: string) {
  return fetchFromApi(endpoint);
}

/**
 * Post data to an API endpoint
 * @param endpoint The API endpoint to call
 * @param data The data to post
 * @returns The response data
 */
export async function postData(endpoint: string, data: any) {
  return fetchFrom<PERSON>pi(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

/**
 * Put data to an API endpoint
 * @param endpoint The API endpoint to call
 * @param data The data to put
 * @returns The response data
 */
export async function putData(endpoint: string, data: any) {
  return fetchFromApi(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

/**
 * Delete data from an API endpoint
 * @param endpoint The API endpoint to call
 * @returns The response data
 */
export async function deleteData(endpoint: string) {
  return fetchFromApi(endpoint, {
    method: 'DELETE',
  });
}
