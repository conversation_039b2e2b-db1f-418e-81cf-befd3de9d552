import Stripe from 'stripe';

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-03-31.basil',
});

export default stripe;

// Define subscription plan IDs
export const SUBSCRIPTION_PLANS = {
  PREMIUM: process.env.STRIPE_PREMIUM_PLAN_ID || 'price_premium',
};

// Helper function to format price
export const formatPrice = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(amount / 100);
};

// Helper function to get subscription plan details
export const getSubscriptionPlanDetails = (planId: string) => {
  switch (planId) {
    case SUBSCRIPTION_PLANS.PREMIUM:
      return {
        name: 'Legacy Preserver',
        price: 2999, // $29.99
        features: [
          'Unlimited asset inventory',
          '5GB vault storage for documents',
          '100 time capsules (up to 10GB)',
          'Up to 5 trustees',
          'Unlimited contacts & wishes',
          'Unlimited service sunset',
          'Priority customer support',
          'Unlimited will advice',
        ],
      };
    default:
      return {
        name: 'Essential Legacy',
        price: 0, // Free
        features: [
          'Unlimited asset inventory',
          'Single document storage',
          'Up to 1 trustee',
          'Basic will advice',
          'Unlimited contacts & wishes',
          'Unlimited service sunset',
        ],
      };
  }
};
