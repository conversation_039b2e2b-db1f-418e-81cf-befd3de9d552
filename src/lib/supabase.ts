import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

// Define the URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a singleton instance for the browser
let supabaseInstance: any = null;
let supabaseAdminInstance: any = null;

// Function to get the Supabase client (singleton pattern)
export function getSupabaseClient() {
  if (supabaseInstance) return supabaseInstance;

  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  });

  return supabaseInstance;
}

// Function to get the Supabase admin client (singleton pattern)
export function getSupabaseAdminClient() {
  // Only allow admin client in server context
  if (typeof window !== 'undefined') {
    console.error('Attempted to use admin client in browser context');
    throw new Error('Admin client can only be used on the server');
  }

  if (supabaseAdminInstance) return supabaseAdminInstance;

  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  supabaseAdminInstance = createClient(
    supabaseUrl,
    serviceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  return supabaseAdminInstance;
}

// For backward compatibility
export const supabase = getSupabaseClient();

// Only export supabaseAdmin in server contexts
if (typeof window === 'undefined') {
  // This will only be executed on the server
  exports.supabaseAdmin = getSupabaseAdminClient();
}
