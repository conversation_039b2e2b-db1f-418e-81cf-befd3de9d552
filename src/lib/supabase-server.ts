import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

// Define the URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Create a singleton instance for the server
let supabaseAdminInstance: ReturnType<typeof createClient<Database>> | null = null;

// Function to get the Supabase admin client (singleton pattern)
export function getSupabaseAdminClient() {
  // Only allow admin client in server context
  if (typeof window !== 'undefined') {
    console.error('Attempted to use admin client in browser context');
    throw new Error('Admin client can only be used on the server');
  }

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Supabase URL or Service Key is not set');
    throw new Error('Database configuration error: Missing Supabase credentials');
  }

  if (supabaseAdminInstance) return supabaseAdminInstance;

  supabaseAdminInstance = createClient<Database>(
    supabaseUrl,
    supabaseServiceKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  return supabaseAdminInstance;
}

// Export the admin client for server-side use
export const supabaseAdmin = getSupabaseAdminClient();
