"use client";

import React from 'react';
import { Power, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const ServiceSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Power className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Service Sunset</h3>
            <p className="feature-card-subtitle">Manage online services</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <Power className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Manage Services</h4>
        <p className="feature-card-empty-description text-blue-700">
          Track online services that should be closed after your passing.
        </p>
        <Link
          href="/service-sunset"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Manage Services
        </Link>
      </div>
    </div>
  );
};

export default ServiceSummary;
