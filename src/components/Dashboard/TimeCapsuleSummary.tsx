"use client";

import React from 'react';
import { Clock, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const TimeCapsuleSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Clock className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Time Capsule</h3>
            <p className="feature-card-subtitle">Future messages for loved ones</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <Clock className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Create Time Capsules</h4>
        <p className="feature-card-empty-description text-blue-700">
          Create messages to be delivered to your loved ones in the future.
        </p>
        <Link
          href="/time-capsule"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <Clock className="mr-2 h-4 w-4" />
          Manage Time Capsules
        </Link>
      </div>
    </div>
  );
};

export default TimeCapsuleSummary;
