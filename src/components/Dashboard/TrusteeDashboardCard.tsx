"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Users, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

interface TrusteeDashboardCardProps {
  userId: string;
}

export default function TrusteeDashboardCard({ userId }: TrusteeDashboardCardProps) {
  const [isTrustee, setIsTrustee] = useState<boolean | null>(null);
  const [hasPendingInvitations, setHasPendingInvitations] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (userId) {
      checkTrusteeStatus();
    }
  }, [userId]);

  const checkTrusteeStatus = async () => {
    try {
      setIsLoading(true);

      // Use the API endpoint to check trustee status
      const response = await fetch('/api/trustees/check-status');

      if (!response.ok) {
        throw new Error('Failed to check trustee status');
      }

      const data = await response.json();
      setIsTrustee(data.isTrustee);
      setHasPendingInvitations(data.hasPendingInvitations || false);
    } catch (error: any) {
      console.error('Error checking trustee status:', error);
      // Don't show an error toast as this is not critical
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="feature-card">
      <CardHeader className="pb-3">
        <div className="flex items-center">
          <div className="feature-card-icon bg-purple-100 mr-3">
            <Users className="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <CardTitle className="text-xl">Trustee Dashboard</CardTitle>
            <CardDescription>
              Manage your responsibilities as a trustee
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <div className="animate-spin h-6 w-6 border-4 border-purple-500 border-t-transparent rounded-full"></div>
          </div>
        ) : isTrustee ? (
          <div className="bg-purple-50 p-4 rounded-md mb-4">
            <p className="text-purple-800 text-sm">
              You are a trustee for one or more people. Access your trustee dashboard to manage your responsibilities.
            </p>
          </div>
        ) : hasPendingInvitations ? (
          <div className="bg-amber-50 p-4 rounded-md mb-4">
            <p className="text-amber-800 text-sm">
              You have pending trustee invitations that need to be completed. Please complete the process to become a trustee.
            </p>
          </div>
        ) : (
          <div className="bg-gray-50 p-4 rounded-md mb-4">
            <p className="text-gray-700 text-sm">
              You are not currently a trustee for anyone. If someone has invited you to be their trustee, please check your email for an invitation.
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          asChild
          variant={isTrustee || hasPendingInvitations ? "default" : "outline"}
          className="w-full"
        >
          <Link href={isTrustee ? "/trustee/dashboard" : hasPendingInvitations ? "/trustee/complete" : "/trustee/not-a-trustee"}>
            {isTrustee ? "Access Trustee Dashboard" : hasPendingInvitations ? "Complete Trustee Setup" : "Learn About Being a Trustee"}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
