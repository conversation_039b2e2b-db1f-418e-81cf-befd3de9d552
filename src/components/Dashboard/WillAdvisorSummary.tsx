"use client";

import React from 'react';
import Link from 'next/link';
import { FileSignature } from 'lucide-react';

const WillAdvisorSummary = () => {

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <FileSignature className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Will Advisor</h3>
            <p className="feature-card-subtitle">
              Not started
            </p>
          </div>
        </div>
      </div>

      <div className="feature-card-empty bg-blue-50">
        <FileSignature className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Will Planning Advice</h4>
        <p className="feature-card-empty-description text-blue-700">
          Get expert guidance and recommendations for creating your legal will.
        </p>
        <Link
          href="/will-advisor"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          Get Advice
        </Link>
      </div>
    </div>
  );
};

export default WillAdvisorSummary;
