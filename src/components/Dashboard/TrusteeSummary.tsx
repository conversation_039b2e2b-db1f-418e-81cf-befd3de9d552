"use client";

import React from 'react';
import { Users, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const TrusteeSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Users className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Trustees</h3>
            <p className="feature-card-subtitle">Manage your trusted contacts</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <Users className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Manage Trustees</h4>
        <p className="feature-card-empty-description text-blue-700">
          Designate trusted individuals to handle your digital legacy.
        </p>
        <Link
          href="/trustees"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <Users className="mr-2 h-4 w-4" />
          Manage Trustees
        </Link>
      </div>
    </div>
  );
};

export default TrusteeSummary;
