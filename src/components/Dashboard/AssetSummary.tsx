"use client";

import React from 'react';
import { Package, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const AssetSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Package className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Assets</h3>
            <p className="feature-card-subtitle">Track your valuable assets</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <Package className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Manage Your Assets</h4>
        <p className="feature-card-empty-description text-blue-700">
          Track your physical and digital assets for your legacy plan.
        </p>
        <Link
          href="/assets"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Manage Assets
        </Link>
      </div>
    </div>
  );
};

export default AssetSummary;
