"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase-browser';

interface AuthErrorAlertProps {
  onDismiss: () => void;
}

const AuthErrorAlert: React.FC<AuthErrorAlertProps> = ({ onDismiss }) => {
  const [hasSupabaseSession, setHasSupabaseSession] = useState<boolean | null>(null);

  useEffect(() => {
    async function checkSupabaseAuth() {
      const { data } = await supabase.auth.getSession();
      setHasSupabaseSession(!!data.session);
    }

    checkSupabaseAuth();
  }, []);

  if (hasSupabaseSession === true) {
    return null; // Don't show the alert if the user has a valid Supabase session
  }

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 relative">
      <button
        onClick={onDismiss}
        className="absolute top-2 right-2 text-amber-500 hover:text-amber-700"
        aria-label="Dismiss"
      >
        ×
      </button>

      <div className="flex items-start">
        <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 mr-3 flex-shrink-0" />
        <div>
          <h3 className="font-medium text-amber-800">Authentication Issue Detected</h3>
          <p className="text-sm text-amber-700 mt-1 mb-3">
            We've detected an issue with your authentication that may cause API errors.
            Some dashboard features might not load correctly.
          </p>
          <Link href="/auth-fix">
            <Button size="sm" variant="outline" className="bg-white border-amber-300 text-amber-700 hover:bg-amber-100">
              Fix Authentication Issues
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AuthErrorAlert;
