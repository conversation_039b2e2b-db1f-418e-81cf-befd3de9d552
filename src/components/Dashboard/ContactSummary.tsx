"use client";

import React from 'react';
import { Phone, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const ContactSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Phone className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Contacts</h3>
            <p className="feature-card-subtitle">Important contact information</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <Phone className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Manage Contacts</h4>
        <p className="feature-card-empty-description text-blue-700">
          Store important contact information for your trustees.
        </p>
        <Link
          href="/contacts"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Manage Contacts
        </Link>
      </div>
    </div>
  );
};

export default ContactSummary;
