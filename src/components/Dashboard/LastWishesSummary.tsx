"use client";

import React from 'react';
import { Heart, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const LastWishesSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Heart className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Last Wishes</h3>
            <p className="feature-card-subtitle">Your final wishes and instructions</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <Heart className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Document Your Wishes</h4>
        <p className="feature-card-empty-description text-blue-700">
          Record your final wishes to share with your trustees.
        </p>
        <Link
          href="/last-wishes"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Manage Last Wishes
        </Link>
      </div>
    </div>
  );
};

export default LastWishesSummary;
