"use client";

import React from 'react';
import { FileText, ArrowR<PERSON>, PlusCircle } from 'lucide-react';
import Link from 'next/link';

const VaultSummary = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Digital Vault</h3>
            <p className="feature-card-subtitle">Store important documents</p>
          </div>
        </div>

      </div>

      <div className="feature-card-empty bg-blue-50">
        <FileText className="feature-card-empty-icon text-blue-500" />
        <h4 className="feature-card-empty-title text-blue-900">Document Storage</h4>
        <p className="feature-card-empty-description text-blue-700">
          Securely store important documents in your digital vault.
        </p>
        <Link
          href="/vault"
          className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Upload Document
        </Link>
      </div>
    </div>
  );
};

export default VaultSummary;
