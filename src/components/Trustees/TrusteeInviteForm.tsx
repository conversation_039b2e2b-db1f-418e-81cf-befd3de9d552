"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import '@/styles/phone-input.css';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

const trusteeSchema = z.object({
  firstName: z.string().min(2, { message: 'First name must be at least 2 characters' }),
  lastName: z.string().min(2, { message: 'Last name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().optional(),
  permissions: z.array(z.string()).min(1, { message: 'Please select at least one permission' }),
  message: z.string().optional(),
}).refine((data) => {
  // Either email or phone must be provided
  return !!data.email || (!!data.phone && data.phone.length > 3);
}, {
  message: "Either email or phone number must be provided",
  path: ["phone"],
});

type FormValues = z.infer<typeof trusteeSchema>;

interface TrusteeInviteFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  isEditing?: boolean;
  defaultValues?: FormValues;
  onUpdate?: (data: FormValues) => void;
}

export default function TrusteeInviteForm({
  onSuccess,
  onCancel,
  isEditing = false,
  defaultValues,
  onUpdate
}: TrusteeInviteFormProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const permissionOptions = [
    { id: 'assets', label: 'Assets', description: 'Digital and physical assets inventory' },
    { id: 'vault', label: 'Digital Vault', description: 'Important documents and files' },
    { id: 'contacts', label: 'Emergency Contacts', description: 'People to notify after your passing' },
    { id: 'services', label: 'Service Sunset', description: 'Services to cancel after your passing' },
  ];

  const form = useForm<FormValues>({
    resolver: zodResolver(trusteeSchema),
    defaultValues: defaultValues || {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      permissions: ['assets', 'vault', 'contacts', 'services'],
      message: '',
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      if (!user) {
        toast.error('You must be logged in to manage trustees');
        return;
      }

      setIsSubmitting(true);

      // If editing, use the update function
      if (isEditing && onUpdate) {
        await onUpdate(data);
        setIsSubmitting(false);
        return;
      }

      // Otherwise, create a new trustee
      const response = await fetch('/api/trustees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          first_name: data.firstName.charAt(0).toUpperCase() + data.firstName.slice(1),
          last_name: data.lastName.charAt(0).toUpperCase() + data.lastName.slice(1),
          permissions: data.permissions,
          phone: data.phone || '',
          message: data.message || ''
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add trustee');
      }

      const trustee = await response.json();

      toast.success(`Trustee added successfully`);

      if (onSuccess) {
        onSuccess();
      }

      // Reset form after submission
      form.reset({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        permissions: ['assets', 'vault', 'contacts', 'services'],
        message: ''
      });
    } catch (error: any) {
      console.error('Error managing trustee:', error);

      if (error.message.includes('duplicate')) {
        toast.error('This person is already added as your trustee');
      } else {
        toast.error(error.message || 'Error managing trustee');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name*</FormLabel>
                <FormControl>
                  <Input placeholder="First name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Last name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email*</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Email address"
                  {...field}
                  disabled={isEditing}
                />
              </FormControl>
              <FormDescription>
                {isEditing
                  ? 'Email cannot be changed. To change the email, delete this trustee and add a new one.'
                  : 'Your trustee will receive an invitation at this email address'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <div className="phone-input-container">
                  <PhoneInput
                    country={'us'}
                    value={field.value}
                    onChange={(phone) => field.onChange(phone)}
                    inputClass="phone-input"
                    containerClass="phone-container"
                    buttonClass="phone-dropdown-button"
                    dropdownClass="phone-dropdown"
                    searchClass="phone-search"
                    enableSearch={true}
                    disableSearchIcon={false}
                    countryCodeEditable={false}
                    preferredCountries={['us', 'ca', 'gb', 'au']}
                    autoFormat={true}
                    inputProps={{
                      name: 'phone',
                      required: false,
                    }}
                  />
                </div>
              </FormControl>
              <FormDescription>
                Optional phone number to contact your trustee
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />


        <FormField
          control={form.control}
          name="permissions"
          render={() => (
            <FormItem>
              <div className="mb-2">
                <FormLabel>Permissions*</FormLabel>
                <FormDescription>
                  Select what information this trustee will have access to after your passing
                </FormDescription>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {permissionOptions.map((option) => (
                  <FormField
                    key={option.id}
                    control={form.control}
                    name="permissions"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              const newValue = checked
                                ? [...field.value, option.id]
                                : field.value.filter((value) => value !== option.id);
                              field.onChange(newValue);
                            }}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>{option.label}</FormLabel>
                          <FormDescription>
                            {option.description}
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                ))}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Personal Message (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Add a personal message to your invitation"
                  className="resize-none h-24"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                This message will be included in the invitation email
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-2">
          {onCancel ? (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          ) : (
            <Button type="button" variant="outline" onClick={() => router.push('/trustees')}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                {isEditing ? 'Updating Trustee...' : 'Adding Trustee...'}
              </>
            ) : (
              isEditing ? 'Update Trustee' : 'Add Trustee'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
