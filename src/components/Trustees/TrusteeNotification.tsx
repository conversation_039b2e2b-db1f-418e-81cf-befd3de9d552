"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { AlertTriangle, CheckCircle, Info, Mail, Shield } from 'lucide-react';

interface TrusteeNotificationProps {
  trusteeId: string;
  trusteeName: string;
  trusteeEmail: string;
}

export default function TrusteeNotification({ trusteeId, trusteeName, trusteeEmail }: TrusteeNotificationProps) {
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [includeContacts, setIncludeContacts] = useState(true);
  const [includeServices, setIncludeServices] = useState(true);
  const [includeAssets, setIncludeAssets] = useState(true);
  const [includeDocuments, setIncludeDocuments] = useState(true);
  const [isSending, setIsSending] = useState(false);

  const handleSendNotification = async () => {
    try {
      if (!user) {
        toast.error('You must be logged in to send notifications');
        return;
      }

      setIsSending(true);

      // In a real implementation, this would send an email to the trustee
      // with the selected information and instructions
      
      // For now, we'll just simulate the notification by creating a record
      const { error } = await supabase
        .from('trustee_notifications')
        .insert({
          user_id: user.id,
          trustee_id: trusteeId,
          message: message,
          include_contacts: includeContacts,
          include_services: includeServices,
          include_assets: includeAssets,
          include_documents: includeDocuments,
          status: 'sent',
        });

      if (error) throw error;

      toast.success(`Notification sent to ${trusteeName}`);
      setMessage('');
    } catch (error: any) {
      console.error('Error sending notification:', error);
      toast.error(`Failed to send notification: ${error.message}`);
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Mail className="h-5 w-5 text-primary mr-2" />
          Notify {trusteeName}
        </CardTitle>
        <CardDescription>
          Send information and instructions to this trustee
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-600 mr-2 mt-0.5" />
            <div>
              <h4 className="font-medium text-amber-800">Important Notice</h4>
              <p className="text-sm text-amber-700 mt-1">
                This will send a real notification to {trusteeName} at {trusteeEmail}. 
                Only use this feature in case of emergency or for testing purposes with the trustee's knowledge.
              </p>
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="message">Message to Trustee</Label>
          <Textarea
            id="message"
            placeholder="Enter instructions or information for your trustee..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="h-32"
          />
        </div>

        <div className="space-y-2">
          <Label>Include the following information:</Label>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-contacts" 
              checked={includeContacts}
              onCheckedChange={(checked) => setIncludeContacts(checked as boolean)}
            />
            <Label htmlFor="include-contacts" className="cursor-pointer">
              Emergency Contacts
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-services" 
              checked={includeServices}
              onCheckedChange={(checked) => setIncludeServices(checked as boolean)}
            />
            <Label htmlFor="include-services" className="cursor-pointer">
              Service Sunset List
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-assets" 
              checked={includeAssets}
              onCheckedChange={(checked) => setIncludeAssets(checked as boolean)}
            />
            <Label htmlFor="include-assets" className="cursor-pointer">
              Digital Assets
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-documents" 
              checked={includeDocuments}
              onCheckedChange={(checked) => setIncludeDocuments(checked as boolean)}
            />
            <Label htmlFor="include-documents" className="cursor-pointer">
              Vault Documents
            </Label>
          </div>
        </div>

        <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800">What Your Trustee Will Receive</h4>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li className="flex items-start">
                  <CheckCircle className="h-3 w-3 text-blue-500 mr-1 mt-1" />
                  <span>Your personal message and instructions</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-3 w-3 text-blue-500 mr-1 mt-1" />
                  <span>A secure link to access the selected information</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-3 w-3 text-blue-500 mr-1 mt-1" />
                  <span>Step-by-step guidance for handling your digital legacy</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleSendNotification} 
          disabled={isSending}
          className="w-full"
        >
          {isSending ? (
            <>
              <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
              Sending Notification...
            </>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              Send Notification to {trusteeName}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
