
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';
import { useAuth } from '@/context/auth-context';
import { Eye, EyeOff } from 'lucide-react';
import { supabase } from '@/lib/supabase';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
});

type FormValues = z.infer<typeof formSchema>;

const LoginForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [trusteeId, setTrusteeId] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Get email and trustee ID from URL parameters
  useEffect(() => {
    if (!searchParams) return;

    const email = searchParams.get('email');
    const trustee = searchParams.get('trustee');

    if (trustee) {
      setTrusteeId(trustee);
    }

    if (email) {
      form.setValue('email', email);
    }
  }, [searchParams, form]);

  const onSubmit = async (data: FormValues) => {
    try {
      await signIn(data.email, data.password);

      // If there's a trustee invitation ID, handle it after successful login
      if (trusteeId) {
        try {
          // Check if the user has accepted the terms
          const hasAcceptedTerms = localStorage.getItem('trusteeTermsAccepted') === 'true';

          if (!hasAcceptedTerms) {
            console.log('User has not accepted trustee terms yet, storing ID for later activation');
            // Store the trustee ID in localStorage for the TrusteeInvitationChecker to handle
            localStorage.setItem('pendingTrusteeId', trusteeId);
            toast.success('Signed in successfully! Please complete your trustee invitation.');
            router.push('/dashboard');
            return;
          }

          // Get the current user
          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            // Update the trustee record
            const { error } = await supabase
              .from('trustees')
              .update({
                trustee_user_id: user.id,
                status: 'active',
                invitation_accepted_at: new Date().toISOString(),
              })
              .eq('id', trusteeId);

            if (error) throw error;

            // Clear the terms accepted flag
            localStorage.removeItem('trusteeTermsAccepted');

            toast.success('Trustee invitation accepted successfully');
            router.push('/dashboard');
            return;
          }
        } catch (trusteeError) {
          console.error('Error accepting trustee invitation:', trusteeError);
          // Continue with normal login flow even if trustee acceptance fails
        }
      }

      // The redirect is handled in the AuthContext if no trustee ID
    } catch (error) {
      console.error('Login error:', error);
      // Error is handled in the AuthContext
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    type="email"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel>Password</FormLabel>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-primary hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="••••••••"
                      type={showPassword ? "text" : "password"}
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400 hover:text-gray-500"
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div>
            <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <span className="flex items-center">
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                  Signing in...
                </span>
              ) : (
                'Sign In'
              )}
            </Button>
          </div>
        </form>
      </Form>

      <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          Don't have an account?{' '}
          <Link href="/register" className="text-primary hover:underline">
            Sign up
          </Link>
        </p>
      </div>
    </div>
  );
};

export default LoginForm;
