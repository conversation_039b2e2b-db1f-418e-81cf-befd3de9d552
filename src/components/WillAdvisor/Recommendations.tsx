"use client";

import React from 'react';
import Link from 'next/link';
import { useWillAdvisor } from '@/context/will-advisor-context';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Info, ArrowRight, RefreshCw } from 'lucide-react';

export default function Recommendations() {
  const { state, resetAdvisor } = useWillAdvisor();
  const { recommendations } = state;

  // Group recommendations by priority
  const highPriority = recommendations.filter(rec => rec.priority === 'high');
  const mediumPriority = recommendations.filter(rec => rec.priority === 'medium');
  const lowPriority = recommendations.filter(rec => rec.priority === 'low');

  const getPriorityIcon = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <Info className="h-5 w-5 text-amber-500" />;
      case 'low':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
  };

  const getPriorityClass = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50';
      case 'medium':
        return 'border-amber-200 bg-amber-50';
      case 'low':
        return 'border-green-200 bg-green-50';
    }
  };

  const renderRecommendationGroup = (
    recommendations: typeof state.recommendations,
    title: string,
    description: string
  ) => {
    if (recommendations.length === 0) return null;

    return (
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <div className="space-y-4">
          {recommendations.map(recommendation => (
            <Card 
              key={recommendation.id} 
              className={`border ${getPriorityClass(recommendation.priority)}`}
            >
              <CardHeader className="pb-2">
                <div className="flex items-center">
                  {getPriorityIcon(recommendation.priority)}
                  <CardTitle className="ml-2 text-lg">{recommendation.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{recommendation.description}</p>
              </CardContent>
              {recommendation.actionLink && (
                <CardFooter>
                  <Button asChild>
                    <Link href={recommendation.actionLink}>
                      {recommendation.actionText || 'Take Action'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Your Will Recommendations</h2>
        <p className="text-gray-600">
          Based on your answers, we've prepared the following recommendations to help you create a comprehensive will.
        </p>
      </div>

      {recommendations.length === 0 ? (
        <Card className="text-center p-8">
          <CardContent>
            <div className="flex flex-col items-center">
              <Info className="h-12 w-12 text-blue-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">No Recommendations</h3>
              <p className="text-gray-600 mb-6">
                Based on your answers, we don't have any specific recommendations at this time.
                This could be because you already have a comprehensive estate plan in place.
              </p>
              <Button onClick={resetAdvisor}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Start Over
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {renderRecommendationGroup(
            highPriority,
            "High Priority Actions",
            "These items require immediate attention to ensure your estate is properly protected."
          )}
          
          {renderRecommendationGroup(
            mediumPriority,
            "Recommended Actions",
            "These items will enhance your estate plan and provide additional protection."
          )}
          
          {renderRecommendationGroup(
            lowPriority,
            "Consider These Options",
            "These items may be beneficial depending on your specific circumstances."
          )}
          
          <div className="flex justify-center mt-8">
            <Button onClick={resetAdvisor} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Start Over
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
