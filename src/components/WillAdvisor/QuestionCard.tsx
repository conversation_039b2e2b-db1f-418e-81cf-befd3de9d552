"use client";

import React from 'react';
import { Question, Answer } from '@/types/will-advisor.types';
import { useWillAdvisor } from '@/context/will-advisor-context';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { HelpCircle, ArrowLeft, ArrowRight } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface QuestionCardProps {
  question: Question;
}

export default function QuestionCard({ question }: QuestionCardProps) {
  const { 
    setAnswer, 
    nextQuestion, 
    previousQuestion, 
    getAnswerByQuestionId,
    state
  } = useWillAdvisor();

  const currentAnswer = getAnswerByQuestionId(question.id);

  const handleSingleSelectChange = (value: string) => {
    setAnswer({
      questionId: question.id,
      value,
    });
  };

  const handleMultiSelectChange = (value: string, checked: boolean) => {
    const currentValues = Array.isArray(currentAnswer?.value) 
      ? [...currentAnswer.value] 
      : [];
    
    const newValues = checked
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);
    
    setAnswer({
      questionId: question.id,
      value: newValues,
    });
  };

  const handleBooleanChange = (value: boolean) => {
    setAnswer({
      questionId: question.id,
      value,
    });
  };

  const handleTextChange = (value: string) => {
    setAnswer({
      questionId: question.id,
      value,
    });
  };

  const handleNumberChange = (value: string) => {
    const numberValue = parseInt(value, 10);
    if (!isNaN(numberValue)) {
      setAnswer({
        questionId: question.id,
        value: numberValue,
      });
    }
  };

  const isAnswered = () => {
    if (!currentAnswer) return false;
    
    if (Array.isArray(currentAnswer.value)) {
      return currentAnswer.value.length > 0;
    }
    
    return currentAnswer.value !== undefined && currentAnswer.value !== '';
  };

  const renderQuestionInput = () => {
    switch (question.type) {
      case 'single-select':
        return (
          <RadioGroup
            value={currentAnswer?.value as string || ''}
            onValueChange={handleSingleSelectChange}
            className="space-y-3"
          >
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.id} />
                <Label htmlFor={option.id} className="cursor-pointer">{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
        );
      
      case 'multi-select':
        return (
          <div className="space-y-3">
            {question.options?.map((option) => {
              const isChecked = Array.isArray(currentAnswer?.value) 
                ? currentAnswer.value.includes(option.value)
                : false;
              
              return (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox 
                    id={option.id} 
                    checked={isChecked}
                    onCheckedChange={(checked) => 
                      handleMultiSelectChange(option.value, checked as boolean)
                    }
                  />
                  <Label htmlFor={option.id} className="cursor-pointer">{option.label}</Label>
                </div>
              );
            })}
          </div>
        );
      
      case 'boolean':
        return (
          <div className="flex flex-col space-y-4">
            <div className="flex items-center space-x-2">
              <RadioGroupItem 
                value="true" 
                id={`${question.id}_yes`}
                checked={currentAnswer?.value === true}
                onClick={() => handleBooleanChange(true)}
              />
              <Label htmlFor={`${question.id}_yes`} className="cursor-pointer">Yes</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem 
                value="false" 
                id={`${question.id}_no`}
                checked={currentAnswer?.value === false}
                onClick={() => handleBooleanChange(false)}
              />
              <Label htmlFor={`${question.id}_no`} className="cursor-pointer">No</Label>
            </div>
          </div>
        );
      
      case 'text':
        return (
          <Input
            type="text"
            value={currentAnswer?.value as string || ''}
            onChange={(e) => handleTextChange(e.target.value)}
            placeholder="Type your answer here..."
          />
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={currentAnswer?.value as number || ''}
            onChange={(e) => handleNumberChange(e.target.value)}
            placeholder="Enter a number..."
            min={0}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">{question.text}</CardTitle>
          {question.helpText && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <HelpCircle className="h-5 w-5 text-gray-400" />
                    <span className="sr-only">Help</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <p>{question.helpText}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        {question.required && (
          <CardDescription>This question is required</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        {renderQuestionInput()}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={previousQuestion}
          disabled={state.currentStep === 0}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextQuestion}
          disabled={question.required && !isAnswered()}
        >
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}
