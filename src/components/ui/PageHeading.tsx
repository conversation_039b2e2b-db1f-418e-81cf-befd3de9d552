"use client";

import React from 'react';

interface PageHeadingProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  showTrusteeEmphasis?: boolean;
  icon?: React.ReactNode;
}

const PageHeading: React.FC<PageHeadingProps> = ({
  title,
  description,
  actions,
  showTrusteeEmphasis = false,
  icon
}) => {
  return (
    <div className="mb-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center">
          {icon && <div className="mr-4 hidden sm:block">{icon}</div>}
          <div>
            <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl tracking-tight">{title}</h1>
            {description && (
              <p className="mt-2 text-md text-gray-600">{description}</p>
            )}
          </div>
        </div>
        {actions && <div className="mt-4 sm:mt-0 sm:ml-4">{actions}</div>}
      </div>


    </div>
  );
};

export default PageHeading;
