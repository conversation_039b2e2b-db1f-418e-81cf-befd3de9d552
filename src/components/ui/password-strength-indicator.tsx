"use client";

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

export function PasswordStrengthIndicator({
  password,
  className
}: PasswordStrengthIndicatorProps) {
  const [strength, setStrength] = useState({
    score: 0,
    label: 'Too weak',
    color: 'bg-red-500',
    percentage: 0
  });

  useEffect(() => {
    // Calculate password strength
    const calculateStrength = () => {
      if (!password) {
        return {
          score: 0,
          label: 'Too weak',
          color: 'bg-red-500',
          percentage: 0
        };
      }

      let score = 0;

      // Length check
      if (password.length >= 8) score += 1;
      if (password.length >= 12) score += 1;

      // Character type checks
      if (/[A-Z]/.test(password)) score += 1; // Has uppercase
      if (/[a-z]/.test(password)) score += 1; // Has lowercase
      if (/[0-9]/.test(password)) score += 1; // Has number
      if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char

      // Determine strength level
      let label = 'Too weak';
      let color = 'bg-red-500';
      let percentage = 0;

      if (score === 0) {
        label = 'Too weak';
        color = 'bg-red-500';
        percentage = 0;
      } else if (score <= 2) {
        label = 'Weak';
        color = 'bg-red-500';
        percentage = 25;
      } else if (score <= 4) {
        label = 'Medium';
        color = 'bg-yellow-500';
        percentage = 50;
      } else if (score <= 5) {
        label = 'Strong';
        color = 'bg-green-500';
        percentage = 75;
      } else {
        label = 'Very strong';
        color = 'bg-green-600';
        percentage = 100;
      }

      return {
        score,
        label,
        color,
        percentage
      };
    };

    setStrength(calculateStrength());
  }, [password]);

  return (
    <div className={cn("w-full space-y-2", className)}>
      <div className="flex justify-between items-center">
        <span className="text-xs text-gray-500">Password strength:</span>
        <span className={cn(
          "text-xs font-medium",
          strength.score <= 2 ? "text-red-500" :
          strength.score <= 4 ? "text-yellow-500" :
          "text-green-500"
        )}>
          {strength.label}
        </span>
      </div>

      <div className="h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
        <div
          className={cn("h-full transition-all duration-300", strength.color)}
          style={{ width: `${strength.percentage || 0}%` }}
        />
      </div>

      <div className="flex justify-between text-xs text-gray-400">
        <span>Weak</span>
        <span>Medium</span>
        <span>Strong</span>
      </div>
    </div>
  );
}
