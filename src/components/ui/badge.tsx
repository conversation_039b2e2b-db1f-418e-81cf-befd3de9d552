import * as React from "react"
import { type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { badgeCva } from "./utils"

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeCva> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeCva({ variant }), className)} {...props} />
  )
}

export { Badge }
