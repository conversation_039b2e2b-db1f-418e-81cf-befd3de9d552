export const SIDEBAR_COOKIE_NAME = "sidebar-state"
export const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 365 // 1 year
export const SIDEBAR_WIDTH = "240px"
export const SIDEBAR_WIDTH_MOBILE = "280px"
export const SIDEBAR_WIDTH_ICON = "48px"
export const SIDEBAR_KEYBOARD_SHORTCUT = "b"

export interface SidebarContextType {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean | ((value: boolean) => boolean)) => void
  isMobile: boolean
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  toggleSidebar: () => void
}
