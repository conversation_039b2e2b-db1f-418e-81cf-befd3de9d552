"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  SERVICE_CATEGORIES,
  SERVICE_PRIORITIES
} from '@/types/service-sunset.types';

// Define cancellation methods locally since they're not in the database schema
const CANCELLATION_METHODS = [
  { value: 'online', label: 'Online' },
  { value: 'phone', label: 'Phone' },
  { value: 'email', label: 'Email' },
  { value: 'mail', label: 'Mail' },
  { value: 'in_person', label: 'In Person' },
  { value: 'automatic', label: 'Automatic' },
];

const serviceSchema = z.object({
  name: z.string().min(2, { message: 'Service name must be at least 2 characters' }),
  category: z.string().min(1, { message: 'Please select a category' }),
  description: z.string().optional(),
  website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  account_number: z.string().optional(),
  username: z.string().optional(),
  password_hint: z.string().optional(),
  contact_info: z.string().optional(),
  cancellation_method: z.string().min(1, { message: 'Please select a cancellation method' }),
  cancellation_instructions: z.string().optional(),
  priority: z.string().min(1, { message: 'Please select a priority' }),
  auto_renewal: z.boolean().default(false),
  renewal_date: z.date().optional().nullable(),
  cost_per_period: z.number().optional().nullable(),
  period: z.string().optional(),
  notes: z.string().optional(),
});

type ServiceFormValues = z.infer<typeof serviceSchema>;

interface ServiceFormProps {
  onSubmit: (data: ServiceFormValues) => void;
  onCancel: () => void;
  defaultValues?: Partial<ServiceFormValues>;
}

export default function ServiceForm({ onSubmit, onCancel, defaultValues }: ServiceFormProps) {
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(serviceSchema),
    defaultValues: {
      name: defaultValues?.name || '',
      category: defaultValues?.category || '',
      description: defaultValues?.description || '',
      website: defaultValues?.website || '',
      account_number: defaultValues?.account_number || '',
      username: defaultValues?.username || '',
      password_hint: defaultValues?.password_hint || '',
      contact_info: defaultValues?.contact_info || '',
      cancellation_method: defaultValues?.cancellation_method || '',
      cancellation_instructions: defaultValues?.cancellation_instructions || '',
      priority: defaultValues?.priority || '',
      auto_renewal: defaultValues?.auto_renewal || false,
      renewal_date: defaultValues?.renewal_date ? new Date(defaultValues.renewal_date) : null,
      cost_per_period: defaultValues?.cost_per_period || null,
      period: defaultValues?.period || '',
      notes: defaultValues?.notes || '',
    },
  });

  const handleSubmit = (data: ServiceFormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Service Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Service Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Netflix, Spotify, Gym Membership, etc." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Category */}
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category*</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {SERVICE_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Website */}
          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input placeholder="https://example.com" {...field} />
                </FormControl>
                <FormDescription>
                  The website where the service can be managed or canceled
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Account Number */}
          <FormField
            control={form.control}
            name="account_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Number</FormLabel>
                <FormControl>
                  <Input placeholder="Account or membership number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Username */}
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input placeholder="Username or email used for login" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Hint */}
          <FormField
            control={form.control}
            name="password_hint"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password Hint</FormLabel>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <FormControl>
                        <Input placeholder="Hint to help remember the password" {...field} />
                      </FormControl>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-xs">
                        For security reasons, do not enter the actual password.
                        Instead, provide a hint that will help your trustee
                        locate or remember the password.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <FormDescription>
                  Do not enter the actual password, just a hint
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Contact Info */}
          <FormField
            control={form.control}
            name="contact_info"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Information</FormLabel>
                <FormControl>
                  <Input placeholder="Phone number, email, or address" {...field} />
                </FormControl>
                <FormDescription>
                  Contact information for canceling the service
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Cancellation Method */}
          <FormField
            control={form.control}
            name="cancellation_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cancellation Method*</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a method" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {CANCELLATION_METHODS.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Priority */}
          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Priority*</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a priority" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {SERVICE_PRIORITIES.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label} - {priority.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Auto Renewal */}
          <FormField
            control={form.control}
            name="auto_renewal"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Auto-Renewal</FormLabel>
                  <FormDescription>
                    This service automatically renews or charges on a recurring basis
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Renewal Date */}
          <FormField
            control={form.control}
            name="renewal_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Next Renewal Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={`w-full pl-3 text-left font-normal ${!field.value ? "text-muted-foreground" : ""}`}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value || undefined}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  When this service will next renew or charge
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Cost Per Period */}
          <FormField
            control={form.control}
            name="cost_per_period"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cost</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="0.00"
                    {...field}
                    onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))}
                  />
                </FormControl>
                <FormDescription>
                  How much this service costs per billing period
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Period */}
          <FormField
            control={form.control}
            name="period"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Billing Period</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a period" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Brief description of the service"
                  className="resize-none h-20"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Cancellation Instructions */}
        <FormField
          control={form.control}
          name="cancellation_instructions"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cancellation Instructions</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Step-by-step instructions for canceling this service"
                  className="resize-none h-32"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Provide detailed instructions to help your trustees cancel this service
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Notes */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Any additional information about this service"
                  className="resize-none h-20"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            Save Service
          </Button>
        </div>
      </form>
    </Form>
  );
}
