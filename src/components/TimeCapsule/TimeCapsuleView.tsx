
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Download, File } from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { TimeCapsule, TimeCapsuleMedia } from '@/types/database.types';

interface TimeCapsuleViewProps {
  capsule: TimeCapsule;
  onBack: () => void;
  accessCode?: string;
  isPublicView?: boolean;
}

const TimeCapsuleView: React.FC<TimeCapsuleViewProps> = ({ 
  capsule, 
  onBack,
  accessCode,
  isPublicView = false
}) => {
  const [media, setMedia] = useState<TimeCapsuleMedia[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(!isPublicView);

  useEffect(() => {
    // Check access code if this is a public view
    if (isPublicView) {
      if (accessCode === capsule.access_code) {
        setAuthenticated(true);
      } else {
        setAuthenticated(false);
        return;
      }
    }

    // Fetch media attachments
    const fetchMedia = async () => {
      if (!capsule?.id) return;
      
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('time_capsule_media')
          .select('*')
          .eq('capsule_id', capsule.id);
          
        if (error) throw error;
        
        if (data) {
          setMedia(data as TimeCapsuleMedia[]);
        }
      } catch (error) {
        console.error('Error fetching media:', error);
        toast.error('Failed to load attachments');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMedia();
  }, [capsule, accessCode, isPublicView]);

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-50';
      case 'delivered':
        return 'bg-green-50';
      case 'cancelled':
        return 'bg-gray-50';
      default:
        return '';
    }
  };

  // Get icon for media type
  const getMediaTypeIcon = (mediaType: string) => {
    return <File className="h-4 w-4 text-gray-500 mr-2" />;
  };

  // Handle file download
  const handleDownload = async (filePath: string, fileName: string) => {
    try {
      const { data, error } = await supabase.storage
        .from('time_capsule_media')
        .download(filePath);
        
      if (error) throw error;
      
      // Create a download link
      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Failed to download file');
    }
  };

  // If access check failed in public view
  if (isPublicView && !authenticated) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Access Denied</CardTitle>
          <CardDescription>
            The access code is incorrect or has expired.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>
            You need a valid access code to view this time capsule. Please check the link in the email you received.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      {!isPublicView && (
        <Button 
          variant="ghost" 
          className="mb-4" 
          onClick={onBack}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      )}
      
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{capsule.title}</CardTitle>
              <CardDescription>
                {isPublicView ? (
                  <>
                    From: {capsule.sender_name || 'Someone who cares about you'}
                    <br />
                    Delivered: {format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}
                  </>
                ) : (
                  <>
                    To: {capsule.recipient_name} ({capsule.recipient_email})
                    <br />
                    {capsule.status === 'scheduled' 
                      ? `To be delivered on: ${format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}` 
                      : `${capsule.status === 'delivered' ? 'Delivered' : 'Cancelled'} on: ${format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}`
                    }
                  </>
                )}
              </CardDescription>
            </div>
            
            {!isPublicView && (
              <Badge 
                variant="outline" 
                className={getStatusBadgeClass(capsule.status)}
              >
                {capsule.status.charAt(0).toUpperCase() + capsule.status.slice(1)}
              </Badge>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Message</h3>
            <div className="bg-gray-50 p-4 rounded min-h-[100px] whitespace-pre-wrap">
              {capsule.message || 'No message content'}
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Attachments</h3>
            {isLoading ? (
              <p className="text-gray-500">Loading attachments...</p>
            ) : media.length === 0 ? (
              <p className="text-gray-500">No attachments</p>
            ) : (
              <div className="space-y-2">
                {media.map((item) => (
                  <div 
                    key={item.id} 
                    className="flex items-center justify-between p-3 bg-gray-50 rounded"
                  >
                    <div className="flex items-center">
                      {getMediaTypeIcon(item.media_type)}
                      <div>
                        <p className="font-medium text-sm">{item.file_name}</p>
                        <p className="text-gray-500 text-xs">
                          {(item.file_size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownload(item.file_path, item.file_name)}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter>
          {isPublicView ? (
            <p className="text-sm text-gray-500">
              This time capsule was created for you and delivered on {format(new Date(capsule.delivery_date), 'MMMM d, yyyy')}
            </p>
          ) : (
            <Button variant="outline" onClick={onBack}>
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Time Capsules
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default TimeCapsuleView;
