
import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/integrations/supabase/client';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  Loader2,
  File,
  X,
  Upload
} from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format, addDays, isBefore, isAfter } from 'date-fns';
import { toast } from 'sonner';
import { generatePassword } from '@/utils/encryption';
import { TimeCapsule } from '@/types/database.types';

// Define the validation schema for the form
const formSchema = z.object({
  title: z.string().min(2, { message: 'Title must be at least 2 characters' }).max(100),
  message: z.string().max(5000, { message: 'Message must be less than 5000 characters' }).optional(),
  recipient_first_name: z.string().min(2, { message: 'First name must be at least 2 characters' }),
  recipient_last_name: z.string().min(2, { message: 'Last name must be at least 2 characters' }),
  recipient_email: z.string().email({ message: 'Please enter a valid email address' }),
  delivery_date: z.date({
    required_error: 'Please select a delivery date',
  }).refine(date => isAfter(date, addDays(new Date(), 1)), {
    message: 'Delivery date must be at least 1 day in the future',
  }),
  delivery_hour: z.string().min(1, { message: 'Please select a delivery hour' }),
});

type FormValues = z.infer<typeof formSchema>;

// Define props for the component
interface TimeCapsuleFormProps {
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  defaultValues?: Partial<TimeCapsule>;
}

const TimeCapsuleForm: React.FC<TimeCapsuleFormProps> = ({
  onSubmit: handleFormSubmit,
  onCancel,
  defaultValues
}) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize the form with default values or existing capsule data
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: defaultValues?.title || '',
      message: defaultValues?.message || '',
      recipient_first_name: defaultValues?.recipient_first_name ||
        (defaultValues?.recipient_name ? defaultValues.recipient_name.split(' ')[0] : ''),
      recipient_last_name: defaultValues?.recipient_last_name ||
        (defaultValues?.recipient_name ? defaultValues.recipient_name.split(' ').slice(1).join(' ') : ''),
      recipient_email: defaultValues?.recipient_email || '',
      delivery_date: defaultValues?.delivery_date
        ? new Date(defaultValues.delivery_date)
        : addDays(new Date(), 7),
      delivery_hour: defaultValues?.delivery_hour?.toString() || '12',
    },
  });

  // Fetch attachments if editing an existing capsule
  useEffect(() => {
    const fetchAttachments = async () => {
      if (defaultValues?.id) {
        try {
          const { data, error } = await supabase
            .from('time_capsule_media')
            .select('*')
            .eq('capsule_id', defaultValues.id);

          if (error) throw error;

          // We can't show the actual files because they're stored in Supabase storage
          // This just shows their names in the UI
          setFiles([]);
        } catch (error) {
          console.error('Error fetching attachments:', error);
          toast.error('Failed to load attachments');
        }
      }
    };

    fetchAttachments();
  }, [defaultValues]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      // Check max files (5)
      if (files.length + newFiles.length > 5) {
        toast.error('Maximum of 5 files can be attached to a time capsule');
        return;
      }

      // Check max file size (50MB)
      const oversizedFiles = newFiles.filter(file => file.size > 50 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        toast.error('One or more files exceed the 50MB size limit');
        return;
      }

      setFiles([...files, ...newFiles]);
    }
  };

  // Remove a file from the list
  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  // Upload files to Supabase storage
  const uploadFiles = async (capsuleId: string): Promise<string[]> => {
    if (files.length === 0) return [];

    setIsUploading(true);
    const fileUrls: string[] = [];
    const totalFiles = files.length;

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `${user?.id}/${capsuleId}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from('time_capsule_media')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        fileUrls.push(filePath);

        // Update progress
        setUploadProgress(Math.round(((i + 1) / totalFiles) * 100));
      }

      return fileUrls;
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files');
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Save media references to the database
  const saveMediaReferences = async (capsuleId: string, filePaths: string[]) => {
    if (filePaths.length === 0) return;

    try {
      const mediaEntries = filePaths.map((path, index) => {
        const file = files[index];
        return {
          capsule_id: capsuleId,
          media_type: file.type.split('/')[0], // 'image', 'video', 'application', etc.
          file_path: path,
          file_name: file.name,
          file_size: file.size
        };
      });

      const { error } = await supabase
        .from('time_capsule_media')
        .insert(mediaEntries);

      if (error) throw error;
    } catch (error) {
      console.error('Error saving media references:', error);
      toast.error('Failed to save media information');
      throw error;
    }
  };

  // Generate a random access code
  const generateAccessCode = (): string => {
    // Use the encryption utility to generate a secure code
    return generatePassword(8);
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    if (!user) {
      toast.error('You must be logged in to create a time capsule');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare the form data
      const formData = {
        title: data.title,
        message: data.message || '',
        recipient_first_name: data.recipient_first_name,
        recipient_last_name: data.recipient_last_name,
        recipient_email: data.recipient_email,
        delivery_date: data.delivery_date,
        delivery_hour: parseInt(data.delivery_hour),
        id: defaultValues?.id // Include ID if editing
      };

      // Call the parent component's submit handler
      await handleFormSubmit(formData);

      // Clear the form
      form.reset({
        title: '',
        message: '',
        recipient_first_name: '',
        recipient_last_name: '',
        recipient_email: '',
        delivery_date: addDays(new Date(), 7),
        delivery_hour: '12'
      });

      // Clear files
      setFiles([]);

    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <Button
        variant="ghost"
        className="mb-4"
        onClick={onCancel}
      >
        <ChevronLeft className="mr-2 h-4 w-4" />
        Back
      </Button>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>
            {defaultValues?.id ? 'Edit Time Capsule' : 'Create New Time Capsule'}
          </CardTitle>
          <CardDescription>
            {defaultValues?.id
              ? 'Update your scheduled time capsule details'
              : 'Create a message that will be delivered in the future'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Give your time capsule a name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Write your message here..."
                        className="h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="recipient_first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Recipient's first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="recipient_last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Recipient's last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="recipient_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Their email address"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="delivery_date"
                  render={({ field }) => {
                    const [isOpen, setIsOpen] = React.useState(false);

                    return (
                      <FormItem className="flex flex-col">
                        <FormLabel>Delivery Date</FormLabel>
                        <Popover open={isOpen} onOpenChange={setIsOpen}>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal"
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? (
                                  format(field.value, "MMMM d, yyyy")
                                ) : (
                                  <span>Select a date</span>
                                )}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={(date) => {
                                field.onChange(date);
                                setIsOpen(false); // Close the popover after selection
                              }}
                              disabled={(date) => isBefore(date, addDays(new Date(), 1))}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="delivery_hour"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Delivery Hour</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select hour" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                            <SelectItem key={hour} value={hour.toString()}>
                              {hour.toString().padStart(2, '0')}:00 {hour < 12 ? 'AM' : 'PM'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-2">
                <FormLabel>Attachments</FormLabel>
                <div className="border border-dashed border-gray-300 rounded-md p-6 text-center">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="hidden"
                    ref={fileInputRef}
                  />

                  <div className="space-y-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={files.length >= 5}
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Add Files
                    </Button>

                    <p className="text-sm text-gray-500">
                      Add up to 5 files (max 50MB each)
                    </p>
                  </div>

                  {files.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm font-medium mb-2">Selected Files:</p>
                      <ul className="space-y-2">
                        {files.map((file, index) => (
                          <li key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center">
                              <File className="h-4 w-4 mr-2 text-gray-500" />
                              <div className="text-sm">
                                <p className="font-medium truncate max-w-[200px]">{file.name}</p>
                                <p className="text-gray-500 text-xs">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              <CardFooter className="flex justify-between px-0">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting || isUploading}
                >
                  Cancel
                </Button>

                <Button
                  type="submit"
                  disabled={isSubmitting || isUploading}
                >
                  {(isSubmitting || isUploading) ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isUploading ? `Uploading (${uploadProgress}%)` : 'Saving...'}
                    </>
                  ) : (
                    defaultValues?.id ? 'Update Time Capsule' : 'Create Time Capsule'
                  )}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default TimeCapsuleForm;
