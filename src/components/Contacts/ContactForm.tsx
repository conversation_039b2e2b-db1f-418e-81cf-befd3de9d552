"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import '@/styles/phone-input.css';
import { formatPhoneNumber } from '@/lib/phone-utils';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';

// Define the schema with conditional validation
const contactSchema = z.object({
  first_name: z.string().min(2, { message: 'First name must be at least 2 characters' }),
  last_name: z.string().min(2, { message: 'Last name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }).or(z.literal('')).optional(),
  phone: z.string().optional(),
  relationship: z.string().optional(),
  notes: z.string().optional(),
}).refine((data) => {
  // Either email or phone must be provided
  return !!data.email || (!!data.phone && data.phone.length > 3); // Ensure phone has meaningful content
}, {
  message: "Either email or phone number must be provided",
  path: ["phone"], // Show the error on the phone field if neither is provided
});

type ContactFormValues = z.infer<typeof contactSchema>;

interface ContactFormProps {
  onSubmit: (data: ContactFormValues) => void;
  onCancel?: () => void;
  defaultValues?: Partial<ContactFormValues> & { name?: string };
}

export default function ContactForm({ onSubmit, onCancel, defaultValues }: ContactFormProps) {
  // Convert old format to new format if needed
  const initialValues = defaultValues ? {
    first_name: defaultValues.first_name || (defaultValues.name ? defaultValues.name.split(' ')[0] : ''),
    last_name: defaultValues.last_name || (defaultValues.name ? defaultValues.name.split(' ').slice(1).join(' ') : ''),
    email: defaultValues.email || '',
    phone: defaultValues.phone || '',
    relationship: defaultValues.relationship || '',
    notes: defaultValues.notes || '',
  } : {
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    relationship: '',
    notes: '',
  };

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactSchema),
    defaultValues: initialValues,
  });

  // Handle form submission
  const handleSubmit = (data: ContactFormValues) => {
    // Capitalize first letter of names
    const formattedData = {
      ...data,
      first_name: data.first_name.charAt(0).toUpperCase() + data.first_name.slice(1),
      last_name: data.last_name.charAt(0).toUpperCase() + data.last_name.slice(1)
    };
    onSubmit(formattedData);
    // Reset form after submission
    form.reset({
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      relationship: '',
      notes: ''
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="last_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="Email address" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <div className="phone-input-container">
                  <PhoneInput
                    country={'us'}
                    value={field.value}
                    onChange={(phone) => field.onChange(phone)}
                    inputClass="phone-input"
                    containerClass="phone-container"
                    buttonClass="phone-dropdown-button"
                    dropdownClass="phone-dropdown"
                    searchClass="phone-search"
                    enableSearch={true}
                    disableSearchIcon={false}
                    countryCodeEditable={false}
                    preferredCountries={['us', 'ca', 'gb', 'au']}
                    autoFormat={true}
                    inputProps={{
                      name: 'phone',
                      required: false,
                    }}
                  />
                </div>
              </FormControl>
              <FormDescription>
                Either email or phone number is required. This information will be shared with your trustees after your passing.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="relationship"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Relationship (optional)</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                value={field.value || ""}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select relationship" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Family">Family</SelectItem>
                  <SelectItem value="Friend">Friend</SelectItem>
                  <SelectItem value="Colleague">Colleague</SelectItem>
                  <SelectItem value="Spouse">Spouse</SelectItem>
                  <SelectItem value="Partner">Partner</SelectItem>
                  <SelectItem value="Child">Child</SelectItem>
                  <SelectItem value="Parent">Parent</SelectItem>
                  <SelectItem value="Sibling">Sibling</SelectItem>
                  <SelectItem value="Extended Family">Extended Family</SelectItem>
                  <SelectItem value="Legal Representative">Legal Representative</SelectItem>
                  <SelectItem value="Financial Advisor">Financial Advisor</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />


        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Any additional information about this contact..."
                  className="resize-none h-20"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit">
            Save Contact
          </Button>
        </div>
      </form>
    </Form>
  );
}
