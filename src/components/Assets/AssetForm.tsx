"use client";

import React, { useState } from 'react';
import { AssetImageUploader } from './AssetImageUploader';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, HelpCircle } from 'lucide-react';
import { format } from 'date-fns';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  ASSET_CATEGORIES,
  AssetType
} from '@/types/asset.types';

const assetSchema = z.object({
  name: z.string().min(2, { message: 'Asset name must be at least 2 characters' }),
  type: z.enum(['physical', 'digital']),
  category: z.string().min(1, { message: 'Please select a category' }),
  description: z.string().optional(),
  location: z.string().optional(),
  value: z.number().optional().nullable(),
  currency: z.string().default('USD'),
  acquisition_date: z.date().optional().nullable(),
  account_details: z.string().optional(),
  image_url: z.string().optional(),
  notes: z.string().optional(),
});

type AssetFormValues = z.infer<typeof assetSchema>;

interface AssetFormProps {
  onSubmit: (data: AssetFormValues) => void;
  onCancel: () => void;
  defaultValues?: Partial<{
    type?: 'digital' | 'physical';
    name?: string;
    value?: number;
    description?: string;
    category?: string;
    notes?: string;
    location?: string;
    currency?: string;
    acquisition_date?: string | Date;
    account_details?: string;
    image_url?: string;
  }>;
}

export default function AssetForm({ onSubmit, onCancel, defaultValues }: AssetFormProps) {
  const [imageUrl, setImageUrl] = useState<string | undefined>(defaultValues?.image_url);

  const form = useForm<AssetFormValues>({
    resolver: zodResolver(assetSchema),
    defaultValues: {
      name: defaultValues?.name || '',
      type: 'physical',
      category: defaultValues?.category || '',
      description: defaultValues?.description || '',
      location: defaultValues?.location || '',
      value: defaultValues?.value || undefined,
      currency: defaultValues?.currency || 'USD',
      acquisition_date: defaultValues?.acquisition_date ?
        (defaultValues.acquisition_date instanceof Date ?
          defaultValues.acquisition_date :
          new Date(defaultValues.acquisition_date)) : null,
      account_details: defaultValues?.account_details || '',
      image_url: defaultValues?.image_url || '',
      notes: defaultValues?.notes || '',
    },
  });

  const handleSubmit = (data: AssetFormValues) => {
    // Include the image URL in the form data
    const formData = {
      ...data,
      image_url: imageUrl
    };
    onSubmit(formData);

    // Clear the form after submission
    form.reset({
      name: '',
      type: 'physical',
      category: '',
      description: '',
      location: '',
      value: undefined,
      currency: 'USD',
      acquisition_date: null,
      account_details: '',
      image_url: '',
      notes: '',
    });

    // Clear the image URL
    setImageUrl(undefined);
  };

  const handleImageUploaded = (url: string) => {
    setImageUrl(url);
    form.setValue('image_url', url);
  };

  // Get categories for assets
  const getCategories = () => {
    return ASSET_CATEGORIES;
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Asset Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Asset Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Enter asset name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Hidden Asset Type Field */}
          <input type="hidden" {...form.register('type')} value="physical" />

          {/* Category */}
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category*</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {getCategories().map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Value */}
          <div className="flex gap-2">
            <FormField
              control={form.control}
              name="value"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Estimated Value</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="0.00"
                      {...field}
                      value={field.value === undefined || field.value === null ? '' : field.value}
                      onChange={e => {
                        // Remove any non-numeric characters except decimal point
                        const value = e.target.value.replace(/[^0-9.]/g, '');
                        // Parse as float or use undefined for empty string
                        field.onChange(value === '' ? undefined : parseFloat(value));
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem className="w-32">
                  <FormLabel>&nbsp;</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="h-10">
                        <SelectValue placeholder="USD" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Common Currencies</SelectLabel>
                        <SelectItem value="USD">$ USD - US Dollar</SelectItem>
                        <SelectItem value="EUR">€ EUR - Euro</SelectItem>
                        <SelectItem value="GBP">£ GBP - British Pound</SelectItem>
                        <SelectItem value="JPY">¥ JPY - Japanese Yen</SelectItem>
                        <SelectItem value="CAD">C$ CAD - Canadian Dollar</SelectItem>
                      </SelectGroup>
                      <SelectSeparator />
                      <SelectGroup>
                        <SelectLabel>Other Currencies</SelectLabel>
                        <SelectItem value="AUD">A$ AUD - Australian Dollar</SelectItem>
                        <SelectItem value="CHF">Fr CHF - Swiss Franc</SelectItem>
                        <SelectItem value="CNY">¥ CNY - Chinese Yuan</SelectItem>
                        <SelectItem value="INR">₹ INR - Indian Rupee</SelectItem>
                        <SelectItem value="BRL">R$ BRL - Brazilian Real</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Acquisition Date */}
          <FormField
            control={form.control}
            name="acquisition_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Acquisition Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={`w-full pl-3 text-left font-normal ${!field.value ? "text-muted-foreground" : ""}`}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value || undefined}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  When you acquired this asset
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location Field */}
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl>
                  <Input placeholder="Where this asset is stored or located" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
              )}
            />

          {/* Account Details Field */}
          <FormField
            control={form.control}
            name="account_details"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Details</FormLabel>
                <FormControl>
                  <Input placeholder="Account number or other relevant information" {...field} />
                </FormControl>
                <FormDescription>
                  Include any relevant account information. This will be securely stored.
                </FormDescription>
                <FormMessage />
              </FormItem>
              )}
            />



        </div>

        {/* Asset Image */}
        <div className="mt-4">
          <FormLabel>Asset Image</FormLabel>
          <div className="mt-2">
            <AssetImageUploader
              onImageSelected={(file) => console.log('Image selected:', file.name)}
              onImageUploaded={handleImageUploaded}
            />
          </div>
          {imageUrl && (
            <p className="text-xs text-gray-500 mt-1">Image will be saved with your asset</p>
          )}
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Detailed description of the asset"
                  className="resize-none h-20"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />



        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            Save Asset
          </Button>
        </div>
      </form>
    </Form>
  );
}
