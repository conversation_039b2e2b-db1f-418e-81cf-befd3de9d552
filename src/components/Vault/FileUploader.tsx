"use client";

import React, { useState, useRef } from 'react';
import { Upload, File, X, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatFileSize } from '@/lib/utils';

interface FileUploaderProps {
  onFileSelected: (file: File) => void;
  maxSizeMB?: number;
  acceptedFileTypes?: string[];
}

export const FileUploader = ({
  onFileSelected,
  maxSizeMB = 5120, // Default max size: 5GB (5120MB)
  acceptedFileTypes = []
}: FileUploaderProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  const validateFile = (file: File): boolean => {
    // Check file size
    if (file.size > maxSizeBytes) {
      setError(`File size exceeds the maximum limit of ${maxSizeMB}MB`);
      return false;
    }

    // Check file type if acceptedFileTypes is provided and not empty
    if (acceptedFileTypes.length > 0) {
      const fileType = file.type;
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      const isAcceptedType = acceptedFileTypes.some(type => {
        // Check MIME type
        if (fileType === type) return true;
        // Check file extension (for cases where MIME type might not be reliable)
        if (type.startsWith('.') && fileExtension === type.substring(1)) return true;
        return false;
      });

      if (!isAcceptedType) {
        setError(`File type not accepted. Please upload a document, image, video, audio, or archive file.`);
        return false;
      }
    }

    setError(null);
    return true;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        onFileSelected(file);
      } else {
        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        onFileSelected(file);
      }
    }
  };

  const clearSelectedFile = () => {
    setSelectedFile(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Use the formatFileSize function from utils

  return (
    <div className="w-full">
      {!selectedFile ? (
        <div
          className={`border-2 border-dashed ${error ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:bg-gray-50'} rounded-lg p-6 text-center cursor-pointer transition-colors`}
          onClick={() => fileInputRef.current?.click()}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {error ? (
            <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
          ) : (
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
          )}
          <p className={`mt-2 text-sm ${error ? 'text-red-600 font-medium' : 'text-gray-600'}`}>
            {error || 'Drag and drop a file here, or click to select a file'}
          </p>
          {acceptedFileTypes.length > 0 && !error && (
            <p className="mt-1 text-xs text-gray-500">
              Accepted file types: PDF, Word, Excel, PowerPoint, Images, Videos, Audio, and more
            </p>
          )}
          <input
            type="file"
            className="hidden"
            onChange={handleFileChange}
            ref={fileInputRef}
            accept={acceptedFileTypes.join(',')}
          />
        </div>
      ) : (
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <File className="h-5 w-5 text-primary mr-2" />
              <div>
                <p className="text-sm font-medium truncate max-w-[200px] md:max-w-xs">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(selectedFile.size)}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
              onClick={clearSelectedFile}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Remove file</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
