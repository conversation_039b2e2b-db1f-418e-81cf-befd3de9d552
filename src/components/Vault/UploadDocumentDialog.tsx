"use client";

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import DocumentForm from './DocumentForm';

interface UploadDocumentDialogProps {
  onDocumentUploaded: () => void;
}

export default function UploadDocumentDialog({ onDocumentUploaded }: UploadDocumentDialogProps) {
  const [open, setOpen] = React.useState(false);

  const handleSuccess = () => {
    setOpen(false);
    // Add a small delay to ensure the database has time to update
    setTimeout(() => {
      onDocumentUploaded();
    }, 1000); // Increased delay to ensure database and storage are updated
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Upload Document
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Upload New Document</DialogTitle>
          <DialogDescription>
            Securely store your important documents in your digital vault.
          </DialogDescription>
        </DialogHeader>
        <DocumentForm onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
}
