"use client";

import React, { useEffect, useState } from 'react';
import { useSubscription } from '@/context/SubscriptionContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Loader2, CheckCircle, AlertCircle, ArrowUpCircle } from 'lucide-react';
import { formatLimit, getSubscriptionLimits, SubscriptionTier } from '@/config/subscription-limits';
import { useRouter } from 'next/navigation';

interface ResourceUsage {
  trustees: { count: number; limit: number };
  assets: { count: number; limit: number };
  contacts: { count: number; limit: number };
  storage: { used: number; limit: number }; // in bytes
  timeCapsules: { count: number; limit: number };
}

export default function SubscriptionStatus() {
  const { plan, isLoading, isSubscribed, subscriptionDetails } = useSubscription();
  const [resourceUsage, setResourceUsage] = useState<ResourceUsage | null>(null);
  const [isLoadingUsage, setIsLoadingUsage] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchResourceUsage = async () => {
      try {
        setIsLoadingUsage(true);

        // Fetch counts for each resource type
        const [trusteesRes, assetsRes, contactsRes, documentsRes, timeCapsuleRes] = await Promise.all([
          fetch('/api/trustees'),
          fetch('/api/assets'),
          fetch('/api/contacts'),
          fetch('/api/vault/documents'),
          fetch('/api/time-capsules')
        ]);

        const trustees = await trusteesRes.json();
        const assets = await assetsRes.json();
        const contacts = await contactsRes.json();
        const documents = await documentsRes.json();
        const timeCapsules = await timeCapsuleRes.json();

        // Calculate storage used
        const storageUsed = documents.reduce((total: number, doc: any) => total + (doc.file_size || 0), 0);

        // Get limits based on subscription tier
        const limits = getSubscriptionLimits(plan as SubscriptionTier);

        setResourceUsage({
          trustees: {
            count: trustees.length,
            limit: limits.maxTrustees
          },
          assets: {
            count: assets.length,
            limit: limits.maxAssets
          },
          contacts: {
            count: contacts.length,
            limit: limits.maxContacts
          },
          storage: {
            used: storageUsed,
            limit: limits.maxVaultStorageGB * 1024 * 1024 * 1024
          },
          timeCapsules: {
            count: timeCapsules.length,
            limit: limits.maxTimeCapsules
          }
        });
      } catch (error) {
        console.error('Error fetching resource usage:', error);
      } finally {
        setIsLoadingUsage(false);
      }
    };

    if (plan) {
      fetchResourceUsage();
    }
  }, [plan]);

  const formatStorageSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const calculatePercentage = (used: number, limit: number | string): number => {
    if (limit === Infinity || typeof limit === 'string') return (used > 0) ? 50 : 0;
    return Math.min(Math.round((used / limit) * 100), 100);
  };

  if (isLoading || isLoadingUsage) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
          <CardDescription>Loading your subscription information...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Subscription Status</span>
          {isSubscribed ? (
            <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">
              Active
            </span>
          ) : (
            <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              Free Plan
            </span>
          )}
        </CardTitle>
        <CardDescription>
          {isSubscribed
            ? `Your ${plan.charAt(0).toUpperCase() + plan.slice(1)} plan is active until ${new Date(subscriptionDetails?.currentPeriodEnd).toLocaleDateString()}`
            : 'You are currently on the free plan'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {resourceUsage && (
          <>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Trustees</span>
                <span>{resourceUsage.trustees.count} / {formatLimit(resourceUsage.trustees.limit)}</span>
              </div>
              <Progress value={calculatePercentage(resourceUsage.trustees.count, resourceUsage.trustees.limit)} />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Assets</span>
                <span>{resourceUsage.assets.count} / {formatLimit(resourceUsage.assets.limit)}</span>
              </div>
              <Progress value={calculatePercentage(resourceUsage.assets.count, resourceUsage.assets.limit)} />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Contacts</span>
                <span>{resourceUsage.contacts.count} / {formatLimit(resourceUsage.contacts.limit)}</span>
              </div>
              <Progress value={calculatePercentage(resourceUsage.contacts.count, resourceUsage.contacts.limit)} />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Storage</span>
                <span>{formatStorageSize(resourceUsage.storage.used)} / {formatStorageSize(resourceUsage.storage.limit)}</span>
              </div>
              <Progress value={calculatePercentage(resourceUsage.storage.used, resourceUsage.storage.limit)} />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Time Capsules</span>
                <span>{resourceUsage.timeCapsules.count} / {formatLimit(resourceUsage.timeCapsules.limit)}</span>
              </div>
              <Progress value={calculatePercentage(resourceUsage.timeCapsules.count, resourceUsage.timeCapsules.limit)} />
            </div>
          </>
        )}

        <div className="mt-6 pt-4 border-t">
          <h4 className="font-medium mb-2">Plan Features</h4>
          <ul className="space-y-2">
            {plan === 'premium' ? (
              <>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Unlimited assets and contacts
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  5GB vault storage
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Up to 5 trustees
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Time capsule messages
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Priority support
                </li>
              </>
            ) : (
              <>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Unlimited assets
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  1 document storage
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  Up to 1 trustee
                </li>
                <li className="flex items-center text-sm">
                  <AlertCircle className="h-4 w-4 text-gray-400 mr-2" />
                  No time capsule messages
                </li>
                <li className="flex items-center text-sm">
                  <AlertCircle className="h-4 w-4 text-gray-400 mr-2" />
                  Priority support
                </li>
              </>
            )}
          </ul>
        </div>
      </CardContent>
      <CardFooter>
        {plan === 'free' ? (
          <Button
            className="w-full"
            onClick={() => router.push('/pricing')}
          >
            <ArrowUpCircle className="h-4 w-4 mr-2" />
            Upgrade to Premium
          </Button>
        ) : (
          <Button
            variant="outline"
            className="w-full"
            onClick={() => router.push('/settings/subscription')}
          >
            Manage Subscription
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
