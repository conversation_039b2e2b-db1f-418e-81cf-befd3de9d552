
import React, { useState } from 'react';
import { useSubscription } from '@/context/SubscriptionContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface PlanProps {
  name: string;
  price: string;
  period: string;
  description: string;
  features: { name: string; included: boolean }[];
  buttonText: string;
  buttonVariant: 'outline' | 'default';
  highlight?: boolean;
  planId: 'free' | 'premium';
}

const pricingPlans: PlanProps[] = [
  {
    name: 'Essential Legacy',
    price: '$0',
    period: 'forever',
    description: 'Basic digital legacy management for individuals',
    features: [
      { name: 'Unlimited assets', included: true },
      { name: '1 document storage', included: true },
      { name: 'Time capsule messages', included: false },
      { name: 'Basic will advice', included: true },
      { name: 'Up to 1 trustee', included: true },
      { name: 'Premium encryption', included: false },
      { name: 'Priority support', included: false },
      { name: 'Legal document review', included: false }
    ],
    buttonText: 'Current Plan',
    buttonVariant: 'outline',
    planId: 'free'
  },
  {
    name: 'Legacy Preserver',
    price: '$29.99',
    period: 'per year',
    description: 'Comprehensive legacy planning for families',
    features: [
      { name: 'Unlimited assets', included: true },
      { name: '5GB vault storage', included: true },
      { name: '100 time capsule messages (up to 10GB)', included: true },
      { name: 'Unlimited will advice', included: true },
      { name: 'Up to 5 trustees', included: true },
      { name: 'Premium encryption', included: true },
      { name: 'Priority support', included: true },
      { name: 'Unlimited contacts & wishes', included: true }
    ],
    buttonText: 'Upgrade to Premium',
    buttonVariant: 'default',
    highlight: true,
    planId: 'premium'
  }
];

const SubscriptionPlans = () => {
  const { plan, isLoading, createCheckout } = useSubscription();
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const handleSubscribe = async (planId: 'premium') => {
    try {
      setProcessingPlan(planId);
      const checkoutUrl = await createCheckout(planId);

      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      toast.error('Failed to initiate checkout process');
      console.error('Checkout error:', error);
    } finally {
      setProcessingPlan(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading subscription information...</span>
      </div>
    );
  }

  return (
    <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-3">
      {pricingPlans.map((planInfo) => {
        const isCurrentPlan = planInfo.planId === plan;
        const isProcessing = processingPlan === planInfo.planId;

        return (
          <Card key={planInfo.name} className={`flex flex-col ${planInfo.highlight ? 'border-primary shadow-lg relative' : ''}`}>
            {planInfo.highlight && (
              <div className="absolute top-0 left-0 right-0 transform -translate-y-1/2 flex justify-center">
                <span className="bg-primary text-white text-sm px-4 py-1 rounded-full font-medium">
                  Most Popular
                </span>
              </div>
            )}
            {isCurrentPlan && (
              <div className="absolute top-0 right-0 mt-4 mr-4">
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                  Current Plan
                </span>
              </div>
            )}
            <CardHeader>
              <CardTitle>{planInfo.name}</CardTitle>
              <div className="flex items-baseline mt-2">
                <span className="text-3xl font-extrabold">{planInfo.price}</span>
                <span className="ml-1 text-gray-500">/{planInfo.period}</span>
              </div>
              <CardDescription className="mt-2">{planInfo.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-3">
                {planInfo.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    {feature.included ? (
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mr-2" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-400 flex-shrink-0 mr-2" />
                    )}
                    <span className={feature.included ? 'text-gray-900' : 'text-gray-500'}>
                      {feature.name}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {planInfo.planId === 'free' ? (
                <Button
                  variant={planInfo.buttonVariant}
                  className="w-full"
                  disabled
                >
                  Free Plan
                </Button>
              ) : isCurrentPlan ? (
                <Button
                  variant="outline"
                  className="w-full"
                  disabled
                >
                  Current Plan
                </Button>
              ) : (
                <Button
                  variant={planInfo.buttonVariant}
                  className="w-full"
                  onClick={() => handleSubscribe(planInfo.planId as 'premium')}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    planInfo.buttonText
                  )}
                </Button>
              )}
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
};

export default SubscriptionPlans;
