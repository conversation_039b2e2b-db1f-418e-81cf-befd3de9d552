import React from 'react';
import { Shield } from 'lucide-react';

export default function ClientFallback() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <div className="flex-1 flex flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center gap-2 mb-8">
          <Shield className="h-10 w-10 text-primary" />
          <h1 className="text-2xl font-bold">Legalock</h1>
        </div>
        
        <div className="text-center max-w-md">
          <div className="flex justify-center mb-6">
            <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading...</h2>
          <p className="text-gray-600">
            Please wait while we prepare your content.
          </p>
        </div>
      </div>
    </div>
  );
}
