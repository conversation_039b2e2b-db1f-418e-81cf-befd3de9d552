"use client";

import React, { useState } from 'react';
import { usePathname } from 'next/navigation';
import Header from '@/components/Navigation/Header';
import { useAuth } from '@/context/auth-context';
import { Loader2 } from 'lucide-react';
import { SidebarProvider } from '@/components/ui/sidebar';
import TrusteeIn<PERSON><PERSON>hecker from '@/components/TrusteeInvitationChecker';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const { loading } = useAuth();
  const pathname = usePathname();

  // Public routes that don't require authentication
  const publicRoutes = ['/login', '/register', '/verify', '/reset-password', '/'];
  const isPublicRoute = publicRoutes.includes(pathname);

  if (loading && !isPublicRoute) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  return (
    <SidebarProvider>
      {/* Add the TrusteeInvitationChecker to check for pending invitations */}
      {!isPublicRoute && <TrusteeInvitationChecker />}

      <div className="min-h-screen flex w-full">
        <div className="flex-1 flex flex-col">
          <Header />
          <main className="flex-1 bg-gray-50">
            <div className="page-container">
              {children}
            </div>
          </main>
          <footer className="py-4 px-6 border-t border-gray-200 text-center text-sm text-gray-500">
            <p>Legalock © {new Date().getFullYear()} - Your digital legacy secured</p>
          </footer>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
