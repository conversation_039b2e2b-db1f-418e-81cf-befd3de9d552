"use client";

import React from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import Header from '@/components/Navigation/Header';

interface FeatureLayoutProps {
  children: React.ReactNode;
}

const FeatureLayout: React.FC<FeatureLayoutProps> = ({ children }) => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <div className="flex-1 flex flex-col">
          <Header />
          <main className="flex-1 bg-gray-50">
            <div className="page-container">
              {children}
            </div>
          </main>
          <footer className="py-4 px-6 border-t border-gray-200 text-center text-sm text-gray-500">
            <p>Legalock © {new Date().getFullYear()} - Your digital legacy secured</p>
          </footer>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default FeatureLayout;
