"use client";

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Database, HardDrive, Users, UserPlus, Sunset, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface FeaturePageLayoutProps {
  title: string;
  description: string;
  iconName: string;
  iconColor: string;
  iconBgColor: string;
  children: React.ReactNode;
}

export default function FeaturePageLayout({
  title,
  description,
  iconName,
  iconColor,
  iconBgColor,
  children,
}: FeaturePageLayoutProps) {
  // Render the appropriate icon based on the iconName
  const renderIcon = () => {
    switch (iconName) {
      case 'Database':
        return <Database className={cn("h-10 w-10", iconColor)} />;
      case 'HardDrive':
        return <HardDrive className={cn("h-10 w-10", iconColor)} />;
      case 'Users':
        return <Users className={cn("h-10 w-10", iconColor)} />;
      case 'UserPlus':
        return <UserPlus className={cn("h-10 w-10", iconColor)} />;
      case 'Sunset':
        return <Sunset className={cn("h-10 w-10", iconColor)} />;
      case 'Heart':
        return <Heart className={cn("h-10 w-10", iconColor)} />;
      default:
        return <Database className={cn("h-10 w-10", iconColor)} />;
    }
  };
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 py-6">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className={cn("py-16", iconBgColor)}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-flex items-center justify-center p-3 rounded-full bg-white/20 mb-6">
              {renderIcon()}
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">{title}</h1>
            <p className="text-xl text-white/90 mb-8">{description}</p>
            <div className="flex justify-center gap-4">
              <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100" asChild>
                <Link href="/register">Get Started</Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white bg-white/10 hover:bg-white/20" asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-100 py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Ready to secure your digital legacy?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Join thousands of users who trust Legalock to protect their digital assets and final wishes.
            </p>
            <div className="flex justify-center gap-4">
              <Button size="lg" asChild>
                <Link href="/register">Get Started for Free</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-gray-500 text-sm">
            <p>© {new Date().getFullYear()} Legalock. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
