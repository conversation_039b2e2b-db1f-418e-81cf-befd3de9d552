"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Package,
  FileText,
  Users,
  Clock,
  FileSignature,
  Power,
  Settings,
  LogOut,
  CreditCard,
  UserPlus
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/auth-context';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

interface SidebarNavProps {
  className?: string;
}

export function SidebarNav({ className }: SidebarNavProps) {
  const { signOut } = useAuth();
  const pathname = usePathname();

  const mainMenuItems = [
    { title: 'Dashboard', icon: Home, path: '/dashboard' },
    { title: 'Assets', icon: Package, path: '/assets' },
    { title: 'Vault', icon: FileText, path: '/vault' },
    { title: 'Trustees', icon: Users, path: '/trustees' },
    { title: 'Contacts', icon: UserPlus, path: '/contacts' },
    { title: 'Time Capsule', icon: Clock, path: '/time-capsule' },
    { title: 'Will', icon: FileSignature, path: '/will' },
    { title: 'Service Sunset', icon: Power, path: '/service-sunset' },
  ];

  const secondaryMenuItems = [
    { title: 'Subscription', icon: CreditCard, path: '/pricing' },
    { title: 'Settings', icon: Settings, path: '/settings' },
  ];

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success('Successfully logged out');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to log out');
    }
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div className="flex items-center justify-center h-16 px-6 border-b border-gray-800">
        <Link href="/" className="text-xl font-bold hover:opacity-80 transition-opacity">
          <span className="text-blue-500">Legal</span>
          <span className="text-blue-300">ock</span>
        </Link>
      </div>

      <div className="flex-1 overflow-auto py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-xs font-semibold text-gray-400 uppercase">
            Main Menu
          </h2>
          <div className="space-y-1">
            {mainMenuItems.map((item) => (
              <Link
                key={item.title}
                href={item.path}
                className={cn(
                  "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
                  pathname === item.path
                    ? "bg-gray-800 text-white font-medium"
                    : "text-gray-400 hover:text-white hover:bg-gray-800"
                )}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.title}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>

      <div className="border-t border-gray-800 p-3">
        <div className="space-y-1">
          {secondaryMenuItems.map((item) => (
            <Link
              key={item.title}
              href={item.path}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
                pathname === item.path
                  ? "bg-gray-800 text-white font-medium"
                  : "text-gray-400 hover:text-white hover:bg-gray-800"
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
            </Link>
          ))}
          <Button
            variant="ghost"
            className="w-full justify-start px-3 text-gray-400 hover:text-white hover:bg-gray-800"
            onClick={handleLogout}
          >
            <LogOut className="mr-3 h-4 w-4" />
            <span>Logout</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
