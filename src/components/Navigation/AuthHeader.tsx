"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

const AuthHeader = () => {
  return (
    <header className="w-full bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image
                src="/images/Legalock-logo.svg"
                alt="Legalock Logo"
                width={160}
                height={50}
                style={{ height: 'auto' }}
                priority
              />
            </Link>
          </div>

          {/* Back to Home Button */}
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/">Back to Home</Link>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default AuthHeader;
