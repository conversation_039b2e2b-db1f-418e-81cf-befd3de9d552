
import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  Home,
  Package,
  FileText,
  Users,
  Clock,
  FileSignature,
  Power,
  Settings,
  LogOut,
  CreditCard
} from 'lucide-react';
import {
  Sidebar as SidebarContainer,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from "@/components/ui/sidebar";
import { useAuth } from '@/context/auth-context';
import { toast } from 'sonner';

const Sidebar = () => {
  const { signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const mainMenuItems = [
    { title: 'Dashboard', icon: Home, path: '/dashboard' },
    { title: 'Assets', icon: Package, path: '/assets' },
    { title: 'Vault', icon: FileText, path: '/vault' },
    { title: 'Trustees', icon: Users, path: '/trustees' },
    { title: 'Time Capsule', icon: Clock, path: '/time-capsule' },
    { title: 'Will', icon: FileSignature, path: '/will' },
    { title: 'Service Sunset', icon: Power, path: '/service-sunset' },
  ];

  const secondaryMenuItems = [
    { title: 'Subscription', icon: CreditCard, path: '/subscription' },
    { title: 'Settings', icon: Settings, path: '/settings' },
  ];

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success('Successfully logged out');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to log out');
    }
  };

  return (
    <SidebarContainer>
      <div className="flex items-center justify-center h-16 px-6 border-b border-sidebar-border">
        <h1 className="text-xl font-bold text-white">
          <span className="text-primary">Legal</span>
          <span className="text-secondary">ock</span>
        </h1>
      </div>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Main Menu</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link
                      href={item.path}
                      className={pathname === item.path
                        ? "text-white font-medium flex items-center gap-2"
                        : "text-sidebar-foreground/80 hover:text-white flex items-center gap-2"
                      }
                    >
                      <item.icon size={18} />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {secondaryMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link
                      href={item.path}
                      className={pathname === item.path
                        ? "text-white font-medium flex items-center gap-2"
                        : "text-sidebar-foreground/80 hover:text-white flex items-center gap-2"
                      }
                    >
                      <item.icon size={18} />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
              <SidebarMenuItem>
                <SidebarMenuButton onClick={handleLogout} className="text-sidebar-foreground/80 hover:text-white">
                  <LogOut size={18} />
                  <span>Logout</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarFooter>
    </SidebarContainer>
  );
};

export default Sidebar;
