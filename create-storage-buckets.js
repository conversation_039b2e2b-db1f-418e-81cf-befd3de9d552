require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createStorageBuckets() {
  try {
    console.log('Creating storage buckets...');
    
    // Create documents bucket
    await createBucket('documents', 'Documents storage for vault');
    
    console.log('Storage buckets created and configured successfully!');
  } catch (error) {
    console.error('Error creating storage buckets:', error);
  }
}

async function createBucket(name, description) {
  try {
    console.log(`Creating ${name} bucket...`);
    
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    
    if (listError) {
      console.error(`Error listing buckets:`, listError);
      return;
    }
    
    const bucketExists = buckets.some(bucket => bucket.name === name);
    
    if (bucketExists) {
      console.log(`Bucket ${name} already exists.`);
    } else {
      // Create the bucket
      const { data, error } = await supabaseAdmin.storage.createBucket(name, {
        public: true,
        fileSizeLimit: 52428800, // 50MB
        allowedMimeTypes: ['*/*', 'application/octet-stream', 'application/encrypted'],
      });
      
      if (error) {
        console.error(`Error creating ${name} bucket:`, error);
      } else {
        console.log(`Bucket ${name} created successfully.`);
      }
    }
  } catch (error) {
    console.error(`Error in createBucket for ${name}:`, error);
  }
}

createStorageBuckets();
