require('dotenv').config({ path: '.env.local' });
const { Resend } = require('resend');

const resend = new Resend(process.env.RESEND_API_KEY);

async function main() {
  try {
    console.log('Using Resend API Key:', process.env.RESEND_API_KEY);

    // Test sending an email
    const { data, error } = await resend.emails.send({
      from: 'Legalock <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'Test Email',
      html: '<p>This is a test email to verify Resend API is working.</p>',
    });

    if (error) {
      console.error('Error sending email:', error);
    } else {
      console.log('Email sent successfully:', data);
    }
  } catch (error) {
    console.error('Exception sending email:', error);
  }
}

main();
