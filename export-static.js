// Custom export script to handle static export more gracefully
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting static export process...');

// Set environment variables to suppress warnings
process.env.NODE_ENV = 'production';

try {
  // Run the Next.js build with static export
  console.log('Building the application...');
  execSync('npm run build', { stdio: 'inherit' });
  
  console.log('Static export completed successfully!');
  console.log('Note: API routes are not included in static exports by design.');
  console.log('The warnings about dynamic server usage are expected and can be ignored.');
  
  // Create a .nojekyll file to prevent GitHub Pages from ignoring files that begin with an underscore
  const outDir = path.join(__dirname, 'out');
  if (fs.existsSync(outDir)) {
    fs.writeFileSync(path.join(outDir, '.nojekyll'), '');
    console.log('Created .nojekyll file for GitHub Pages compatibility.');
  }
  
  console.log('Your static site is ready in the "out" directory!');
} catch (error) {
  console.error('Error during export:', error);
  process.exit(1);
}
