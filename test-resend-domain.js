require('dotenv').config({ path: '.env.local' });
const { Resend } = require('resend');

const resend = new Resend(process.env.RESEND_API_KEY);

async function main() {
  try {
    console.log('Using Resend API Key:', process.env.RESEND_API_KEY);
    
    // Check domain verification status
    try {
      const { data: domains, error: domainsError } = await resend.domains.list();
      
      if (domainsError) {
        console.error('Error fetching domains:', domainsError);
      } else {
        console.log('Domains:', domains);
        
        // Check if legalock.com is verified
        const legalockDomain = domains.data.find(domain => domain.name === 'legalock.com');
        if (legalockDomain) {
          console.log('Legalock domain status:', legalockDomain.status);
        } else {
          console.log('Legalock.com domain not found in your Resend account');
        }
      }
    } catch (error) {
      console.error('Error checking domains:', error);
    }
    
    // Test sending an email to Resend's test email
    try {
      const { data, error } = await resend.emails.send({
        from: '<EMAIL>', // Use Resend's domain for testing
        to: ['<EMAIL>'],
        subject: 'Test Email',
        html: '<p>This is a test email to verify Resend API is working.</p>',
      });
      
      if (error) {
        console.error('Error sending email:', error);
      } else {
        console.log('Email sent successfully:', data);
      }
    } catch (error) {
      console.error('Exception sending email:', error);
    }
  } catch (error) {
    console.error('Main error:', error);
  }
}

main();
