#!/bin/bash

# Make sure the script exits on any error
set -e

echo "Deploying Supabase Edge Functions..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI not found. Installing..."
    npm install -g supabase
fi

# Login to Supabase (this will open a browser window)
echo "Logging in to Supabase..."
supabase login

# Link to the project
echo "Linking to Supabase project..."
supabase link --project-ref ccwvtcudztphwwzzgwvg

# Create a temporary directory for deployment
TMP_DIR="$(mktemp -d)"
echo "Created temporary directory: $TMP_DIR"

# Function to deploy a single function
deploy_function() {
    local function_name=$1
    echo "Deploying $function_name function..."

    # Create function directory in temp dir
    mkdir -p "$TMP_DIR/$function_name"

    # Copy function files
    cp "supabase/functions/$function_name/index.ts" "$TMP_DIR/$function_name/"

    # Copy any other files in the function directory if they exist
    if [ -f "supabase/functions/$function_name/deno.json" ]; then
        cp "supabase/functions/$function_name/deno.json" "$TMP_DIR/$function_name/"
    fi

    # Create shared directory and copy shared files
    mkdir -p "$TMP_DIR/$function_name/shared"
    cp "supabase/functions/shared/cors.ts" "$TMP_DIR/$function_name/shared/"

    # Deploy from temp directory
    (cd "$TMP_DIR" && supabase functions deploy "$function_name")

    echo "$function_name deployed successfully"
}

# Deploy each function individually
deploy_function "create-checkout"
deploy_function "check-subscription"
deploy_function "estate-recommendations"
deploy_function "deliver-time-capsule"
deploy_function "send-trustee-invitation"
deploy_function "send-email-hook"

# Clean up temp directory
rm -rf "$TMP_DIR"
echo "Cleaned up temporary directory"

# Set the necessary secrets (using environment variables instead of hardcoded values)
echo "Setting up secrets..."
if [ -n "$STRIPE_SECRET_KEY" ]; then
    supabase secrets set STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY"
else
    echo "Warning: STRIPE_SECRET_KEY environment variable not set. Skipping secret setup."
fi

echo "Edge Functions deployed successfully!"
