require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createVaultDocumentsTable() {
  try {
    console.log('Creating vault_documents table...');
    
    // Execute SQL to create the table
    const { error } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.vault_documents (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL,
          file_path TEXT NOT NULL,
          file_type TEXT,
          file_size INTEGER,
          is_encrypted BOOLEAN NOT NULL DEFAULT TRUE,
          encryption_key TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );

        -- Add RLS policies for vault_documents
        ALTER TABLE public.vault_documents ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY IF NOT EXISTS "Users can view their own vault documents"
        ON public.vault_documents
        FOR SELECT
        USING (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can insert their own vault documents"
        ON public.vault_documents
        FOR INSERT
        WITH CHECK (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can update their own vault documents"
        ON public.vault_documents
        FOR UPDATE
        USING (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can delete their own vault documents"
        ON public.vault_documents
        FOR DELETE
        USING (auth.uid() = user_id);

        -- Grant privileges
        GRANT ALL ON public.vault_documents TO authenticated;
        GRANT ALL ON public.vault_documents TO service_role;
      `
    });
    
    if (error) {
      console.error('Error creating vault_documents table:', error);
    } else {
      console.log('Vault documents table created successfully!');
    }
  } catch (error) {
    console.error('Error in createVaultDocumentsTable:', error);
  }
}

createVaultDocumentsTable();
