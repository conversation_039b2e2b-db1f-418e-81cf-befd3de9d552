require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Create a Supabase client with hardcoded credentials
const supabaseUrl = 'https://ccwvtcudztphwwzzgwvg.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Function to hash a password
function hashPassword(password) {
  return new Promise((resolve, reject) => {
    // Generate a random salt
    const salt = crypto.randomBytes(16).toString('hex');

    // Use PBKDF2 to hash the password
    crypto.pbkdf2(password, salt, 10000, 64, 'sha512', (err, derivedKey) => {
      if (err) {
        reject(err);
        return;
      }

      // Format: iterations:salt:hash
      resolve(`10000:${salt}:${derivedKey.toString('hex')}`);
    });
  });
}

async function createTestUser() {
  try {
    console.log('Creating a test user...');
    
    // Generate a random email
    const randomEmail = `test-${Date.now()}@example.com`;
    
    // Hash the password
    const passwordHash = await hashPassword('password123');
    
    // Create the user
    const { data: user, error: userError } = await supabase
      .from('custom_users')
      .insert({
        email: randomEmail,
        password_hash: passwordHash,
        first_name: 'Test',
        last_name: 'User',
        email_verified: true,
      })
      .select();
    
    if (userError) {
      console.error('Error creating test user:', userError);
    } else {
      console.log('Test user created successfully!');
      console.log('User:', user[0]);
    }
    
  } catch (error) {
    console.error('Error creating test user:', error);
  }
}

createTestUser();
