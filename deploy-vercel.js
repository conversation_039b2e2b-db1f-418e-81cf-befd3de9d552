#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute shell commands and print output
function runCommand(command) {
  console.log(`Running: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return output;
  } catch (error) {
    console.error(`<PERSON>rror executing command: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

// Main deployment function
async function deploy() {
  console.log('Starting Vercel deployment process...');

  // Install Vercel CLI if not already installed
  try {
    execSync('vercel --version', { stdio: 'ignore' });
    console.log('Vercel CLI is already installed');
  } catch (error) {
    console.log('Installing Vercel CLI...');
    runCommand('npm install -g vercel');
  }

  // Install dependencies
  console.log('Installing dependencies...');
  runCommand('npm install');

  // Build the project
  console.log('Building the project...');
  runCommand('npm run build');

  // Deploy to Vercel
  console.log('Deploying to Vercel...');
  runCommand('npx vercel --prod');

  console.log('Deployment complete!');
}

// Run the deployment
deploy().catch(error => {
  console.error('Deployment failed:', error);
  process.exit(1);
});
