#!/bin/bash

# Make the script executable
chmod +x update-vercel-env.sh

# Load environment variables from .env.local
source .env.local

# Update the NEXT_PUBLIC_SITE_URL to the production URL
NEXT_PUBLIC_SITE_URL="https://www.legalock.com"

# Set Vercel environment variables
echo "Setting Vercel environment variables..."

# Database Configuration
vercel env add NEXT_PUBLIC_SUPABASE_URL production "$NEXT_PUBLIC_SUPABASE_URL"
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production "$NEXT_PUBLIC_SUPABASE_ANON_KEY"

# Database Admin Key
vercel env add SUPABASE_SERVICE_ROLE_KEY production "$SUPABASE_SERVICE_ROLE_KEY"

# Authentication Configuration
vercel env add AUTH_SECRET production "$AUTH_SECRET"
vercel env add AUTH_SESSION_EXPIRY_DAYS production "$AUTH_SESSION_EXPIRY_DAYS"

# Email Service
vercel env add RESEND_API_KEY production "$RESEND_API_KEY"

# Site URL
vercel env add NEXT_PUBLIC_SITE_URL production "$NEXT_PUBLIC_SITE_URL"

# Stripe Integration
vercel env add STRIPE_SECRET_KEY production "$STRIPE_SECRET_KEY"
vercel env add STRIPE_WEBHOOK_SECRET production "$STRIPE_WEBHOOK_SECRET"
vercel env add NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY production "$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"
vercel env add STRIPE_PREMIUM_PLAN_ID production "$STRIPE_PREMIUM_PLAN_ID"

# AI Integration
vercel env add DEEPSEEK_API_KEY production "$DEEPSEEK_API_KEY"

echo "Environment variables set successfully!"
echo "Now run 'vercel --prod' to deploy with the updated environment variables."
