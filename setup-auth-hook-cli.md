# Setting Up Supabase Auth Email Hook via CLI

Since the Supabase CLI has some limitations with configuring auth hooks directly, we'll use a combination of CLI commands and dashboard configuration.

## Step 1: Deploy the Email Hook Function

The email hook function has already been deployed to your Supabase project. You can verify this by running:

```bash
supabase functions list
```

You should see `send-email-hook` in the list of functions.

## Step 2: Set the Webhook Secret

We've already generated a webhook secret and set it as an environment variable:

```bash
SEND_EMAIL_HOOK_SECRET="57MK2BeeRJyI2xX/1B8ZF6RrIvCVqY5jEtdc8WvWV3M="
```

This secret is now available to your function.

## Step 3: Configure the Auth Hook in the Dashboard

1. Go to the Supabase Dashboard: https://supabase.com/dashboard/project/ccwvtcudztphwwzzgwvg/auth/hooks

2. In the "Auth Hooks" section, click on "Create new hook"

3. Select "Send Email" as the hook type

4. Select "HTTPS" as the hook method

5. Enter the following URL for the hook:
   ```
   https://ccwvtcudztphwwzzgwvg.supabase.co/functions/v1/send-email-hook
   ```

6. Click "Generate Secret" to create a webhook secret

7. Copy the generated secret (it will look like `v1,whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`)

8. Run the following command in your terminal to update the secret:
   ```
   supabase secrets set SEND_EMAIL_HOOK_SECRET="<paste_the_secret_here_without_v1,whsec_prefix>"
   ```

9. Click "Create" to save the hook configuration

## Step 4: Test the Hook

To test if your email hook is working correctly:

1. Try signing up with a new email address on your application

2. Check if you receive a verification email with the 6-digit code

3. Verify that the email has the correct formatting and branding

## Troubleshooting

If emails are not being sent or received:

1. Check the function logs in the Supabase Dashboard:
   - Go to https://supabase.com/dashboard/project/ccwvtcudztphwwzzgwvg/functions
   - Click on the "send-email-hook" function
   - Check the logs for any errors

2. Verify that the environment variables are set correctly:
   ```bash
   supabase secrets list
   ```

3. Make sure the hook is properly configured in the Auth settings

4. Check if the Resend API is working by running the test-send-email.js script:
   ```
   node test-send-email.js
   ```
