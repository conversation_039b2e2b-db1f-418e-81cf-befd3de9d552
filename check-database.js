require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function checkDatabase() {
  try {
    console.log('Checking database structure...');

    // Check if custom_users table exists
    console.log('Checking custom_users table...');
    const { data: customUsers, error: customUsersError } = await supabase
      .from('custom_users')
      .select('id')
      .limit(1);

    if (customUsersError) {
      console.error('Error checking custom_users table:', customUsersError);
    } else {
      console.log('custom_users table exists!');
    }

    // Check if user_sessions table exists
    console.log('Checking user_sessions table...');
    const { data: userSessions, error: userSessionsError } = await supabase
      .from('user_sessions')
      .select('id')
      .limit(1);

    if (userSessionsError) {
      console.error('Error checking user_sessions table:', userSessionsError);
    } else {
      console.log('user_sessions table exists!');
    }

    // Check if verification_codes table exists
    console.log('Checking verification_codes table...');
    const { data: verificationCodes, error: verificationCodesError } = await supabase
      .from('verification_codes')
      .select('id')
      .limit(1);

    if (verificationCodesError) {
      console.error('Error checking verification_codes table:', verificationCodesError);
    } else {
      console.log('verification_codes table exists!');
    }

    // List all tables in the public schema
    console.log('Listing all tables in the public schema...');
    const { data: tables, error: tablesError } = await supabase
      .rpc('list_tables');

    if (tablesError) {
      console.error('Error listing tables:', tablesError);
    } else {
      console.log('Tables in the public schema:', tables);
    }

  } catch (error) {
    console.error('Error checking database:', error);
  }
}

checkDatabase();
