/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['ccwvtcudztphwwzzgwvg.supabase.co'],
  },
  // Fix for browser extension attributes warning
  compiler: {
    styledComponents: true,
  },
  // Ignore specific HTML attributes added by browser extensions
  webpack: (config) => {
    config.module.rules.push({
      test: /\.js$/,
      exclude: /node_modules/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['next/babel'],
        },
      },
    });
    return config;
  },
  // Keep trailing slashes for consistent URLs
  trailingSlash: true,
  serverExternalPackages: []
};

export default nextConfig;
