require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with hardcoded credentials
const supabaseUrl = 'https://ccwvtcudztphwwzzgwvg.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjd3Z0Y3VkenRwaHd3enpnd3ZnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA3NDcyNiwiZXhwIjoyMDU5NjUwNzI2fQ.M3ZPuB3P4P2n7NlfoWpJUxArjsBlcL9noxxfUvuldWM';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function getTestUser() {
  try {
    console.log('Getting the test user...');
    
    // Get the test user by email
    const { data: user, error: userError } = await supabase
      .from('custom_users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();
    
    if (userError) {
      console.error('Error getting test user:', userError);
    } else {
      console.log('Test user retrieved successfully!');
      console.log('User:', user);
    }
    
  } catch (error) {
    console.error('Error getting test user:', error);
  }
}

getTestUser();
