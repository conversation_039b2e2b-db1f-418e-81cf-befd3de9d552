// Script to update the service_sunset table schema
require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function updateServiceSunsetSchema() {
  try {
    console.log('Updating service_sunset table schema...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'update-service-sunset-schema.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabaseAdmin.rpc('exec_sql', {
      sql: sqlContent
    });
    
    if (error) {
      console.error('Error updating service_sunset table schema:', error);
      return;
    }
    
    console.log('Service sunset table schema updated successfully!');
  } catch (error) {
    console.error('Error in updateServiceSunsetSchema:', error);
  }
}

// Run the function
updateServiceSunsetSchema();
