// Script to check a user's status
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function checkUserStatus() {
  try {
    console.log('Checking user status...');
    
    // Get the user from custom_users table
    const { data: users, error: userError } = await supabaseAdmin
      .from('custom_users')
      .select('*')
      .eq('email', '<EMAIL>');
    
    if (userError) {
      console.error('Error getting user:', userError);
      return;
    }
    
    if (users.length === 0) {
      console.log('User not found');
      return;
    }
    
    const user = users[0];
    console.log('User found:', user);
    
    // List all tables in the database
    try {
      const { data: tables, error: tablesError } = await supabaseAdmin
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
      
      if (tablesError) {
        console.error('Error listing tables:', tablesError);
      } else {
        console.log('Tables in the database:', tables.map(t => t.table_name));
      }
    } catch (error) {
      console.error('Error listing tables:', error);
    }
    
    // Check if the user has a session
    try {
      const { data: sessions, error: sessionsError } = await supabaseAdmin
        .from('user_sessions')
        .select('*')
        .eq('user_id', user.id);
      
      if (sessionsError) {
        console.error('Error getting sessions:', sessionsError);
      } else {
        console.log('User sessions:', sessions);
      }
    } catch (error) {
      console.error('Error getting sessions:', error);
    }
    
    console.log('User status check completed!');
  } catch (error) {
    console.error('Error checking user status:', error);
  }
}

// Run the function
checkUserStatus();
