-- Update the service_sunset table to make cancellation_method optional

-- First, check if the service_sunset table exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'service_sunset') THEN
        -- Alter the table to drop the NOT NULL constraint from cancellation_method
        ALTER TABLE public.service_sunset ALTER COLUMN cancellation_method DROP NOT NULL;
        
        RAISE NOTICE 'Successfully updated service_sunset table schema.';
    ELSE
        RAISE NOTICE 'service_sunset table does not exist.';
    END IF;
END
$$;
