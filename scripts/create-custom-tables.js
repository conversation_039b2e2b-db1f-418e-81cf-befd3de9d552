require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createCustomTables() {
  try {
    console.log('Creating custom tables...');
    
    // Create custom_users table if it doesn't exist
    await createCustomUsersTable();
    
    // Create verification_codes table if it doesn't exist
    await createVerificationCodesTable();
    
    // Create user_sessions table if it doesn't exist
    await createUserSessionsTable();
    
    // Create service_sunset table if it doesn't exist
    await createServiceSunsetTable();
    
    console.log('All custom tables created successfully!');
  } catch (error) {
    console.error('Error creating custom tables:', error);
  }
}

async function createCustomUsersTable() {
  try {
    console.log('Checking custom_users table...');
    
    // Check if the table exists
    const { error: checkError } = await supabaseAdmin
      .from('custom_users')
      .select('count(*)', { count: 'exact', head: true });
    
    if (checkError && checkError.code === '42P01') {
      console.log('custom_users table does not exist, creating it...');
      
      // Create the table using raw SQL
      const { error } = await supabaseAdmin.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.custom_users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            email_verified BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
          );
          
          -- Add RLS policies
          ALTER TABLE public.custom_users ENABLE ROW LEVEL SECURITY;
        `
      });
      
      if (error) {
        console.error('Error creating custom_users table:', error);
      } else {
        console.log('custom_users table created successfully!');
      }
    } else if (checkError) {
      console.error('Error checking custom_users table:', checkError);
    } else {
      console.log('custom_users table already exists!');
    }
  } catch (error) {
    console.error('Error in createCustomUsersTable:', error);
  }
}

async function createVerificationCodesTable() {
  try {
    console.log('Checking verification_codes table...');
    
    // Check if the table exists
    const { error: checkError } = await supabaseAdmin
      .from('verification_codes')
      .select('count(*)', { count: 'exact', head: true });
    
    if (checkError && checkError.code === '42P01') {
      console.log('verification_codes table does not exist, creating it...');
      
      // Create the table using raw SQL
      const { error } = await supabaseAdmin.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.verification_codes (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email TEXT NOT NULL,
            code TEXT NOT NULL,
            used BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL
          );
          
          -- Add indexes for faster lookups
          CREATE INDEX IF NOT EXISTS verification_codes_email_idx ON public.verification_codes (email);
          CREATE INDEX IF NOT EXISTS verification_codes_code_idx ON public.verification_codes (code);
        `
      });
      
      if (error) {
        console.error('Error creating verification_codes table:', error);
      } else {
        console.log('verification_codes table created successfully!');
      }
    } else if (checkError) {
      console.error('Error checking verification_codes table:', checkError);
    } else {
      console.log('verification_codes table already exists!');
    }
  } catch (error) {
    console.error('Error in createVerificationCodesTable:', error);
  }
}

async function createUserSessionsTable() {
  try {
    console.log('Checking user_sessions table...');
    
    // Check if the table exists
    const { error: checkError } = await supabaseAdmin
      .from('user_sessions')
      .select('count(*)', { count: 'exact', head: true });
    
    if (checkError && checkError.code === '42P01') {
      console.log('user_sessions table does not exist, creating it...');
      
      // Create the table using raw SQL
      const { error } = await supabaseAdmin.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.user_sessions (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES public.custom_users(id) ON DELETE CASCADE,
            session_token TEXT UNIQUE NOT NULL,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
          );
          
          -- Add indexes for faster lookups
          CREATE INDEX IF NOT EXISTS user_sessions_user_id_idx ON public.user_sessions (user_id);
          CREATE INDEX IF NOT EXISTS user_sessions_session_token_idx ON public.user_sessions (session_token);
        `
      });
      
      if (error) {
        console.error('Error creating user_sessions table:', error);
      } else {
        console.log('user_sessions table created successfully!');
      }
    } else if (checkError) {
      console.error('Error checking user_sessions table:', checkError);
    } else {
      console.log('user_sessions table already exists!');
    }
  } catch (error) {
    console.error('Error in createUserSessionsTable:', error);
  }
}

async function createServiceSunsetTable() {
  try {
    console.log('Checking service_sunset table...');
    
    // Check if the table exists
    const { error: checkError } = await supabaseAdmin
      .from('service_sunset')
      .select('count(*)', { count: 'exact', head: true });
    
    if (checkError && checkError.code === '42P01') {
      console.log('service_sunset table does not exist, creating it...');
      
      // Create the table using raw SQL
      const { error } = await supabaseAdmin.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.service_sunset (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            description TEXT,
            website TEXT,
            account_number TEXT,
            username TEXT,
            password_hint TEXT,
            contact_info TEXT,
            cancellation_method TEXT NOT NULL,
            cancellation_instructions TEXT,
            priority TEXT NOT NULL,
            auto_renewal BOOLEAN NOT NULL DEFAULT FALSE,
            renewal_date TIMESTAMP WITH TIME ZONE,
            cost_per_period DECIMAL(15, 2),
            period TEXT,
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
          );
          
          -- Add RLS (Row Level Security) policies for service_sunset table
          ALTER TABLE public.service_sunset ENABLE ROW LEVEL SECURITY;
          
          -- Policy for users to view their own services
          CREATE POLICY "Users can view their own services"
          ON public.service_sunset
          FOR SELECT
          USING (auth.uid() = user_id);
          
          -- Policy for users to insert their own services
          CREATE POLICY "Users can insert their own services"
          ON public.service_sunset
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
          
          -- Policy for users to update their own services
          CREATE POLICY "Users can update their own services"
          ON public.service_sunset
          FOR UPDATE
          USING (auth.uid() = user_id);
          
          -- Policy for users to delete their own services
          CREATE POLICY "Users can delete their own services"
          ON public.service_sunset
          FOR DELETE
          USING (auth.uid() = user_id);
        `
      });
      
      if (error) {
        console.error('Error creating service_sunset table:', error);
        
        // Try a simpler approach without RLS
        console.log('Trying simpler table creation without RLS...');
        const { error: simpleError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS public.service_sunset (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              user_id UUID NOT NULL,
              name TEXT NOT NULL,
              category TEXT NOT NULL,
              description TEXT,
              website TEXT,
              account_number TEXT,
              username TEXT,
              password_hint TEXT,
              contact_info TEXT,
              cancellation_method TEXT NOT NULL,
              cancellation_instructions TEXT,
              priority TEXT NOT NULL,
              auto_renewal BOOLEAN NOT NULL DEFAULT FALSE,
              renewal_date TIMESTAMP WITH TIME ZONE,
              cost_per_period DECIMAL(15, 2),
              period TEXT,
              notes TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );
          `
        });
        
        if (simpleError) {
          console.error('Error with simple table creation:', simpleError);
          
          // Try direct HTTP request to create the table
          console.log('Trying direct HTTP request...');
          
          const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
              'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
              'Prefer': 'return=minimal'
            },
            body: JSON.stringify({
              name: 'service_sunset',
              user_id: 'uuid',
              name: 'text',
              category: 'text',
              description: 'text',
              website: 'text',
              account_number: 'text',
              username: 'text',
              password_hint: 'text',
              contact_info: 'text',
              cancellation_method: 'text',
              cancellation_instructions: 'text',
              priority: 'text',
              auto_renewal: 'boolean',
              renewal_date: 'timestamptz',
              cost_per_period: 'numeric',
              period: 'text',
              notes: 'text'
            })
          });
          
          if (!response.ok) {
            console.error('Error with direct HTTP request:', await response.text());
          } else {
            console.log('Table created with direct HTTP request!');
          }
        } else {
          console.log('Simple table creation successful!');
        }
      } else {
        console.log('service_sunset table created successfully!');
      }
    } else if (checkError) {
      console.error('Error checking service_sunset table:', checkError);
      
      // If the error is not that the table doesn't exist, try to create it anyway
      console.log('Attempting to create service_sunset table anyway...');
      
      // Create the table using raw SQL with a different approach
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify({
          name: 'service_sunset',
          columns: [
            { name: 'id', type: 'uuid', primaryKey: true },
            { name: 'user_id', type: 'uuid', notNull: true },
            { name: 'name', type: 'text', notNull: true },
            { name: 'category', type: 'text', notNull: true },
            { name: 'description', type: 'text' },
            { name: 'website', type: 'text' },
            { name: 'account_number', type: 'text' },
            { name: 'username', type: 'text' },
            { name: 'password_hint', type: 'text' },
            { name: 'contact_info', type: 'text' },
            { name: 'cancellation_method', type: 'text', notNull: true },
            { name: 'cancellation_instructions', type: 'text' },
            { name: 'priority', type: 'text', notNull: true },
            { name: 'auto_renewal', type: 'boolean', notNull: true, default: false },
            { name: 'renewal_date', type: 'timestamptz' },
            { name: 'cost_per_period', type: 'numeric' },
            { name: 'period', type: 'text' },
            { name: 'notes', type: 'text' },
            { name: 'created_at', type: 'timestamptz', notNull: true, default: 'now()' },
            { name: 'updated_at', type: 'timestamptz', notNull: true, default: 'now()' }
          ]
        })
      });
      
      if (!response.ok) {
        console.error('Error creating table with direct HTTP request:', await response.text());
      } else {
        console.log('Table created with direct HTTP request!');
      }
    } else {
      console.log('service_sunset table already exists!');
    }
  } catch (error) {
    console.error('Error in createServiceSunsetTable:', error);
  }
}

createCustomTables();
