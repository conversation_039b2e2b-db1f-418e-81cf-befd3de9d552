require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createServiceSunsetTable() {
  try {
    console.log('Creating service_sunset table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'create-service-sunset-table.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabaseAdmin.rpc('exec_sql', { sql: sqlContent });
    
    if (error) {
      if (error.message.includes('function "exec_sql" does not exist')) {
        console.log('exec_sql function not available, trying direct SQL execution...');
        
        // Try using the Supabase CLI to execute the SQL
        console.log('Executing SQL using Supabase CLI...');
        exec(`supabase db execute --file ${sqlFilePath}`, (err, stdout, stderr) => {
          if (err) {
            console.error('Error executing SQL with Supabase CLI:', err);
            console.log('Trying alternative approach...');
            
            // Try executing the SQL directly with psql
            const tempFilePath = path.join(__dirname, 'temp-db-url.txt');
            exec('supabase db remote get > ' + tempFilePath, async (err, stdout, stderr) => {
              if (err) {
                console.error('Error getting database URL:', err);
                return;
              }
              
              const dbUrl = fs.readFileSync(tempFilePath, 'utf8').trim();
              fs.unlinkSync(tempFilePath); // Clean up
              
              exec(`psql "${dbUrl}" -f ${sqlFilePath}`, (err, stdout, stderr) => {
                if (err) {
                  console.error('Error executing SQL with psql:', err);
                } else {
                  console.log('SQL executed successfully with psql!');
                }
              });
            });
          } else {
            console.log('SQL executed successfully with Supabase CLI!');
          }
        });
      } else {
        console.error('Error executing SQL:', error);
      }
    } else {
      console.log('SQL executed successfully!');
    }
    
    // Check if the table exists
    const { data, error: checkError } = await supabaseAdmin
      .from('service_sunset')
      .select('count(*)', { count: 'exact', head: true });
    
    if (checkError) {
      console.error('Error checking if table exists:', checkError);
    } else {
      console.log('service_sunset table exists and is accessible!');
    }
    
  } catch (error) {
    console.error('Error creating service_sunset table:', error);
  }
}

createServiceSunsetTable();
