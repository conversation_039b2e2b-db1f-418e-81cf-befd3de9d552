require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createStorageBuckets() {
  try {
    console.log('Creating storage buckets...');
    
    // Create documents bucket
    await createBucket('documents', 'Documents storage for vault');
    
    // Create time_capsule_media bucket
    await createBucket('time_capsule_media', 'Media storage for time capsules');
    
    // Set up RLS policies for the buckets
    await setupBucketPolicies();
    
    console.log('Storage buckets created and configured successfully!');
  } catch (error) {
    console.error('Error creating storage buckets:', error);
  }
}

async function createBucket(name, description) {
  try {
    console.log(`Creating ${name} bucket...`);
    
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    
    if (listError) {
      console.error(`Error listing buckets:`, listError);
      return;
    }
    
    const bucketExists = buckets.some(bucket => bucket.name === name);
    
    if (bucketExists) {
      console.log(`Bucket ${name} already exists.`);
    } else {
      // Create the bucket
      const { data, error } = await supabaseAdmin.storage.createBucket(name, {
        public: false,
        fileSizeLimit: 52428800, // 50MB
        allowedMimeTypes: ['*/*'],
      });
      
      if (error) {
        console.error(`Error creating ${name} bucket:`, error);
      } else {
        console.log(`Bucket ${name} created successfully.`);
      }
    }
  } catch (error) {
    console.error(`Error in createBucket for ${name}:`, error);
  }
}

async function setupBucketPolicies() {
  try {
    console.log('Setting up bucket policies...');
    
    // Documents bucket policies
    await setupBucketPolicy('documents', 'SELECT', 'authenticated', '(bucket_id = \'documents\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    await setupBucketPolicy('documents', 'INSERT', 'authenticated', '(bucket_id = \'documents\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    await setupBucketPolicy('documents', 'UPDATE', 'authenticated', '(bucket_id = \'documents\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    await setupBucketPolicy('documents', 'DELETE', 'authenticated', '(bucket_id = \'documents\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    
    // Time capsule media bucket policies
    await setupBucketPolicy('time_capsule_media', 'SELECT', 'authenticated', '(bucket_id = \'time_capsule_media\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    await setupBucketPolicy('time_capsule_media', 'INSERT', 'authenticated', '(bucket_id = \'time_capsule_media\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    await setupBucketPolicy('time_capsule_media', 'UPDATE', 'authenticated', '(bucket_id = \'time_capsule_media\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    await setupBucketPolicy('time_capsule_media', 'DELETE', 'authenticated', '(bucket_id = \'time_capsule_media\' AND auth.uid() = (storage.foldername(name))[1]::uuid)');
    
  } catch (error) {
    console.error('Error setting up bucket policies:', error);
  }
}

async function setupBucketPolicy(bucketName, operation, role, condition) {
  try {
    console.log(`Setting up ${operation} policy for ${bucketName} bucket...`);
    
    const policyName = `${role} can ${operation.toLowerCase()} their own files`;
    
    // Create the policy
    const { data, error } = await supabaseAdmin.storage.from(bucketName).createPolicy(policyName, {
      name: policyName,
      definition: condition,
      operation: operation,
      role: role
    });
    
    if (error) {
      // If policy already exists, this is fine
      if (error.message.includes('already exists')) {
        console.log(`Policy ${policyName} already exists for ${bucketName}.`);
      } else {
        console.error(`Error creating policy for ${bucketName}:`, error);
      }
    } else {
      console.log(`Policy ${policyName} created successfully for ${bucketName}.`);
    }
  } catch (error) {
    console.error(`Error in setupBucketPolicy for ${bucketName}:`, error);
  }
}

createStorageBuckets();
