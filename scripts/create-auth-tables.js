require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createAuthTables() {
  try {
    console.log('Checking and creating custom auth tables if they don\'t exist...');

    // Check if custom_users table exists
    const { data: customUsersExists, error: customUsersCheckError } = await supabaseAdmin
      .from('custom_users')
      .select('count(*)', { count: 'exact', head: true });

    if (customUsersCheckError && customUsersCheckError.code === '42P01') {
      console.log('Creating custom_users table...');
      
      // Create custom_users table
      const { error: createCustomUsersError } = await supabaseAdmin.rpc('create_custom_users_table', {});
      
      if (createCustomUsersError) {
        // If RPC doesn't exist, create the table with raw SQL
        const { error: sqlError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS public.custom_users (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              email TEXT UNIQUE NOT NULL,
              password_hash TEXT NOT NULL,
              first_name TEXT NOT NULL,
              last_name TEXT NOT NULL,
              email_verified BOOLEAN NOT NULL DEFAULT FALSE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );
            
            -- Add RLS policies
            ALTER TABLE public.custom_users ENABLE ROW LEVEL SECURITY;
            
            -- Create trigger to update updated_at
            CREATE OR REPLACE FUNCTION update_custom_users_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            
            CREATE TRIGGER update_custom_users_updated_at
            BEFORE UPDATE ON public.custom_users
            FOR EACH ROW
            EXECUTE FUNCTION update_custom_users_updated_at();
          `
        });
        
        if (sqlError) {
          console.error('Error creating custom_users table with SQL:', sqlError);
        } else {
          console.log('Created custom_users table with SQL');
        }
      } else {
        console.log('Created custom_users table with RPC');
      }
    } else {
      console.log('custom_users table already exists');
    }

    // Check if verification_codes table exists
    const { data: verificationCodesExists, error: verificationCodesCheckError } = await supabaseAdmin
      .from('verification_codes')
      .select('count(*)', { count: 'exact', head: true });

    if (verificationCodesCheckError && verificationCodesCheckError.code === '42P01') {
      console.log('Creating verification_codes table...');
      
      // Create verification_codes table
      const { error: createVerificationCodesError } = await supabaseAdmin.rpc('create_verification_codes_table', {});
      
      if (createVerificationCodesError) {
        // If RPC doesn't exist, create the table with raw SQL
        const { error: sqlError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS public.verification_codes (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              email TEXT NOT NULL,
              code TEXT NOT NULL,
              used BOOLEAN NOT NULL DEFAULT FALSE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
              expires_at TIMESTAMP WITH TIME ZONE NOT NULL
            );
            
            -- Add index for faster lookups
            CREATE INDEX IF NOT EXISTS verification_codes_email_idx ON public.verification_codes (email);
            CREATE INDEX IF NOT EXISTS verification_codes_code_idx ON public.verification_codes (code);
            CREATE INDEX IF NOT EXISTS verification_codes_expires_at_idx ON public.verification_codes (expires_at);
          `
        });
        
        if (sqlError) {
          console.error('Error creating verification_codes table with SQL:', sqlError);
        } else {
          console.log('Created verification_codes table with SQL');
        }
      } else {
        console.log('Created verification_codes table with RPC');
      }
    } else {
      console.log('verification_codes table already exists');
    }

    // Check if user_sessions table exists
    const { data: userSessionsExists, error: userSessionsCheckError } = await supabaseAdmin
      .from('user_sessions')
      .select('count(*)', { count: 'exact', head: true });

    if (userSessionsCheckError && userSessionsCheckError.code === '42P01') {
      console.log('Creating user_sessions table...');
      
      // Create user_sessions table
      const { error: createUserSessionsError } = await supabaseAdmin.rpc('create_user_sessions_table', {});
      
      if (createUserSessionsError) {
        // If RPC doesn't exist, create the table with raw SQL
        const { error: sqlError } = await supabaseAdmin.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS public.user_sessions (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              user_id UUID NOT NULL REFERENCES public.custom_users(id) ON DELETE CASCADE,
              session_token TEXT UNIQUE NOT NULL,
              expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );
            
            -- Add index for faster lookups
            CREATE INDEX IF NOT EXISTS user_sessions_user_id_idx ON public.user_sessions (user_id);
            CREATE INDEX IF NOT EXISTS user_sessions_session_token_idx ON public.user_sessions (session_token);
            CREATE INDEX IF NOT EXISTS user_sessions_expires_at_idx ON public.user_sessions (expires_at);
          `
        });
        
        if (sqlError) {
          console.error('Error creating user_sessions table with SQL:', sqlError);
        } else {
          console.log('Created user_sessions table with SQL');
        }
      } else {
        console.log('Created user_sessions table with RPC');
      }
    } else {
      console.log('user_sessions table already exists');
    }

    console.log('Custom auth tables setup complete!');
  } catch (error) {
    console.error('Error setting up custom auth tables:', error);
  }
}

createAuthTables();
