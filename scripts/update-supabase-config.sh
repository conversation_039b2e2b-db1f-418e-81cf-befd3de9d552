#!/bin/bash

# Create a temporary file with the updated configuration
cat > supabase/config.toml.new << EOL
project_id = "ytjvojtdowufgpwgpncf"

[storage]
file_size_limit = "50MiB"

[functions.estate-recommendations]
verify_jwt = true

[functions.deliver-time-capsule]
verify_jwt = false

# Disable built-in auth - we're using custom auth
[auth]
enabled = false

# Keep the functions configuration
[functions.send-email-hook]
enabled = true
verify_jwt = false
import_map = "./functions/send-email-hook/deno.json"
entrypoint = "./functions/send-email-hook/index.ts"
EOL

# Backup the old configuration
cp supabase/config.toml supabase/config.toml.backup

# Replace the old configuration with the new one
mv supabase/config.toml.new supabase/config.toml

echo "Supabase configuration updated successfully!"
