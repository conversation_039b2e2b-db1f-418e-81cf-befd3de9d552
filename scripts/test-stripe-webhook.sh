#!/bin/bash

# This script tests the Stripe webhook integration by forwarding events to the local webhook endpoint

# Start the Next.js development server in the background
echo "Starting Next.js development server..."
npm run dev &
DEV_SERVER_PID=$!

# Wait for the server to start
echo "Waiting for server to start..."
sleep 10

# Start the Stripe CLI webhook forwarding
echo "Starting Stripe webhook forwarding..."
stripe listen --forward-to http://localhost:3000/api/stripe/webhook

# Clean up when the script is interrupted
trap "kill $DEV_SERVER_PID; echo 'Shutting down...'" SIGINT SIGTERM

# Wait for the Stripe CLI to exit
wait
