#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function checkTables() {
  try {
    // Check if verification_codes table exists
    const { data: verificationCodesExists, error: verificationCodesError } = await supabaseAdmin
      .from('verification_codes')
      .select('*')
      .limit(1);

    if (verificationCodesError) {
      console.error('Error checking verification_codes table:', verificationCodesError);
      console.log('verification_codes table does not exist or is not accessible');
    } else {
      console.log('verification_codes table exists');
    }

    // Check if custom_users table exists
    const { data: customUsersExists, error: customUsersError } = await supabaseAdmin
      .from('custom_users')
      .select('*')
      .limit(1);

    if (customUsersError) {
      console.error('Error checking custom_users table:', customUsersError);
      console.log('custom_users table does not exist or is not accessible');
    } else {
      console.log('custom_users table exists');
    }

    // Check if user_sessions table exists
    const { data: userSessionsExists, error: userSessionsError } = await supabaseAdmin
      .from('user_sessions')
      .select('*')
      .limit(1);

    if (userSessionsError) {
      console.error('Error checking user_sessions table:', userSessionsError);
      console.log('user_sessions table does not exist or is not accessible');
    } else {
      console.log('user_sessions table exists');
    }
  } catch (error) {
    console.error('Error checking tables:', error);
  }
}

checkTables();
