-- SQL script to update Supa<PERSON> database for subscription tiers

-- First, check if the profiles table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
        -- Create the profiles table if it doesn't exist
        CREATE TABLE public.profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            email TEXT,
            first_name TEXT,
            last_name TEXT,
            subscription_tier TEXT DEFAULT 'free',
            subscription_status TEXT DEFAULT 'inactive',
            subscription_start_date TIMESTAMP WITH TIME ZONE,
            subscription_end_date TIMESTAMP WITH TIME ZONE,
            stripe_customer_id TEXT,
            stripe_subscription_id TEXT
        );

        -- Add RLS policies for the profiles table
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

        -- Create policy to allow users to view their own profile
        CREATE POLICY "Users can view their own profile"
            ON public.profiles
            FOR SELECT
            USING (auth.uid() = id);

        -- Create policy to allow users to update their own profile
        CREATE POLICY "Users can update their own profile"
            ON public.profiles
            FOR UPDATE
            USING (auth.uid() = id);

        -- Create policy to allow service role to manage all profiles
        CREATE POLICY "Service role can manage all profiles"
            ON public.profiles
            USING (auth.role() = 'service_role');
    ELSE
        -- Add subscription columns if they don't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'subscription_tier') THEN
            ALTER TABLE public.profiles ADD COLUMN subscription_tier TEXT DEFAULT 'free';
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'subscription_status') THEN
            ALTER TABLE public.profiles ADD COLUMN subscription_status TEXT DEFAULT 'inactive';
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'subscription_start_date') THEN
            ALTER TABLE public.profiles ADD COLUMN subscription_start_date TIMESTAMP WITH TIME ZONE;
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'subscription_end_date') THEN
            ALTER TABLE public.profiles ADD COLUMN subscription_end_date TIMESTAMP WITH TIME ZONE;
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'stripe_customer_id') THEN
            ALTER TABLE public.profiles ADD COLUMN stripe_customer_id TEXT;
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'stripe_subscription_id') THEN
            ALTER TABLE public.profiles ADD COLUMN stripe_subscription_id TEXT;
        END IF;
    END IF;
END
$$;

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to update the updated_at column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'update_profiles_updated_at'
        AND tgrelid = 'public.profiles'::regclass
    ) THEN
        CREATE TRIGGER update_profiles_updated_at
        BEFORE UPDATE ON public.profiles
        FOR EACH ROW
        EXECUTE FUNCTION public.update_updated_at_column();
    END IF;
END
$$;

-- Update all existing users to have the free tier
UPDATE public.profiles
SET subscription_tier = 'free'
WHERE subscription_tier IS NULL OR subscription_tier = '';

-- Create a view to show subscription limits for each user
CREATE OR REPLACE VIEW public.user_subscription_limits AS
SELECT
    p.id,
    p.email,
    p.subscription_tier,
    p.subscription_status,
    CASE
        WHEN p.subscription_tier = 'premium' THEN 5
        ELSE 1
    END AS max_trustees,
    CASE
        WHEN p.subscription_tier = 'premium' THEN NULL -- NULL represents unlimited
        ELSE NULL -- Free tier also has unlimited assets
    END AS max_assets,
    CASE
        WHEN p.subscription_tier = 'premium' THEN 5368709120 -- 5GB in bytes
        ELSE 1048576 -- 1MB in bytes (effectively 1 document)
    END AS max_vault_storage_bytes,
    CASE
        WHEN p.subscription_tier = 'premium' THEN 100
        ELSE 0
    END AS max_time_capsules,
    CASE
        WHEN p.subscription_tier = 'premium' THEN NULL -- NULL represents unlimited
        ELSE NULL -- Free tier also has unlimited contacts
    END AS max_contacts,
    CASE
        WHEN p.subscription_tier = 'premium' THEN NULL -- NULL represents unlimited
        ELSE NULL -- Free tier also has unlimited service sunset
    END AS max_service_sunset
FROM
    public.profiles p;

-- Grant access to the view
GRANT SELECT ON public.user_subscription_limits TO authenticated;
GRANT SELECT ON public.user_subscription_limits TO service_role;

-- Create a function to check if a user has reached their limit
CREATE OR REPLACE FUNCTION public.has_reached_limit(
    user_id UUID,
    limit_type TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    user_tier TEXT;
    user_limit INTEGER;
    current_count INTEGER;
BEGIN
    -- Get the user's subscription tier
    SELECT subscription_tier INTO user_tier
    FROM public.profiles
    WHERE id = user_id;

    -- Default to free tier if not found
    IF user_tier IS NULL THEN
        user_tier := 'free';
    END IF;

    -- Check the specific limit
    CASE limit_type
        WHEN 'trustees' THEN
            -- Get the user's limit
            SELECT
                CASE
                    WHEN user_tier = 'premium' THEN 5
                    ELSE 1
                END INTO user_limit;

            -- Count the user's trustees
            SELECT COUNT(*) INTO current_count
            FROM public.trustees
            WHERE user_id = user_id;

        WHEN 'vault_storage' THEN
            -- Get the user's limit
            SELECT
                CASE
                    WHEN user_tier = 'premium' THEN 5368709120 -- 5GB in bytes
                    ELSE 1048576 -- 1MB in bytes
                END INTO user_limit;

            -- Sum the user's document sizes
            SELECT COALESCE(SUM(file_size), 0) INTO current_count
            FROM public.vault_documents
            WHERE user_id = user_id;

        WHEN 'time_capsules' THEN
            -- Get the user's limit
            SELECT
                CASE
                    WHEN user_tier = 'premium' THEN 100
                    ELSE 0
                END INTO user_limit;

            -- Count the user's time capsules
            SELECT COUNT(*) INTO current_count
            FROM public.time_capsules
            WHERE user_id = user_id;

        ELSE
            -- Unknown limit type
            RETURN FALSE;
    END CASE;

    -- Check if the user has reached their limit
    RETURN current_count >= user_limit;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.has_reached_limit TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_reached_limit TO service_role;
