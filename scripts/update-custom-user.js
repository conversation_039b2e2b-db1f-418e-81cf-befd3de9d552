// Script to update a user's subscription plan directly in the custom_users table
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function updateUserSubscription() {
  try {
    console.log('Updating user subscription...');
    
    // Get the user from custom_users table
    const { data: users, error: userError } = await supabaseAdmin
      .from('custom_users')
      .select('*')
      .eq('email', '<EMAIL>');
    
    if (userError) {
      console.error('Error getting user:', userError);
      return;
    }
    
    if (users.length === 0) {
      console.log('User not found');
      return;
    }
    
    const user = users[0];
    console.log('User found:', user);
    
    // Add a subscription_tier field to the custom_users table if it doesn't exist
    try {
      const { error: alterError } = await supabaseAdmin.rpc('exec_sql', {
        sql: `
          ALTER TABLE public.custom_users 
          ADD COLUMN IF NOT EXISTS subscription_tier TEXT DEFAULT 'free' NOT NULL;
        `
      });
      
      if (alterError) {
        console.error('Error adding subscription_tier column:', alterError);
        // Continue anyway, as the column might already exist
      }
    } catch (error) {
      console.error('Error in alter table:', error);
      // Continue anyway, as the column might already exist
    }
    
    // Update the user's subscription tier
    const { error: updateError } = await supabaseAdmin
      .from('custom_users')
      .update({
        subscription_tier: 'free',
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (updateError) {
      console.error('Error updating user:', updateError);
      return;
    }
    
    console.log('User subscription updated successfully!');
  } catch (error) {
    console.error('Error updating user subscription:', error);
  }
}

// Run the function
updateUserSubscription();
