// Script to update a user's subscription plan
require('dotenv').config({ path: '.env.local' });
const fetch = require('node-fetch');

async function updateUserSubscription() {
  try {
    console.log('Updating user subscription...');
    
    // First, create the profiles table if it doesn't exist
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.profiles (
        id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
        first_name TEXT,
        last_name TEXT,
        email TEXT,
        avatar_url TEXT,
        subscription_tier TEXT DEFAULT 'free' NOT NULL,
        subscription_status TEXT DEFAULT 'active' NOT NULL,
        subscription_start_date TIMESTAMP WITH TIME ZONE,
        subscription_end_date TIMESTAMP WITH TIME ZONE,
        stripe_customer_id TEXT,
        stripe_subscription_id TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
      );
    `;
    
    // Execute the SQL using the REST API
    const createTableResponse = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({ sql: createTableSQL })
      }
    );
    
    console.log('Create table response:', await createTableResponse.text());
    
    // Get the user from custom_users table
    const getUserResponse = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/custom_users?email=<EMAIL>`,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      }
    );
    
    const users = await getUserResponse.json();
    console.log('Users found:', users);
    
    if (users.length === 0) {
      console.log('User not found');
      return;
    }
    
    const userId = users[0].id;
    console.log('User ID:', userId);
    
    // Check if profile exists
    const getProfileResponse = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}`,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      }
    );
    
    const profiles = await getProfileResponse.json();
    console.log('Profiles found:', profiles);
    
    if (profiles.length === 0) {
      // Create profile
      console.log('Creating profile...');
      const createProfileResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/profiles`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify({
            id: userId,
            email: '<EMAIL>',
            first_name: users[0].first_name || '',
            last_name: users[0].last_name || '',
            subscription_tier: 'free',
            subscription_status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
        }
      );
      
      console.log('Create profile response:', await createProfileResponse.text());
    } else {
      // Update profile
      console.log('Updating profile...');
      const updateProfileResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}`,
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify({
            subscription_tier: 'free',
            subscription_status: 'active',
            updated_at: new Date().toISOString()
          })
        }
      );
      
      console.log('Update profile response:', await updateProfileResponse.text());
    }
    
    console.log('User subscription updated successfully!');
  } catch (error) {
    console.error('Error updating user subscription:', error);
  }
}

// Run the function
updateUserSubscription();
