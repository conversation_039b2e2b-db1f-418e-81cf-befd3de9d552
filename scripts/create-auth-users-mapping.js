// <PERSON>ript to create and populate the auth_users_mapping table
require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createAuthUsersMapping() {
  try {
    console.log('Creating and populating auth_users_mapping table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'create-auth-users-mapping.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabaseAdmin.rpc('exec_sql', {
      sql: sqlContent
    });
    
    if (error) {
      console.error('Error creating auth_users_mapping table:', error);
      return;
    }
    
    console.log('Auth users mapping table created and populated successfully!');
    
    // Now let's check if we have any mappings
    const { data: mappings, error: mappingError } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('*');
    
    if (mappingError) {
      console.error('Error fetching mappings:', mappingError);
      return;
    }
    
    console.log(`Found ${mappings.length} user mappings`);
    
    // If no mappings were created, let's try to debug why
    if (mappings.length === 0) {
      // Check custom_users table
      const { data: customUsers, error: customUsersError } = await supabaseAdmin
        .from('custom_users')
        .select('id, email');
      
      if (customUsersError) {
        console.error('Error fetching custom users:', customUsersError);
      } else {
        console.log(`Found ${customUsers.length} custom users`);
        if (customUsers.length > 0) {
          console.log('Sample custom user:', customUsers[0]);
        }
      }
      
      // Check auth.users table
      const { data: authUsers, error: authUsersError } = await supabaseAdmin
        .from('auth.users')
        .select('id, email');
      
      if (authUsersError) {
        console.error('Error fetching auth users:', authUsersError);
        console.log('Note: You might not have permission to directly query auth.users');
      } else {
        console.log(`Found ${authUsers.length} auth users`);
        if (authUsers.length > 0) {
          console.log('Sample auth user:', authUsers[0]);
        }
      }
      
      // Try a direct insert for a specific user
      if (customUsers && customUsers.length > 0 && authUsers && authUsers.length > 0) {
        const customUser = customUsers[0];
        const authUser = authUsers.find(au => au.email.toLowerCase() === customUser.email.toLowerCase());
        
        if (authUser) {
          console.log(`Attempting direct insert for user ${customUser.email}`);
          const { error: insertError } = await supabaseAdmin
            .from('auth_users_mapping')
            .insert({
              custom_user_id: customUser.id,
              auth_user_id: authUser.id
            });
          
          if (insertError) {
            console.error('Error with direct insert:', insertError);
          } else {
            console.log('Direct insert successful!');
          }
        } else {
          console.log(`No matching auth user found for ${customUser.email}`);
        }
      }
    }
  } catch (error) {
    console.error('Error in createAuthUsersMapping:', error);
  }
}

// Run the function
createAuthUsersMapping();
