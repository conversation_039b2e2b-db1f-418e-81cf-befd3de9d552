-- Create the service_sunset table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.service_sunset (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    website TEXT,
    account_number TEXT,
    username TEXT,
    password_hint TEXT,
    contact_info TEXT,
    cancellation_method TEXT NOT NULL,
    cancellation_instructions TEXT,
    priority TEXT NOT NULL,
    auto_renewal BOOLEAN NOT NULL DEFAULT FALSE,
    renewal_date TIMESTAMP WITH TIME ZONE,
    cost_per_period DECIMAL(15, 2),
    period TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add RLS (Row Level Security) policies for service_sunset table
ALTER TABLE public.service_sunset ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own services
CREATE POLICY "Users can view their own services"
ON public.service_sunset
FOR SELECT
USING (auth.uid() = user_id);

-- Policy for users to insert their own services
CREATE POLICY "Users can insert their own services"
ON public.service_sunset
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own services
CREATE POLICY "Users can update their own services"
ON public.service_sunset
FOR UPDATE
USING (auth.uid() = user_id);

-- Policy for users to delete their own services
CREATE POLICY "Users can delete their own services"
ON public.service_sunset
FOR DELETE
USING (auth.uid() = user_id);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS service_sunset_user_id_idx ON public.service_sunset (user_id);
CREATE INDEX IF NOT EXISTS service_sunset_category_idx ON public.service_sunset (category);
CREATE INDEX IF NOT EXISTS service_sunset_priority_idx ON public.service_sunset (priority);

-- Create trigger to update the updated_at column
CREATE OR REPLACE FUNCTION update_service_sunset_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_service_sunset_updated_at ON public.service_sunset;
CREATE TRIGGER update_service_sunset_updated_at
BEFORE UPDATE ON public.service_sunset
FOR EACH ROW
EXECUTE FUNCTION update_service_sunset_updated_at();
