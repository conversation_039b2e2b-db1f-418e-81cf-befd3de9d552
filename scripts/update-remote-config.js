#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute shell commands
function exec(command) {
  console.log(`Executing: ${command}`);
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    return output;
  } catch (error) {
    console.error(`Error executing command: ${error.message}`);
    console.error(error.stdout);
    console.error(error.stderr);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Updating Supabase configuration to disable built-in auth...');

    // Create a temporary configuration file
    const configPath = path.join(process.cwd(), 'temp-config.toml');
    
    // Write the new configuration
    fs.writeFileSync(configPath, `
project_id = "ytjvojtdowufgpwgpncf"

[storage]
file_size_limit = "50MiB"

[functions.estate-recommendations]
verify_jwt = true

[functions.deliver-time-capsule]
verify_jwt = false

# Disable built-in auth - we're using custom auth
[auth]
enabled = false

# Keep the functions configuration
[functions.send-email-hook]
enabled = true
verify_jwt = false
import_map = "./functions/send-email-hook/deno.json"
entrypoint = "./functions/send-email-hook/index.ts"
`);

    console.log('Configuration file created');
    
    // Apply the configuration
    exec(`supabase secrets set --env-file .env.local`);
    
    console.log('Supabase configuration updated successfully!');
    
    // Clean up
    fs.unlinkSync(configPath);
    
  } catch (error) {
    console.error('Error updating Supabase configuration:', error);
    process.exit(1);
  }
}

// Run the main function
main();
