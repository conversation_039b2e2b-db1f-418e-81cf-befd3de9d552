#!/bin/bash

# This script triggers Stripe webhook events for testing

# Set your Stripe API key
STRIPE_API_KEY="sk_test_51QK6h2Rt1fOVdwKGKp8N8Y0kh7mCmtfsBDqPhMm9dMfwWtrugbpFJNCcCoKwObYiwoyPMqtrMFunwtyLL7CUqXzg00eJGqlR4C"

# Function to trigger a checkout.session.completed event
trigger_checkout_completed() {
  echo "Triggering checkout.session.completed event..."
  stripe trigger checkout.session.completed --api-key $STRIPE_API_KEY
}

# Function to trigger a customer.subscription.updated event
trigger_subscription_updated() {
  echo "Triggering customer.subscription.updated event..."
  stripe trigger customer.subscription.updated --api-key $STRIPE_API_KEY
}

# Function to trigger a customer.subscription.deleted event
trigger_subscription_deleted() {
  echo "Triggering customer.subscription.deleted event..."
  stripe trigger customer.subscription.deleted --api-key $STRIPE_API_KEY
}

# Main menu
echo "Stripe Webhook Event Trigger"
echo "==========================="
echo "1. Trigger checkout.session.completed"
echo "2. Trigger customer.subscription.updated"
echo "3. Trigger customer.subscription.deleted"
echo "4. Exit"
echo ""
read -p "Enter your choice (1-4): " choice

case $choice in
  1)
    trigger_checkout_completed
    ;;
  2)
    trigger_subscription_updated
    ;;
  3)
    trigger_subscription_deleted
    ;;
  4)
    echo "Exiting..."
    exit 0
    ;;
  *)
    echo "Invalid choice. Exiting..."
    exit 1
    ;;
esac
