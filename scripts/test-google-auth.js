// Simple script to test Google OAuth configuration
const { createClient } = require('@supabase/supabase-js');

// Define the URL and key with fallbacks to prevent initialization errors
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ccwvtcudztphwwzzgwvg.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rwLPXx6OCe-lvKa1cXJCUlIkzgKmVLrP1LyCWWQzyps';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testGoogleAuth() {
  try {
    // Use the Supabase callback URL directly
    const redirectTo = 'https://ccwvtcudztphwwzzgwvg.supabase.co/auth/v1/callback';

    console.log('Using redirect URL:', redirectTo);

    // Get the Google OAuth URL
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo,
        scopes: 'email profile',
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      console.error('Error generating Google OAuth URL:', error);
      return;
    }

    console.log('Google OAuth URL generated successfully:');
    console.log('URL:', data.url);
    console.log('\nOpen this URL in your browser to test the OAuth flow.');
    console.log('After authentication, you should be redirected to your application.');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testGoogleAuth();
