require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createServiceSunsetTable() {
  try {
    console.log('Creating service_sunset table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'create-service-sunset-table-fixed.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL using the REST API
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({ sql: sqlContent })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error executing SQL:', errorData);
      console.log('Trying alternative approach...');
      
      // Try creating the table directly with the Supabase client
      console.log('Creating table directly...');
      
      // First check if the table exists
      const { error: checkError } = await supabaseAdmin.from('service_sunset').select('count(*)', { count: 'exact', head: true });
      
      if (checkError && checkError.code === '42P01') { // Table doesn't exist
        console.log('Table does not exist, creating it...');
        
        // Create the table
        const { error: createError } = await supabaseAdmin.rpc('create_service_sunset_table');
        
        if (createError) {
          console.error('Error creating table:', createError);
          
          // Try a simpler approach - just create the table without RLS, etc.
          console.log('Trying simpler table creation...');
          const simpleSQL = `
            CREATE TABLE IF NOT EXISTS public.service_sunset (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              user_id UUID NOT NULL,
              name TEXT NOT NULL,
              category TEXT NOT NULL,
              description TEXT,
              website TEXT,
              account_number TEXT,
              username TEXT,
              password_hint TEXT,
              contact_info TEXT,
              cancellation_method TEXT NOT NULL,
              cancellation_instructions TEXT,
              priority TEXT NOT NULL,
              auto_renewal BOOLEAN NOT NULL DEFAULT FALSE,
              renewal_date TIMESTAMP WITH TIME ZONE,
              cost_per_period DECIMAL(15, 2),
              period TEXT,
              notes TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );
          `;
          
          const simpleResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
              'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
            },
            body: JSON.stringify({ sql: simpleSQL })
          });
          
          if (!simpleResponse.ok) {
            const simpleErrorData = await simpleResponse.json();
            console.error('Error with simple table creation:', simpleErrorData);
          } else {
            console.log('Simple table creation successful!');
          }
        } else {
          console.log('Table created successfully!');
        }
      } else if (checkError) {
        console.error('Error checking if table exists:', checkError);
      } else {
        console.log('Table already exists!');
      }
    } else {
      console.log('SQL executed successfully!');
    }
    
    // Final check if the table exists
    const { data, error: finalCheckError } = await supabaseAdmin
      .from('service_sunset')
      .select('count(*)', { count: 'exact', head: true });
    
    if (finalCheckError) {
      console.error('Error checking if table exists after creation:', finalCheckError);
    } else {
      console.log('service_sunset table exists and is accessible!');
    }
    
  } catch (error) {
    console.error('Error creating service_sunset table:', error);
  }
}

createServiceSunsetTable();
