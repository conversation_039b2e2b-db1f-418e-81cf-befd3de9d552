// Script to update a user's subscription plan
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function updateUserSubscription() {
  try {
    console.log('Updating user subscription...');

    // Get the user ID from the email
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin
      .listUsers()
      .then(({ data }) => {
        const user = data.users.find(u => u.email === '<EMAIL>');
        return { data: user ? { id: user.id } : null, error: user ? null : new Error('User not found') };
      })
      .catch(error => ({ data: null, error }));

    if (userError) {
      console.error('Error finding user:', userError);

      // Try with custom_users table
      console.log('Trying with custom_users table...');
      const { data: customUserData, error: customUserError } = await supabaseAdmin
        .from('custom_users')
        .select('id')
        .eq('email', '<EMAIL>')
        .single();

      if (customUserError) {
        console.error('Error finding user in custom_users table:', customUserError);
        return;
      }

      console.log('User found in custom_users table:', customUserData);

      // Check if profile exists
      const { data: profileData, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('id', customUserData.id)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        console.log('Creating profile for user...');
        const { error: insertError } = await supabaseAdmin
          .from('profiles')
          .insert({
            id: customUserData.id,
            email: '<EMAIL>',
            first_name: '',
            last_name: '',
            subscription_tier: 'free',
            subscription_status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error creating profile:', insertError);
          return;
        }

        console.log('Profile created successfully!');
      } else if (profileError) {
        console.error('Error checking profile:', profileError);
        return;
      } else {
        // Profile exists, update it
        console.log('Updating existing profile...');
        const { error: updateError } = await supabaseAdmin
          .from('profiles')
          .update({
            subscription_tier: 'free',
            subscription_status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq('id', customUserData.id);

        if (updateError) {
          console.error('Error updating profile:', updateError);
          return;
        }

        console.log('Profile updated successfully!');
      }
    } else {
      console.log('User found in auth.users table:', userData);

      // Check if profile exists
      const { data: profileData, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('id', userData.id)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        console.log('Creating profile for user...');
        const { error: insertError } = await supabaseAdmin
          .from('profiles')
          .insert({
            id: userData.id,
            email: '<EMAIL>',
            first_name: '',
            last_name: '',
            subscription_tier: 'free',
            subscription_status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error creating profile:', insertError);
          return;
        }

        console.log('Profile created successfully!');
      } else if (profileError) {
        console.error('Error checking profile:', profileError);
        return;
      } else {
        // Profile exists, update it
        console.log('Updating existing profile...');
        const { error: updateError } = await supabaseAdmin
          .from('profiles')
          .update({
            subscription_tier: 'free',
            subscription_status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq('id', userData.id);

        if (updateError) {
          console.error('Error updating profile:', updateError);
          return;
        }

        console.log('Profile updated successfully!');
      }
    }
  } catch (error) {
    console.error('Error updating user subscription:', error);
  }
}

// Run the function
updateUserSubscription();
