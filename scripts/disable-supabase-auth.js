#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to execute shell commands
function exec(command) {
  console.log(`Executing: ${command}`);
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    return output;
  } catch (error) {
    console.error(`Error executing command: ${error.message}`);
    console.error(error.stdout);
    console.error(error.stderr);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Updating Supabase configuration to disable built-in auth...');

    // Create a temporary configuration file
    const configPath = path.join(process.cwd(), 'supabase/config.toml');
    
    // Read the current configuration
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    // Check if auth section exists
    if (configContent.includes('[auth]')) {
      // Update the auth section
      configContent = configContent.replace(/\[auth\][\s\S]*?(?=\[|$)/, '[auth]\nenabled = false\n\n');
    } else {
      // Add the auth section
      configContent += '\n[auth]\nenabled = false\n';
    }
    
    // Write the updated configuration
    fs.writeFileSync(configPath, configContent);
    
    console.log('Configuration file updated');
    
    // Apply the configuration
    exec('supabase stop');
    exec('supabase start');
    
    console.log('Supabase configuration updated successfully!');
  } catch (error) {
    console.error('Error updating Supabase configuration:', error);
    process.exit(1);
  }
}

// Run the main function
main();
