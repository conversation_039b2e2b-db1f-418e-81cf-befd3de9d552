#!/bin/bash

# Create service_sunset table using Supabase CLI
echo "Creating service_sunset table using Supabase CLI..."

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Installing..."
    npm install -g supabase
fi

# Create a temporary migration file
MIGRATION_FILE="supabase/migrations/$(date +%Y%m%d%H%M%S)_create_service_sunset.sql"
cp scripts/create-service-sunset-table.sql "$MIGRATION_FILE"

# Apply the migration
echo "Applying migration..."
supabase db push

# Clean up
rm "$MIGRATION_FILE"

echo "Service sunset table creation complete!"
