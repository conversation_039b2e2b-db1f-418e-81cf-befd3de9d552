-- Enable Row Level Security on the service_sunset table
ALTER TABLE public.service_sunset ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own services" ON public.service_sunset;
DROP POLICY IF EXISTS "Users can insert their own services" ON public.service_sunset;
DROP POLICY IF EXISTS "Users can update their own services" ON public.service_sunset;
DROP POLICY IF EXISTS "Users can delete their own services" ON public.service_sunset;
DROP POLICY IF EXISTS "Trustees can view services of deceased users" ON public.service_sunset;

-- Create new policies
CREATE POLICY "Users can view their own services"
ON public.service_sunset
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own services"
ON public.service_sunset
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own services"
ON public.service_sunset
FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own services"
ON public.service_sunset
FOR DELETE
USING (auth.uid() = user_id);

-- Policy for trustees to view services of deceased users
CREATE POLICY "Trustees can view services of deceased users"
ON public.service_sunset
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.trustees
        WHERE trustees.user_id = service_sunset.user_id
        AND trustees.trustee_user_id = auth.uid()
        AND trustees.status = 'active'
        AND EXISTS (
            SELECT 1 FROM public.death_notifications
            WHERE death_notifications.user_id = service_sunset.user_id
            AND death_notifications.status = 'verified'
        )
    )
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS service_sunset_user_id_idx ON public.service_sunset (user_id);
CREATE INDEX IF NOT EXISTS service_sunset_category_idx ON public.service_sunset (category);
CREATE INDEX IF NOT EXISTS service_sunset_priority_idx ON public.service_sunset (priority);
