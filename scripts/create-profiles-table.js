// <PERSON>ript to create the profiles table
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function createProfilesTable() {
  try {
    console.log('Creating profiles table...');
    
    // Create the profiles table
    const { error } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.profiles (
          id UUID PRIMARY KEY,
          first_name TEXT,
          last_name TEXT,
          email TEXT,
          avatar_url TEXT,
          subscription_tier TEXT DEFAULT 'free' NOT NULL,
          subscription_status TEXT DEFAULT 'active' NOT NULL,
          subscription_start_date TIMESTAMP WITH TIME ZONE,
          subscription_end_date TIMESTAMP WITH TIME ZONE,
          stripe_customer_id TEXT,
          stripe_subscription_id TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );
      `
    });
    
    if (error) {
      console.error('Error creating profiles table:', error);
      return;
    }
    
    console.log('Profiles table created successfully!');
    
    // Get the user from custom_users table
    const { data: users, error: userError } = await supabaseAdmin
      .from('custom_users')
      .select('*')
      .eq('email', '<EMAIL>');
    
    if (userError) {
      console.error('Error getting user:', userError);
      return;
    }
    
    if (users.length === 0) {
      console.log('User not found');
      return;
    }
    
    const user = users[0];
    console.log('User found:', user);
    
    // Create a profile for the user
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        subscription_tier: 'free',
        subscription_status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    
    if (profileError) {
      console.error('Error creating profile:', profileError);
      return;
    }
    
    console.log('Profile created successfully!');
  } catch (error) {
    console.error('Error in createProfilesTable:', error);
  }
}

// Run the function
createProfilesTable();
