project_id = "ccwvtcudztphwwzzgwvg"

[storage]
enabled = true
file_size_limit = "50MiB"

[functions.estate-recommendations]
verify_jwt = true

[functions.deliver-time-capsule]
verify_jwt = false

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = []
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10

[auth.mfa]
max_enrolled_factors = 10
[auth.mfa.totp]
enroll_enabled = true
verify_enabled = true
[auth.mfa.phone]
enroll_enabled = false
verify_enabled = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true
secure_password_change = false
max_frequency = "1m0s"
otp_length = 6
otp_expiry = 86400

# Keep the functions configuration
[functions.send-email-hook]
enabled = true
verify_jwt = false
import_map = "./functions/send-email-hook/deno.json"
entrypoint = "./functions/send-email-hook/index.ts"
