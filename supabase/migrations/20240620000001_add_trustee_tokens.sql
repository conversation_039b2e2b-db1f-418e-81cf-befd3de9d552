-- Create trustee_tokens table for magic links
CREATE TABLE IF NOT EXISTS public.trustee_tokens (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    token text NOT NULL,
    trustee_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used boolean DEFAULT false,
    PRIMARY KEY (id),
    FOREIGN KEY (trustee_id) REFERENCES public.trustees(id) ON DELETE CASCADE
);

-- Create index on token for faster lookups
CREATE INDEX IF NOT EXISTS idx_trustee_tokens_token ON public.trustee_tokens(token);

-- Add RLS policies
ALTER TABLE public.trustee_tokens ENABLE ROW LEVEL SECURITY;

-- Create a function to clean up expired tokens
CREATE OR REPLACE FUNCTION public.cleanup_expired_trustee_tokens()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
    DELETE FROM public.trustee_tokens
    WHERE expires_at < NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to clean up expired tokens
CREATE OR REPLACE TRIGGER trigger_cleanup_expired_trustee_tokens
AFTER INSERT ON public.trustee_tokens
FOR EACH STATEMENT
EXECUTE FUNCTION public.cleanup_expired_trustee_tokens();
