-- Add file_type column to time_capsule_media table
ALTER TABLE public.time_capsule_media 
ADD COLUMN IF NOT EXISTS file_type text;

-- Copy data from media_type to file_type if media_type exists
UPDATE public.time_capsule_media
SET file_type = media_type
WHERE file_type IS NULL AND media_type IS NOT NULL;

-- Make file_type NOT NULL
ALTER TABLE public.time_capsule_media 
ALTER COLUMN file_type SET NOT NULL;
