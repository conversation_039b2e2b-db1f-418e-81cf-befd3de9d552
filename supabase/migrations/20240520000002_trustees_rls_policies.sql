-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Users can insert their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Users can update their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Users can delete their own trustees" ON public.trustees;
DROP POLICY IF EXISTS "Combined trustees access policy" ON public.trustees;

-- Create comprehensive RLS policies for trustees table
CREATE POLICY "Users can view their own trustees"
ON public.trustees
FOR SELECT
USING (
    -- Users can view trustees they've created
    user_id = (SELECT auth.uid())
    OR
    -- Trustees can view their trustee relationships
    trustee_user_id = (SELECT auth.uid())
);

CREATE POLICY "Users can insert their own trustees"
ON public.trustees
FOR INSERT
WITH CHECK (
    user_id = (SELECT auth.uid())
);

CREATE POLICY "Users can update their own trustees"
ON public.trustees
FOR UPDATE
USING (
    user_id = (SELECT auth.uid())
);

CREATE POLICY "Users can delete their own trustees"
ON public.trustees
FOR DELETE
USING (
    user_id = (SELECT auth.uid())
);
