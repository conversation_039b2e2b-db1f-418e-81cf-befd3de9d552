-- Create documents table if it doesn't exist
CREATE TABLE IF NOT EXISTS "public"."documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "category" "text" NOT NULL,
    "file_name" "text" NOT NULL,
    "file_type" "text",
    "file_size" integer,
    "encryption_key" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "documents_pkey" PRIMARY KEY ("id")
);

-- Enable RLS on documents table
ALTER TABLE "public"."documents" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for documents table
CREATE POLICY "Users can insert their own documents" 
ON "public"."documents" 
FOR INSERT 
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can select their own documents" 
ON "public"."documents" 
FOR SELECT 
USING (user_id = auth.uid());

CREATE POLICY "Users can update their own documents" 
ON "public"."documents" 
FOR UPDATE 
USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own documents" 
ON "public"."documents" 
FOR DELETE 
USING (user_id = auth.uid());

-- Create storage bucket for documents if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', false)
ON CONFLICT (id) DO NOTHING;

-- Create storage policy to allow users to upload their own documents
CREATE POLICY "Users can upload their own documents"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'documents' AND
  auth.uid() = (storage.foldername(name))[1]::uuid
);

-- Create storage policy to allow users to read their own documents
CREATE POLICY "Users can read their own documents"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'documents' AND
  auth.uid() = (storage.foldername(name))[1]::uuid
);

-- Create storage policy to allow users to update their own documents
CREATE POLICY "Users can update their own documents"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'documents' AND
  auth.uid() = (storage.foldername(name))[1]::uuid
);

-- Create storage policy to allow users to delete their own documents
CREATE POLICY "Users can delete their own documents"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'documents' AND
  auth.uid() = (storage.foldername(name))[1]::uuid
);

-- Grant permissions on documents table
GRANT ALL ON TABLE "public"."documents" TO "anon";
GRANT ALL ON TABLE "public"."documents" TO "authenticated";
GRANT ALL ON TABLE "public"."documents" TO "service_role";
