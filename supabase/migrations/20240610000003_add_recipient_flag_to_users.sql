-- Add is_recipient field to custom_users table if it doesn't exist
ALTER TABLE public.custom_users 
ADD COLUMN IF NOT EXISTS is_recipient BOOLEAN DEFAULT false;

-- Create index on recipient_email for time_capsules table
CREATE INDEX IF NOT EXISTS idx_time_capsules_recipient_email
ON public.time_capsules (recipient_email);

-- Add RLS policy for recipients to view their time capsules
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsules' 
        AND policyname = 'Recipients can view their time capsules'
    ) THEN
        CREATE POLICY "Recipients can view their time capsules"
        ON public.time_capsules
        FOR SELECT
        TO authenticated
        USING (
            recipient_email = auth.email() AND
            status = 'delivered'
        );
    END IF;
END
$$;
