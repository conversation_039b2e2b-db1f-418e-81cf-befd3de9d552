-- <PERSON>QL script to fix RLS performance issues in Supabase database
-- This script addresses two main issues:
-- 1. Auth RLS Initialization Plan issues (replacing auth.uid() with (select auth.uid()))
-- 2. Multiple permissive policies for the same role and action

-- =============================================
-- FIX 1: Auth RLS Initialization Plan Issues
-- =============================================

-- Fix for death_notifications table
DROP POLICY IF EXISTS "Users can view death notifications about them" ON public.death_notifications;
CREATE POLICY "Users can view death notifications about them"
ON public.death_notifications
FOR SELECT
USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS "Trustees can insert death notifications" ON public.death_notifications;
CREATE POLICY "Trustees can insert death notifications"
ON public.death_notifications
FOR INSERT
WITH CHECK (EXISTS (
    SELECT 1 FROM public.trustees
    WHERE trustees.user_id = death_notifications.user_id
    AND trustees.trustee_user_id = (SELECT auth.uid())
    AND trustees.status = 'active'
));

DROP POLICY IF EXISTS "Trustees can view death notifications they've reported" ON public.death_notifications;
CREATE POLICY "Trustees can view death notifications they've reported"
ON public.death_notifications
FOR SELECT
USING (reported_by = (SELECT auth.uid()));

-- Fix for assets table
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON public.assets;
CREATE POLICY "Enable all operations for authenticated users"
ON public.assets
USING (user_id = (SELECT auth.uid()))
WITH CHECK (user_id = (SELECT auth.uid()));

-- =============================================
-- FIX 2: Multiple Permissive Policies Issues
-- =============================================

-- Fix for contacts table
DROP POLICY IF EXISTS "Users can view their own contacts" ON public.contacts;
DROP POLICY IF EXISTS "Trustees can view contacts of deceased users" ON public.contacts;

-- Create a combined policy for SELECT on contacts
CREATE POLICY "Combined contacts access policy"
ON public.contacts
FOR SELECT
USING (
    -- Users can view their own contacts
    user_id = (SELECT auth.uid())
    OR
    -- Trustees can view contacts of deceased users
    EXISTS (
        SELECT 1 FROM public.trustees t
        JOIN public.death_notifications dn ON t.user_id = dn.user_id
        WHERE t.trustee_user_id = (SELECT auth.uid())
        AND dn.status = 'verified'
        AND contacts.user_id = t.user_id
    )
);

-- Fix for death_notifications table
DROP POLICY IF EXISTS "Users can view death notifications about them" ON public.death_notifications;
DROP POLICY IF EXISTS "Trustees can view death notifications they've reported" ON public.death_notifications;

-- Create a combined policy for SELECT on death_notifications
CREATE POLICY "Combined death notifications access policy"
ON public.death_notifications
FOR SELECT
USING (
    -- Users can view death notifications about them
    user_id = (SELECT auth.uid())
    OR
    -- Trustees can view death notifications they've reported
    reported_by = (SELECT auth.uid())
);

-- Fix for last_wishes table
DROP POLICY IF EXISTS "Users can view their own last wishes" ON public.last_wishes;
DROP POLICY IF EXISTS "Trustees can view last wishes of deceased users" ON public.last_wishes;

-- Create a combined policy for SELECT on last_wishes
CREATE POLICY "Combined last wishes access policy"
ON public.last_wishes
FOR SELECT
USING (
    -- Users can view their own last wishes
    user_id = (SELECT auth.uid())
    OR
    -- Trustees can view last wishes of deceased users
    EXISTS (
        SELECT 1 FROM public.trustees t
        JOIN public.death_notifications dn ON t.user_id = dn.user_id
        WHERE t.trustee_user_id = (SELECT auth.uid())
        AND dn.status = 'verified'
        AND last_wishes.user_id = t.user_id
    )
);

-- Fix for service_sunset table
DROP POLICY IF EXISTS "Users can view their own services" ON public.service_sunset;
DROP POLICY IF EXISTS "Trustees can view services of deceased users" ON public.service_sunset;

-- Create a combined policy for SELECT on service_sunset
CREATE POLICY "Combined service sunset access policy"
ON public.service_sunset
FOR SELECT
USING (
    -- Users can view their own services
    user_id = (SELECT auth.uid())
    OR
    -- Trustees can view services of deceased users
    EXISTS (
        SELECT 1 FROM public.trustees t
        JOIN public.death_notifications dn ON t.user_id = dn.user_id
        WHERE t.trustee_user_id = (SELECT auth.uid())
        AND dn.status = 'verified'
        AND service_sunset.user_id = t.user_id
    )
);

-- Fix for trustees table
DROP POLICY IF EXISTS "Users can view trustees they've created" ON public.trustees;
DROP POLICY IF EXISTS "Trustees can view their trustee relationships" ON public.trustees;

-- Create a combined policy for SELECT on trustees
CREATE POLICY "Combined trustees access policy"
ON public.trustees
FOR SELECT
USING (
    -- Users can view trustees they've created
    user_id = (SELECT auth.uid())
    OR
    -- Trustees can view their trustee relationships
    trustee_user_id = (SELECT auth.uid())
);
