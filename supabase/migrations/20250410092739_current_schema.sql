

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."cleanup_expired_sessions"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
  DELETE FROM user_sessions
  WHERE expires_at < NOW();
  RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."cleanup_expired_sessions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."cleanup_expired_verification_codes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
  DELETE FROM verification_codes
  WHERE expires_at < NOW();
  RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."cleanup_expired_verification_codes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
DECLARE
  provider text;
  first_name text;
  last_name text;
  full_name text;
BEGIN
  -- Get the provider
  provider := NEW.raw_app_meta_data->>'provider';

  -- Handle different providers
  IF provider = 'google' THEN
    -- For Google, use full_name or name from user_metadata
    full_name := COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name', '');

    IF full_name <> '' THEN
      -- Split full name into first and last name
      first_name := split_part(full_name, ' ', 1);
      last_name := substr(full_name, length(split_part(full_name, ' ', 1)) + 2);
    ELSE
      -- Fallback to email
      first_name := split_part(NEW.email, '@', 1);
      last_name := '';
    END IF;
  ELSE
    -- For email users, use first_name and last_name from user_metadata
    first_name := COALESCE(NEW.raw_user_meta_data->>'first_name', split_part(NEW.email, '@', 1));
    last_name := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
  END IF;

  -- Insert the profile
  INSERT INTO public.profiles (id, first_name, last_name, email, created_at, updated_at)
  VALUES (
    NEW.id,
    first_name,
    last_name,
    NEW.email,
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_assets_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_assets_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_contacts_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_contacts_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_custom_users_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_custom_users_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_death_notifications_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_death_notifications_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_last_wishes_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_last_wishes_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_time_capsules_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_time_capsules_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_trustees_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_trustees_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_will_progress_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_will_progress_updated_at"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."assets" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "category" "text" NOT NULL,
    "description" "text",
    "value" numeric(15,2),
    "location" "text",
    "account_details" "text",
    "currency" "text" DEFAULT 'USD'::"text",
    "acquisition_date" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "assets_new_type_check" CHECK (("type" = ANY (ARRAY['digital'::"text", 'physical'::"text"]))),
    CONSTRAINT "assets_type_check" CHECK (("type" = 'physical'::"text"))
);


ALTER TABLE "public"."assets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."contacts" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "email" "text",
    "phone" "text",
    "relationship" "text",
    "address" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "personal_message" "text",
    "has_personal_message" boolean DEFAULT false,
    "country_code" "text"
);


ALTER TABLE "public"."contacts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."custom_users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" "text" NOT NULL,
    "password_hash" "text" NOT NULL,
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "email_verified" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."custom_users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."death_notifications" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "reported_by" "uuid" NOT NULL,
    "status" "text" NOT NULL,
    "verification_method" "text",
    "verification_details" "text",
    "death_certificate" "text",
    "reported_at" timestamp with time zone NOT NULL,
    "verified_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "death_notifications_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'verified'::"text", 'rejected'::"text"])))
);


ALTER TABLE "public"."death_notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."last_wishes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "funeral_wishes" "text",
    "burial_wishes" "text",
    "personal_messages" "text",
    "is_organ_donor" boolean DEFAULT false NOT NULL,
    "organ_donor_state" character varying(2),
    "has_pets" boolean DEFAULT false NOT NULL,
    "pet_care_instructions" "text",
    "other_wishes" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "burial_option" "text",
    "show_personal_messages" boolean DEFAULT false,
    "organ_donor_country" "text" DEFAULT 'USA'::"text"
);


ALTER TABLE "public"."last_wishes" OWNER TO "postgres";


COMMENT ON COLUMN "public"."last_wishes"."burial_option" IS 'Selected burial option (burial, cremation, etc.)';



COMMENT ON COLUMN "public"."last_wishes"."show_personal_messages" IS 'Whether to show personal messages section';



COMMENT ON COLUMN "public"."last_wishes"."organ_donor_country" IS 'Country where the user is registered as an organ donor';



CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "first_name" "text",
    "last_name" "text",
    "email" "text",
    "avatar_url" "text",
    "subscription_tier" "text" DEFAULT 'free'::"text" NOT NULL,
    "subscription_status" "text" DEFAULT 'active'::"text" NOT NULL,
    "subscription_start_date" timestamp with time zone,
    "subscription_end_date" timestamp with time zone,
    "stripe_customer_id" "text",
    "stripe_subscription_id" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service_sunset" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "category" "text" NOT NULL,
    "description" "text",
    "account_number" "text",
    "contact_info" "text",
    "cancellation_instructions" "text",
    "auto_renewal" boolean DEFAULT false NOT NULL,
    "renewal_date" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."service_sunset" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."time_capsules" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "recipient_email" "text" NOT NULL,
    "delivery_date" timestamp with time zone NOT NULL,
    "status" "text" NOT NULL,
    "message" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "time_capsules_status_check" CHECK (("status" = ANY (ARRAY['draft'::"text", 'scheduled'::"text", 'delivered'::"text"])))
);


ALTER TABLE "public"."time_capsules" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."trustees" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "trustee_email" "text" NOT NULL,
    "trustee_user_id" "uuid",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "relationship" "text" NOT NULL,
    "status" "text" NOT NULL,
    "permissions" "text"[] NOT NULL,
    "invitation_sent_at" timestamp with time zone NOT NULL,
    "invitation_accepted_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "trustees_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'active'::"text", 'revoked'::"text"])))
);


ALTER TABLE "public"."trustees" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_sessions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "session_token" "text" NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_sessions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."vault_documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "category" "text" NOT NULL,
    "file_path" "text" NOT NULL,
    "file_type" "text",
    "file_size" integer,
    "is_encrypted" boolean DEFAULT true NOT NULL,
    "encryption_key" "text",
    "file_name" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vault_documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."verification_codes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" "text" NOT NULL,
    "code" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone NOT NULL,
    "used" boolean DEFAULT false
);


ALTER TABLE "public"."verification_codes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."will_progress" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "progress_percentage" integer DEFAULT 0 NOT NULL,
    "basic_info_completed" boolean DEFAULT false NOT NULL,
    "assets_completed" boolean DEFAULT false NOT NULL,
    "executor_completed" boolean DEFAULT false NOT NULL,
    "final_review_completed" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."will_progress" OWNER TO "postgres";


ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_new_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."contacts"
    ADD CONSTRAINT "contacts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."custom_users"
    ADD CONSTRAINT "custom_users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."custom_users"
    ADD CONSTRAINT "custom_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."death_notifications"
    ADD CONSTRAINT "death_notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."last_wishes"
    ADD CONSTRAINT "last_wishes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_sunset"
    ADD CONSTRAINT "service_sunset_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."time_capsules"
    ADD CONSTRAINT "time_capsules_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."trustees"
    ADD CONSTRAINT "trustees_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."trustees"
    ADD CONSTRAINT "trustees_user_id_trustee_email_key" UNIQUE ("user_id", "trustee_email");



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_session_token_key" UNIQUE ("session_token");



ALTER TABLE ONLY "public"."vault_documents"
    ADD CONSTRAINT "vault_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."verification_codes"
    ADD CONSTRAINT "verification_codes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."will_progress"
    ADD CONSTRAINT "will_progress_pkey" PRIMARY KEY ("id");



CREATE INDEX "contacts_email_idx" ON "public"."contacts" USING "btree" ("email");



CREATE INDEX "contacts_first_name_idx" ON "public"."contacts" USING "btree" ("first_name");



CREATE INDEX "contacts_last_name_idx" ON "public"."contacts" USING "btree" ("last_name");



CREATE INDEX "contacts_user_id_idx" ON "public"."contacts" USING "btree" ("user_id");



CREATE INDEX "custom_users_email_idx" ON "public"."custom_users" USING "btree" ("email");



CREATE INDEX "death_notifications_reported_by_idx" ON "public"."death_notifications" USING "btree" ("reported_by");



CREATE INDEX "death_notifications_user_id_idx" ON "public"."death_notifications" USING "btree" ("user_id");



CREATE INDEX "last_wishes_user_id_idx" ON "public"."last_wishes" USING "btree" ("user_id");



CREATE INDEX "service_sunset_category_idx" ON "public"."service_sunset" USING "btree" ("category");



CREATE INDEX "service_sunset_user_id_idx" ON "public"."service_sunset" USING "btree" ("user_id");



CREATE INDEX "time_capsules_user_id_idx" ON "public"."time_capsules" USING "btree" ("user_id");



CREATE INDEX "trustees_trustee_user_id_idx" ON "public"."trustees" USING "btree" ("trustee_user_id");



CREATE INDEX "trustees_user_id_idx" ON "public"."trustees" USING "btree" ("user_id");



CREATE INDEX "user_sessions_token_idx" ON "public"."user_sessions" USING "btree" ("session_token");



CREATE INDEX "verification_codes_email_idx" ON "public"."verification_codes" USING "btree" ("email");



CREATE INDEX "will_progress_user_id_idx" ON "public"."will_progress" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "trigger_cleanup_expired_sessions" AFTER INSERT ON "public"."user_sessions" FOR EACH STATEMENT EXECUTE FUNCTION "public"."cleanup_expired_sessions"();



CREATE OR REPLACE TRIGGER "trigger_cleanup_expired_verification_codes" AFTER INSERT ON "public"."verification_codes" FOR EACH STATEMENT EXECUTE FUNCTION "public"."cleanup_expired_verification_codes"();



CREATE OR REPLACE TRIGGER "update_contacts_updated_at" BEFORE UPDATE ON "public"."contacts" FOR EACH ROW EXECUTE FUNCTION "public"."update_contacts_updated_at"();



CREATE OR REPLACE TRIGGER "update_custom_users_updated_at" BEFORE UPDATE ON "public"."custom_users" FOR EACH ROW EXECUTE FUNCTION "public"."update_custom_users_updated_at"();



CREATE OR REPLACE TRIGGER "update_death_notifications_updated_at" BEFORE UPDATE ON "public"."death_notifications" FOR EACH ROW EXECUTE FUNCTION "public"."update_death_notifications_updated_at"();



CREATE OR REPLACE TRIGGER "update_last_wishes_updated_at" BEFORE UPDATE ON "public"."last_wishes" FOR EACH ROW EXECUTE FUNCTION "public"."update_last_wishes_updated_at"();



CREATE OR REPLACE TRIGGER "update_time_capsules_updated_at" BEFORE UPDATE ON "public"."time_capsules" FOR EACH ROW EXECUTE FUNCTION "public"."update_time_capsules_updated_at"();



CREATE OR REPLACE TRIGGER "update_trustees_updated_at" BEFORE UPDATE ON "public"."trustees" FOR EACH ROW EXECUTE FUNCTION "public"."update_trustees_updated_at"();



CREATE OR REPLACE TRIGGER "update_will_progress_updated_at" BEFORE UPDATE ON "public"."will_progress" FOR EACH ROW EXECUTE FUNCTION "public"."update_will_progress_updated_at"();



ALTER TABLE ONLY "public"."contacts"
    ADD CONSTRAINT "contacts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."death_notifications"
    ADD CONSTRAINT "death_notifications_reported_by_fkey" FOREIGN KEY ("reported_by") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."death_notifications"
    ADD CONSTRAINT "death_notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."last_wishes"
    ADD CONSTRAINT "last_wishes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."custom_users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."time_capsules"
    ADD CONSTRAINT "time_capsules_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."trustees"
    ADD CONSTRAINT "trustees_trustee_user_id_fkey" FOREIGN KEY ("trustee_user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."trustees"
    ADD CONSTRAINT "trustees_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_sessions"
    ADD CONSTRAINT "user_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."custom_users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."will_progress"
    ADD CONSTRAINT "will_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Enable all operations for authenticated users" ON "public"."assets" USING (("user_id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Trustees can insert death notifications" ON "public"."death_notifications" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."trustees"
  WHERE (("trustees"."user_id" = "death_notifications"."user_id") AND ("trustees"."trustee_user_id" = "auth"."uid"()) AND ("trustees"."status" = 'active'::"text")))));



CREATE POLICY "Trustees can view contacts of deceased users" ON "public"."contacts" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM ("public"."trustees" "t"
     JOIN "public"."death_notifications" "dn" ON (("t"."user_id" = "dn"."user_id")))
  WHERE (("t"."trustee_user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("dn"."status" = 'verified'::"text") AND ("contacts"."user_id" = "t"."user_id")))));



CREATE POLICY "Trustees can view death notifications they've reported" ON "public"."death_notifications" FOR SELECT USING (("auth"."uid"() = "reported_by"));



CREATE POLICY "Trustees can view last wishes of deceased users" ON "public"."last_wishes" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM ("public"."trustees" "t"
     JOIN "public"."death_notifications" "dn" ON (("t"."user_id" = "dn"."user_id")))
  WHERE (("t"."trustee_user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("dn"."status" = 'verified'::"text") AND ("last_wishes"."user_id" = "t"."user_id")))));



CREATE POLICY "Trustees can view services of deceased users" ON "public"."service_sunset" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM ("public"."trustees" "t"
     JOIN "public"."death_notifications" "dn" ON (("t"."user_id" = "dn"."user_id")))
  WHERE (("t"."trustee_user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("dn"."status" = 'verified'::"text") AND ("service_sunset"."user_id" = "t"."user_id")))));



CREATE POLICY "Trustees can view their trustee relationships" ON "public"."trustees" FOR SELECT USING (("trustee_user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete their own contacts" ON "public"."contacts" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete their own last wishes" ON "public"."last_wishes" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete their own services" ON "public"."service_sunset" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete their own time capsules" ON "public"."time_capsules" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete their own vault documents" ON "public"."vault_documents" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete their own will progress" ON "public"."will_progress" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can delete trustees they've created" ON "public"."trustees" FOR DELETE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own contacts" ON "public"."contacts" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own last wishes" ON "public"."last_wishes" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own services" ON "public"."service_sunset" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own time capsules" ON "public"."time_capsules" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own vault documents" ON "public"."vault_documents" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert their own will progress" ON "public"."will_progress" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can insert trustees" ON "public"."trustees" FOR INSERT WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can manage their own profiles" ON "public"."profiles" USING (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can only access their own sessions" ON "public"."user_sessions" USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can only access their own user data" ON "public"."custom_users" USING (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can only access their own verification codes" ON "public"."verification_codes" USING (("email" = ( SELECT "auth"."email"() AS "email")));



CREATE POLICY "Users can update their own contacts" ON "public"."contacts" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own last wishes" ON "public"."last_wishes" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own services" ON "public"."service_sunset" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own time capsules" ON "public"."time_capsules" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own vault documents" ON "public"."vault_documents" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own will progress" ON "public"."will_progress" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update trustees they've created" ON "public"."trustees" FOR UPDATE USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view death notifications about them" ON "public"."death_notifications" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own contacts" ON "public"."contacts" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own last wishes" ON "public"."last_wishes" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own services" ON "public"."service_sunset" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own time capsules" ON "public"."time_capsules" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own vault documents" ON "public"."vault_documents" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own will progress" ON "public"."will_progress" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view trustees they've created" ON "public"."trustees" FOR SELECT USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



ALTER TABLE "public"."assets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."contacts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."custom_users" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."death_notifications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."last_wishes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_sunset" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."time_capsules" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."trustees" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."vault_documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."verification_codes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."will_progress" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."cleanup_expired_sessions"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_expired_sessions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_expired_sessions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_expired_verification_codes"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_expired_verification_codes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_expired_verification_codes"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_assets_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_assets_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_assets_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_contacts_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_contacts_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_contacts_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_custom_users_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_custom_users_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_custom_users_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_death_notifications_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_death_notifications_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_death_notifications_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_last_wishes_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_last_wishes_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_last_wishes_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_time_capsules_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_time_capsules_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_time_capsules_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_trustees_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_trustees_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_trustees_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_will_progress_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_will_progress_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_will_progress_updated_at"() TO "service_role";


















GRANT ALL ON TABLE "public"."assets" TO "anon";
GRANT ALL ON TABLE "public"."assets" TO "authenticated";
GRANT ALL ON TABLE "public"."assets" TO "service_role";



GRANT ALL ON TABLE "public"."contacts" TO "anon";
GRANT ALL ON TABLE "public"."contacts" TO "authenticated";
GRANT ALL ON TABLE "public"."contacts" TO "service_role";



GRANT ALL ON TABLE "public"."custom_users" TO "anon";
GRANT ALL ON TABLE "public"."custom_users" TO "authenticated";
GRANT ALL ON TABLE "public"."custom_users" TO "service_role";



GRANT ALL ON TABLE "public"."death_notifications" TO "anon";
GRANT ALL ON TABLE "public"."death_notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."death_notifications" TO "service_role";



GRANT ALL ON TABLE "public"."last_wishes" TO "anon";
GRANT ALL ON TABLE "public"."last_wishes" TO "authenticated";
GRANT ALL ON TABLE "public"."last_wishes" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."service_sunset" TO "anon";
GRANT ALL ON TABLE "public"."service_sunset" TO "authenticated";
GRANT ALL ON TABLE "public"."service_sunset" TO "service_role";



GRANT ALL ON TABLE "public"."time_capsules" TO "anon";
GRANT ALL ON TABLE "public"."time_capsules" TO "authenticated";
GRANT ALL ON TABLE "public"."time_capsules" TO "service_role";



GRANT ALL ON TABLE "public"."trustees" TO "anon";
GRANT ALL ON TABLE "public"."trustees" TO "authenticated";
GRANT ALL ON TABLE "public"."trustees" TO "service_role";



GRANT ALL ON TABLE "public"."user_sessions" TO "anon";
GRANT ALL ON TABLE "public"."user_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."vault_documents" TO "anon";
GRANT ALL ON TABLE "public"."vault_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."vault_documents" TO "service_role";



GRANT ALL ON TABLE "public"."verification_codes" TO "anon";
GRANT ALL ON TABLE "public"."verification_codes" TO "authenticated";
GRANT ALL ON TABLE "public"."verification_codes" TO "service_role";



GRANT ALL ON TABLE "public"."will_progress" TO "anon";
GRANT ALL ON TABLE "public"."will_progress" TO "authenticated";
GRANT ALL ON TABLE "public"."will_progress" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
