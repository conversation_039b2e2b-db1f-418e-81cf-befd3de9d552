-- Create a mapping table between custom_users and auth.users
CREATE TABLE IF NOT EXISTS public.auth_users_mapping (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    custom_user_id uuid NOT NULL,
    auth_user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    PRIMARY KEY (id),
    UNIQUE (custom_user_id),
    UNIQUE (auth_user_id),
    FOREIGN KEY (custom_user_id) REFERENCES public.custom_users(id) ON DELETE CASCADE,
    FOREIGN KEY (auth_user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS policies
ALTER TABLE public.auth_users_mapping ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own mapping"
ON public.auth_users_mapping
FOR SELECT
USING (
    custom_user_id = (SELECT auth.uid())
);

-- Insert a mapping for the current user
INSERT INTO public.auth_users_mapping (custom_user_id, auth_user_id)
SELECT 
    cu.id as custom_user_id,
    au.id as auth_user_id
FROM 
    public.custom_users cu
JOIN 
    auth.users au ON cu.email = au.email
WHERE 
    NOT EXISTS (
        SELECT 1 FROM public.auth_users_mapping m 
        WHERE m.custom_user_id = cu.id OR m.auth_user_id = au.id
    );
