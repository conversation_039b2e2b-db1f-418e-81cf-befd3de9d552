-- Create a storage bucket for time capsule media
INSERT INTO storage.buckets (id, name, public, avif_autodetection)
VALUES ('time_capsule_media', 'time_capsule_media', false, false)
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for the time_capsule_media bucket
CREATE POLICY "Users can upload their own time capsule media"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can view their own time capsule media"
ON storage.objects
FOR SELECT
TO authenticated
USING (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can update their own time capsule media"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can delete their own time capsule media"
ON storage.objects
FOR DELETE
TO authenticated
USING (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

-- Create a table to store time capsule media metadata
CREATE TABLE IF NOT EXISTS public.time_capsule_media (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    capsule_id uuid NOT NULL REFERENCES public.time_capsules(id) ON DELETE CASCADE,
    file_path text NOT NULL,
    file_name text NOT NULL,
    file_type text NOT NULL,
    file_size integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Add RLS policies for time_capsule_media table
ALTER TABLE public.time_capsule_media ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own time capsule media metadata"
ON public.time_capsule_media
FOR SELECT
TO authenticated
USING (
    capsule_id IN (
        SELECT id FROM public.time_capsules
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can insert their own time capsule media metadata"
ON public.time_capsule_media
FOR INSERT
TO authenticated
WITH CHECK (
    capsule_id IN (
        SELECT id FROM public.time_capsules
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can update their own time capsule media metadata"
ON public.time_capsule_media
FOR UPDATE
TO authenticated
USING (
    capsule_id IN (
        SELECT id FROM public.time_capsules
        WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can delete their own time capsule media metadata"
ON public.time_capsule_media
FOR DELETE
TO authenticated
USING (
    capsule_id IN (
        SELECT id FROM public.time_capsules
        WHERE user_id = auth.uid()
    )
);
