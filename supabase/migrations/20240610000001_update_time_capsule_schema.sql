-- Add delivery_hour field to time_capsules table if it doesn't exist
ALTER TABLE public.time_capsules 
ADD COLUMN IF NOT EXISTS delivery_hour INTEGER DEFAULT 12;

-- Add access_code field if it doesn't exist
ALTER TABLE public.time_capsules 
ADD COLUMN IF NOT EXISTS access_code TEXT;

-- Add sender_name field if it doesn't exist
ALTER TABLE public.time_capsules 
ADD COLUMN IF NOT EXISTS sender_name TEXT;

-- Ensure the status check constraint includes all possible statuses
ALTER TABLE public.time_capsules 
DROP CONSTRAINT IF EXISTS time_capsules_status_check;

ALTER TABLE public.time_capsules 
ADD CONSTRAINT time_capsules_status_check 
CHECK (status IN ('draft', 'scheduled', 'delivered', 'cancelled'));

-- Add RLS policies for time_capsules if they don't exist
ALTER TABLE public.time_capsules ENABLE ROW LEVEL SECURITY;

-- Users can view their own time capsules
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsules' 
        AND policyname = 'Users can view their own time capsules'
    ) THEN
        CREATE POLICY "Users can view their own time capsules"
        ON public.time_capsules
        FOR SELECT
        TO authenticated
        USING (user_id = auth.uid());
    END IF;
END
$$;

-- Users can insert their own time capsules
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsules' 
        AND policyname = 'Users can insert their own time capsules'
    ) THEN
        CREATE POLICY "Users can insert their own time capsules"
        ON public.time_capsules
        FOR INSERT
        TO authenticated
        WITH CHECK (user_id = auth.uid());
    END IF;
END
$$;

-- Users can update their own time capsules
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsules' 
        AND policyname = 'Users can update their own time capsules'
    ) THEN
        CREATE POLICY "Users can update their own time capsules"
        ON public.time_capsules
        FOR UPDATE
        TO authenticated
        USING (user_id = auth.uid())
        WITH CHECK (user_id = auth.uid());
    END IF;
END
$$;

-- Users can delete their own time capsules
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsules' 
        AND policyname = 'Users can delete their own time capsules'
    ) THEN
        CREATE POLICY "Users can delete their own time capsules"
        ON public.time_capsules
        FOR DELETE
        TO authenticated
        USING (user_id = auth.uid());
    END IF;
END
$$;

-- Ensure time_capsule_media table exists and has proper structure
CREATE TABLE IF NOT EXISTS public.time_capsule_media (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    capsule_id uuid NOT NULL REFERENCES public.time_capsules(id) ON DELETE CASCADE,
    media_type TEXT,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_type TEXT,
    file_size INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies for time_capsule_media
ALTER TABLE public.time_capsule_media ENABLE ROW LEVEL SECURITY;

-- Users can view their own time capsule media
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsule_media' 
        AND policyname = 'Users can view their own time capsule media'
    ) THEN
        CREATE POLICY "Users can view their own time capsule media"
        ON public.time_capsule_media
        FOR SELECT
        TO authenticated
        USING (
            capsule_id IN (
                SELECT id FROM public.time_capsules
                WHERE user_id = auth.uid()
            )
        );
    END IF;
END
$$;

-- Users can insert their own time capsule media
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsule_media' 
        AND policyname = 'Users can insert their own time capsule media'
    ) THEN
        CREATE POLICY "Users can insert their own time capsule media"
        ON public.time_capsule_media
        FOR INSERT
        TO authenticated
        WITH CHECK (
            capsule_id IN (
                SELECT id FROM public.time_capsules
                WHERE user_id = auth.uid()
            )
        );
    END IF;
END
$$;

-- Users can delete their own time capsule media
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'time_capsule_media' 
        AND policyname = 'Users can delete their own time capsule media'
    ) THEN
        CREATE POLICY "Users can delete their own time capsule media"
        ON public.time_capsule_media
        FOR DELETE
        TO authenticated
        USING (
            capsule_id IN (
                SELECT id FROM public.time_capsules
                WHERE user_id = auth.uid()
            )
        );
    END IF;
END
$$;

-- Create a storage bucket for time capsule media if it doesn't exist
INSERT INTO storage.buckets (id, name, public, avif_autodetection)
VALUES ('time_capsule_media', 'time_capsule_media', false, false)
ON CONFLICT (id) DO NOTHING;

-- Add storage policies for time_capsule_media bucket
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'objects' 
        AND schemaname = 'storage'
        AND policyname = 'Users can upload their own time capsule media'
    ) THEN
        CREATE POLICY "Users can upload their own time capsule media"
        ON storage.objects
        FOR INSERT
        TO authenticated
        WITH CHECK (
            bucket_id = 'time_capsule_media' AND
            (storage.foldername(name))[1] = auth.uid()::text
        );
    END IF;
END
$$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'objects' 
        AND schemaname = 'storage'
        AND policyname = 'Users can view their own time capsule media'
    ) THEN
        CREATE POLICY "Users can view their own time capsule media"
        ON storage.objects
        FOR SELECT
        TO authenticated
        USING (
            bucket_id = 'time_capsule_media' AND
            (storage.foldername(name))[1] = auth.uid()::text
        );
    END IF;
END
$$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'objects' 
        AND schemaname = 'storage'
        AND policyname = 'Users can delete their own time capsule media'
    ) THEN
        CREATE POLICY "Users can delete their own time capsule media"
        ON storage.objects
        FOR DELETE
        TO authenticated
        USING (
            bucket_id = 'time_capsule_media' AND
            (storage.foldername(name))[1] = auth.uid()::text
        );
    END IF;
END
$$;
