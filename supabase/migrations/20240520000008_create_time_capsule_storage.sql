-- Create a storage bucket for time capsule media
INSERT INTO storage.buckets (id, name, public, avif_autodetection)
VALUES ('time_capsule_media', 'time_capsule_media', false, false)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can upload their own time capsule media" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own time capsule media" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own time capsule media" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own time capsule media" ON storage.objects;

-- Create RLS policies for the time_capsule_media bucket
CREATE POLICY "Users can upload their own time capsule media"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can view their own time capsule media"
ON storage.objects
FOR SELECT
TO authenticated
USING (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can update their own time capsule media"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can delete their own time capsule media"
ON storage.objects
FOR DELETE
TO authenticated
USING (
    bucket_id = 'time_capsule_media' AND
    (storage.foldername(name))[1] = auth.uid()::text
);
