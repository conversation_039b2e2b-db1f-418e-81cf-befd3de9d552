// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { Webhook } from "https://esm.sh/standardwebhooks@1.0.0"
import { Resend } from "npm:resend@4.0.0"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Initialize Resend with API key
const resend = new Resend(Deno.env.get("RESEND_API_KEY") as string)

// Get the webhook secret from environment variables
// This is set when configuring the auth hook in Supabase dashboard
const hookSecret = Deno.env.get("SEND_EMAIL_HOOK_SECRET") as string

// Email templates for different action types
function getEmailTemplate(actionType: string, email: string, token: string): { subject: string; html: string } {
  const baseTemplate = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://legalock.com/logo.png" alt="Legalock Logo" style="height: 60px; margin-bottom: 16px;" />
  `

  const footer = `
      <p style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">
        If you didn't request this code, you can safely ignore this email.
      </p>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;" />

      <p style="font-size: 14px; color: #6b7280; text-align: center; margin: 0;">
        &copy; ${new Date().getFullYear()} Legalock. All rights reserved.<br>
        <a href="https://legalock.com" style="color: #4f46e5; text-decoration: none;">legalock.com</a>
      </p>
    </div>
  `

  switch (actionType) {
    case "signup":
      return {
        subject: "Verify your Legalock account",
        html: `${baseTemplate}
          <h1 style="color: #4f46e5; margin: 0;">Verify Your Legalock Account</h1>
        </div>

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
          Thank you for signing up with Legalock. To complete your registration and secure your digital legacy, please enter the 6-digit verification code below.
        </p>

        <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
          <p style="font-size: 16px; margin-bottom: 16px;">
            Your verification code:
          </p>
          <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
            ${token}
          </p>
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            This code will expire in 24 hours.
          </p>
        </div>

        <div style="margin: 32px 0; text-align: center;">
          <p style="font-size: 16px; margin-bottom: 16px;">
            After verification, you'll have access to all Legalock features:
          </p>
          <ul style="text-align: left; padding-left: 20px; margin-bottom: 24px;">
            <li style="margin-bottom: 8px;">Create a comprehensive inventory of your digital assets</li>
            <li style="margin-bottom: 8px;">Securely store sensitive documents in your Digital Vault</li>
            <li style="margin-bottom: 8px;">Designate trustees to manage your digital legacy</li>
            <li style="margin-bottom: 8px;">Schedule future messages with Time Capsule</li>
            <li style="margin-bottom: 0;">Document your last wishes and preferences</li>
          </ul>
        </div>
        ${footer}`
      }
    case "recovery":
      return {
        subject: "Reset your Legalock password",
        html: `${baseTemplate}
          <h1 style="color: #4f46e5; margin: 0;">Reset Your Password</h1>
        </div>

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
          We received a request to reset your password for your Legalock account. Please enter the 6-digit verification code below to continue.
        </p>

        <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
          <p style="font-size: 16px; margin-bottom: 16px;">
            Your verification code:
          </p>
          <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
            ${token}
          </p>
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            This code will expire in 24 hours.
          </p>
        </div>
        ${footer}`
      }
    case "email_change":
      return {
        subject: "Verify your new email address",
        html: `${baseTemplate}
          <h1 style="color: #4f46e5; margin: 0;">Verify Your New Email</h1>
        </div>

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
          We received a request to change the email address associated with your Legalock account. Please enter the 6-digit verification code below to confirm this change.
        </p>

        <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
          <p style="font-size: 16px; margin-bottom: 16px;">
            Your verification code:
          </p>
          <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
            ${token}
          </p>
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            This code will expire in 24 hours.
          </p>
        </div>
        ${footer}`
      }
    default:
      return {
        subject: "Verification code for Legalock",
        html: `${baseTemplate}
          <h1 style="color: #4f46e5; margin: 0;">Your Verification Code</h1>
        </div>

        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 24px;">
          Please enter the 6-digit verification code below to continue.
        </p>

        <div style="background-color: #f3f4f6; padding: 24px; border-radius: 8px; text-align: center; margin: 24px 0;">
          <p style="font-size: 16px; margin-bottom: 16px;">
            Your verification code:
          </p>
          <p style="font-size: 32px; font-weight: bold; letter-spacing: 4px; margin-bottom: 16px; color: #4f46e5;">
            ${token}
          </p>
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            This code will expire in 24 hours.
          </p>
        </div>
        ${footer}`
      }
  }
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders })
  }

  // Only accept POST requests
  if (req.method !== "POST") {
    return new Response("Method not allowed", {
      status: 405,
      headers: corsHeaders
    })
  }

  try {
    // Get the payload and headers
    const payload = await req.text()
    const headers = Object.fromEntries(req.headers)

    // Verify the webhook signature
    const wh = new Webhook(hookSecret)

    // Parse and verify the webhook payload
    const { user, email_data } = wh.verify(payload, headers) as {
      user: {
        email: string;
        app_metadata?: Record<string, any>;
        user_metadata?: Record<string, any>;
      };
      email_data: {
        token: string;
        token_hash: string;
        redirect_to: string;
        email_action_type: string;
        site_url: string;
        token_new?: string;
        token_hash_new?: string;
      };
    }

    console.log(`Processing ${email_data.email_action_type} email for ${user.email}`)

    // Get the appropriate email template based on the action type
    const { subject, html } = getEmailTemplate(
      email_data.email_action_type,
      user.email,
      email_data.token
    )

    // Send the email using Resend
    const { data, error } = await resend.emails.send({
      from: "Legalock <<EMAIL>>",
      to: [user.email],
      subject: subject,
      html: html,
    })

    if (error) {
      console.error("Error sending email:", error)
      throw error
    }

    console.log(`Email sent successfully to ${user.email}`)

    // Return an empty response with 200 status code
    return new Response(JSON.stringify({}), {
      status: 200,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    })
  } catch (error) {
    console.error("Error in send-email-hook:", error)

    // Return an error response
    return new Response(
      JSON.stringify({
        error: {
          http_code: error.code || 500,
          message: error.message || "An unexpected error occurred",
        },
      }),
      {
        status: error.code || 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    )
  }
})
