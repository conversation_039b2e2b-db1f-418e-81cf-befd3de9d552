
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "https://ccwvtcudztphwwzzgwvg.supabase.co";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Resend for email delivery
const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

interface TimeCapsule {
  id: string;
  user_id: string;
  title: string;
  message: string | null;
  recipient_name?: string;
  recipient_first_name?: string;
  recipient_last_name?: string;
  recipient_email: string;
  delivery_date: string;
  delivery_hour?: number;
  status: string;
  access_code: string;
  created_at: string;
  updated_at: string;
  sender_name?: string | null;
}

interface TimeCapsuleMedia {
  id: string;
  capsule_id: string;
  media_type: string;
  file_path: string;
  file_name: string;
  file_size: number;
  created_at: string;
}

async function deliverTimeCapsule(timeCapsule: TimeCapsule, media: TimeCapsuleMedia[]) {
  try {
    // Generate a secure access URL with the capsule ID and access code
    const accessUrl = `${Deno.env.get("PUBLIC_SITE_URL") || "https://legalock.com"}/time-capsule/view/${timeCapsule.id}?code=${timeCapsule.access_code}`;

    // Count attachments
    const attachmentCount = media.length;

    // Send the email
    const { data, error } = await resend.emails.send({
      from: "Time Capsule <<EMAIL>>",
      to: [timeCapsule.recipient_email],
      subject: `Time Capsule: ${timeCapsule.title}`,
      html: `
        <h1>A Time Capsule Has Been Delivered To You</h1>
        <p>Hello ${timeCapsule.recipient_first_name} ${timeCapsule.recipient_last_name || ''},</p>
        <p>${timeCapsule.sender_name || 'Someone'} has sent you a special time capsule message that was scheduled to be delivered today.</p>
        <p><strong>Time Capsule:</strong> ${timeCapsule.title}</p>
        <p><strong>Contains:</strong> A message${attachmentCount > 0 ? ` and ${attachmentCount} attachment(s)` : ''}</p>
        <p>To view your time capsule, please click the button below:</p>
        <div style="text-align: center; margin: 20px 0;">
          <a href="${accessUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4F46E5; color: white; text-decoration: none; border-radius: 5px;">View Time Capsule</a>
        </div>

        <p>For enhanced security and to access all your time capsules in one place, we recommend creating a recipient account:</p>
        <div style="text-align: center; margin: 20px 0;">
          <a href="https://legalock.com/time-capsule/recipient/register" style="display: inline-block; padding: 10px 20px; background-color: #64748B; color: white; text-decoration: none; border-radius: 5px;">Create Recipient Account</a>
        </div>

        <p>Or copy and paste this link into your browser:</p>
        <p>${accessUrl}</p>
        <p>The link is secured with a unique access code that was set when the time capsule was created.</p>
        <p>This time capsule will be available for viewing for the next 90 days.</p>
        <p>Best regards,<br>Legalock Time Capsule Service</p>
      `,
    });

    if (error) {
      console.error(`Failed to send email for time capsule ${timeCapsule.id}:`, error);
      return { success: false, error };
    }

    console.log(`Successfully delivered time capsule ${timeCapsule.id} to ${timeCapsule.recipient_email}`);
    return { success: true, data };
  } catch (error) {
    console.error(`Error in deliverTimeCapsule for capsule ${timeCapsule.id}:`, error);
    return { success: false, error };
  }
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Running deliver-time-capsule function");

    // Get current hour
    const now = new Date();
    const currentHour = now.getHours();

    console.log(`Current hour: ${currentHour}`);

    // Get time capsules that are due for delivery
    const { data: timeCapsules, error: capsuleError } = await supabase
      .from("time_capsules")
      .select("*")
      .eq("status", "scheduled")
      .lte("delivery_date", now.toISOString())
      .eq("delivery_hour", currentHour);

    if (capsuleError) {
      console.error("Error fetching due time capsules:", capsuleError);
      return new Response(
        JSON.stringify({ error: "Failed to fetch due time capsules" }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    console.log(`Found ${timeCapsules.length} time capsules ready for delivery`);

    const deliveryResults = [];

    // Process each time capsule
    for (const capsule of timeCapsules) {
      // Get media attachments for this capsule
      const { data: media, error: mediaError } = await supabase
        .from("time_capsule_media")
        .select("*")
        .eq("capsule_id", capsule.id);

      if (mediaError) {
        console.error(`Error fetching media for time capsule ${capsule.id}:`, mediaError);
        deliveryResults.push({
          capsule_id: capsule.id,
          success: false,
          error: "Failed to fetch media attachments"
        });
        continue;
      }

      // Deliver the time capsule
      const deliveryResult = await deliverTimeCapsule(capsule, media);

      // Update the status to delivered
      if (deliveryResult.success) {
        const { error: updateError } = await supabase
          .from("time_capsules")
          .update({ status: "delivered", updated_at: new Date().toISOString() })
          .eq("id", capsule.id);

        if (updateError) {
          console.error(`Error updating status for time capsule ${capsule.id}:`, updateError);
        }
      }

      deliveryResults.push({
        capsule_id: capsule.id,
        recipient: capsule.recipient_email,
        success: deliveryResult.success,
        error: deliveryResult.success ? null : deliveryResult.error
      });
    }

    return new Response(
      JSON.stringify({ delivered: deliveryResults }),
      { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error in deliver-time-capsule function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
};

serve(handler);
