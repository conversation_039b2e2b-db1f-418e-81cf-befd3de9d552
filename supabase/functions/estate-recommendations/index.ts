
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { assets, context } = await req.json();
    const apiKey = Deno.env.get('DEEPSEEK_API_KEY');

    if (!apiKey) {
      throw new Error('DEEPSEEK_API_KEY is not set in the environment variables');
    }

    console.log('Generating estate recommendations for assets:', assets);

    // Create a prompt based on the user's assets and context
    const prompt = `
Based on the following estate assets:
${assets.map(a => `- ${a.name} (${a.category}): ${a.value}`).join('\n')}

${context || 'Please provide recommendations for estate planning.'}

Provide 3-5 specific recommendations for estate planning, considering tax implications,
asset protection, and beneficiary considerations. Format the response as a JSON array
of recommendation objects, each with a "title" and "description" field.
    `;

    // Call DeepSeek API
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are an estate planning expert assistant that provides concise, actionable recommendations.' },
          { role: 'user', content: prompt }
        ],
        response_format: { type: 'json_object' },
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('DeepSeek API error:', errorData);
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const recommendations = JSON.parse(data.choices[0].message.content);

    console.log('Generated recommendations:', recommendations);

    return new Response(JSON.stringify({ recommendations }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in estate-recommendations function:', error);
    return new Response(JSON.stringify({
      error: error.message,
      message: 'Failed to generate estate recommendations'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
